{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/loader.tsx"], "sourcesContent": ["const Loader = () => {\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center bg-white z-50\">\r\n      <div className=\"relative inline-flex items-center justify-center p-8\">\r\n        <img\r\n          src=\"/icons/logo.png\"\r\n          alt=\"Logo\"\r\n          className=\"w-32 h-32 sm:w-24 sm:h-24 lg:w-32 lg:h-32 object-contain z-10\"\r\n        />\r\n        <div\r\n          className=\"absolute rounded-full border-8 border-t-transparent animate-spin\"\r\n          style={{\r\n            width: \"120%\",\r\n            height: \"120%\",\r\n            borderColor: \"rgba(239, 68, 68, 0.2)\",\r\n            borderTopColor: \"rgb(239, 68, 68)\",\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loader;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,KAAI;oBACJ,KAAI;oBACJ,WAAU;;;;;;8BAEZ,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,aAAa;wBACb,gBAAgB;oBAClB;;;;;;;;;;;;;;;;;AAKV;uCAEe"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/onboarding/loading.tsx"], "sourcesContent": ["import Loader from \"@/app/components/student/loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <Loader/>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC,uIAAA,CAAA,UAAM;;;;;AAEX"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}