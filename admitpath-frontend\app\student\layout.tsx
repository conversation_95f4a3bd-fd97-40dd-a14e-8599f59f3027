"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useProfile } from "@/app/hooks/student/useProfile";

export default function StudentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { userInfo, fetchUserInfo } = useProfile();

  useEffect(() => {
    if (!localStorage.getItem("access_token")) {
      router.replace("/auth/login");
      return;
    }
    fetchUserInfo();
  }, [fetchUserInfo]);

  useEffect(() => {
    if (userInfo) {
      // If not a student, redirect to counselor dashboard
      if (userInfo.userType !== "student") {
        router.replace("/counselor/dashboard");
        return;
      }

      const isOnboardingPath = pathname?.startsWith("/student/onboarding");
      const isDashboardPath = pathname?.startsWith("/student/dashboard");

      // If profile is not complete and user is trying to access dashboard
      if (!userInfo.isProfileComplete && isDashboardPath) {
        router.replace("/student/onboarding");
        return;
      }

      // If profile is complete and user is trying to access onboarding
      if (userInfo.isProfileComplete && isOnboardingPath) {
        router.replace("/student/dashboard");
        return;
      }
    }
  }, [userInfo, router, pathname]);

  return children;
}
