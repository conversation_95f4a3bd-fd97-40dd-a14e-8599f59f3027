{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/passwordPages/verificationCard/timer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nexport interface TimerProps {\r\n  seconds: number;\r\n  onComplete: () => void;\r\n}\r\n\r\nexport const Timer: React.FC<TimerProps> = ({ seconds, onComplete }) => {\r\n  const [timeLeft, setTimeLeft] = React.useState(seconds);\r\n\r\n  React.useEffect(() => {\r\n    // Reset timer when seconds prop changes\r\n    setTimeLeft(seconds);\r\n  }, [seconds]);\r\n\r\n  React.useEffect(() => {\r\n    if (!timeLeft) {\r\n      onComplete();\r\n      return;\r\n    }\r\n\r\n    const timer = setInterval(() => {\r\n      setTimeLeft((prev) => {\r\n        if (prev <= 0) {\r\n          clearInterval(timer);\r\n          return 0;\r\n        }\r\n        return prev - 1;\r\n      });\r\n    }, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [timeLeft, onComplete]);\r\n\r\n  return (\r\n    <div className=\"flex gap-1 text-sm\">\r\n      <span className=\"text-gray-600\">Resend code in</span>\r\n      <span className=\"text-blue-600\">\r\n        0:{timeLeft.toString().padStart(2, \"0\")}\r\n      </span>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,MAAM,QAA8B,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE;;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,8JAAM,QAAQ,CAAC;IAE/C,8JAAM,SAAS;2BAAC;YACd,wCAAwC;YACxC,YAAY;QACd;0BAAG;QAAC;KAAQ;IAEZ,8JAAM,SAAS;2BAAC;YACd,IAAI,CAAC,UAAU;gBACb;gBACA;YACF;YAEA,MAAM,QAAQ;yCAAY;oBACxB;iDAAY,CAAC;4BACX,IAAI,QAAQ,GAAG;gCACb,cAAc;gCACd,OAAO;4BACT;4BACA,OAAO,OAAO;wBAChB;;gBACF;wCAAG;YAEH;mCAAO,IAAM,cAAc;;QAC7B;0BAAG;QAAC;QAAU;KAAW;IAEzB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;0BAChC,6LAAC;gBAAK,WAAU;;oBAAgB;oBAC3B,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;AAI3C;GAnCa;KAAA"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/passwordPages/verificationCard/verificationInput.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nexport interface VerificationInputProps {\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  isFocused: boolean;\r\n  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;\r\n  inputRef:\r\n    | React.Ref<HTMLInputElement>\r\n    | ((instance: HTMLInputElement | null) => void);\r\n}\r\n\r\nexport const VerificationInput: React.FC<VerificationInputProps> = ({\r\n  value,\r\n  onChange,\r\n  isFocused,\r\n  onKeyDown,\r\n  inputRef,\r\n}) => {\r\n  return (\r\n    <div className=\"flex items-center justify-center w-12 h-12 border border-gray-200 rounded-lg bg-white\">\r\n      <input\r\n        ref={inputRef}\r\n        type=\"text\"\r\n        inputMode=\"numeric\"\r\n        pattern=\"[0-9]*\"\r\n        maxLength={1}\r\n        value={value}\r\n        onChange={(e) => {\r\n          const val = e.target.value.replace(/[^0-9]/g, \"\");\r\n          onChange(val);\r\n        }}\r\n        onKeyDown={onKeyDown}\r\n        className=\"w-full h-full text-center text-xl font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n        aria-label=\"Verification code digit\"\r\n        autoFocus={isFocused}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,oBAAsD,CAAC,EAClE,KAAK,EACL,QAAQ,EACR,SAAS,EACT,SAAS,EACT,QAAQ,EACT;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,MAAK;YACL,WAAU;YACV,SAAQ;YACR,WAAW;YACX,OAAO;YACP,UAAU,CAAC;gBACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW;gBAC9C,SAAS;YACX;YACA,WAAW;YACX,WAAU;YACV,cAAW;YACX,WAAW;;;;;;;;;;;AAInB;KA3Ba"}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/passwordPages/verificationCard/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { Timer } from \"./timer\";\r\nimport { VerificationInput } from \"./verificationInput\";\r\n\r\ninterface VerificationCardProps {\r\n  email: string;\r\n  onVerify: (code: string) => void;\r\n  onCancel: () => void;\r\n  onResend: () => void;\r\n}\r\n\r\nexport const VerificationCard: React.FC<VerificationCardProps> = ({\r\n  email,\r\n  onVerify,\r\n  onCancel,\r\n  onResend,\r\n}) => {\r\n  const [code, setCode] = useState([\"\", \"\", \"\", \"\", \"\", \"\"]);\r\n  const [activeInput, setActiveInput] = useState(0);\r\n  const [isResendActive, setIsResendActive] = useState(false);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const [timerKey, setTimerKey] = useState(0); // Add this to force timer reset\r\n  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n    return () => setIsMounted(false);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    inputRefs.current = inputRefs.current.slice(0, 6);\r\n    if (activeInput >= 0 && activeInput < 6) {\r\n      inputRefs.current[activeInput]?.focus();\r\n    }\r\n  }, [activeInput]);\r\n\r\n  const handleInputChange = (index: number, value: string) => {\r\n    if (value.length <= 1) {\r\n      const newCode = [...code];\r\n      newCode[index] = value;\r\n      setCode(newCode);\r\n\r\n      if (value !== \"\" && index < 5) {\r\n        setActiveInput(index + 1);\r\n      }\r\n    } else {\r\n      // Handle pasting multiple digits\r\n      const digits = value.replace(/[^0-9]/g, \"\").split(\"\");\r\n      const newCode = [...code];\r\n      digits.forEach((digit, i) => {\r\n        if (index + i < 6) {\r\n          newCode[index + i] = digit;\r\n        }\r\n      });\r\n      setCode(newCode);\r\n      setActiveInput(Math.min(index + digits.length, 5));\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (\r\n    e: React.KeyboardEvent<HTMLInputElement>,\r\n    index: number\r\n  ) => {\r\n    if (e.key === \"Backspace\" && !code[index] && index > 0) {\r\n      setActiveInput(index - 1);\r\n    }\r\n  };\r\n\r\n  const handlePaste = (e: React.ClipboardEvent) => {\r\n    e.preventDefault();\r\n    const pastedData = e.clipboardData.getData(\"text\");\r\n    const numericValue = pastedData.replace(/[^0-9]/g, \"\");\r\n    const newCode = [...code];\r\n    for (let i = 0; i < Math.min(numericValue.length, 6); i++) {\r\n      newCode[i] = numericValue[i];\r\n    }\r\n    setCode(newCode);\r\n    setActiveInput(Math.min(numericValue.length, 6) - 1);\r\n  };\r\n\r\n  const handleTimerComplete = () => {\r\n    setIsResendActive(true);\r\n  };\r\n\r\n  const handleResend = async () => {\r\n    await onResend();\r\n    setIsResendActive(false);\r\n    setTimerKey((prev) => prev + 1); // Force timer to reset\r\n  };\r\n\r\n  if (!isMounted) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n      <div className=\"max-w-md w-full space-y-8\">\r\n        <div className=\"bg-white py-8 px-6 shadow-2xl rounded-2xl\">\r\n          <div className=\"flex flex-col items-center gap-8\">\r\n            {/* Icon */}\r\n            <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\">\r\n              <svg\r\n                className=\"w-8 h-8 text-blue-600\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                viewBox=\"0 0 24 24\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth=\"2\"\r\n                  d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\r\n                />\r\n              </svg>\r\n            </div>\r\n\r\n            {/* Header */}\r\n            <div className=\"text-center space-y-2\">\r\n              <h2 className=\"text-3xl font-bold text-gray-900\">\r\n                Verify your email\r\n              </h2>\r\n              <p className=\"text-sm text-gray-500\">\r\n                We sent a verification code to\r\n                <br />\r\n                <span className=\"font-medium text-gray-900\">{email}</span>\r\n              </p>\r\n            </div>\r\n\r\n            {/* Verification inputs */}\r\n            <div className=\"flex gap-2\" onPaste={handlePaste}>\r\n              {code.map((digit, idx) => (\r\n                <VerificationInput\r\n                  key={idx}\r\n                  value={digit}\r\n                  onChange={(value) => handleInputChange(idx, value)}\r\n                  onKeyDown={(e) => handleKeyDown(e, idx)}\r\n                  isFocused={activeInput === idx}\r\n                  inputRef={(el) => (inputRefs.current[idx] = el)}\r\n                />\r\n              ))}\r\n            </div>\r\n\r\n            {/* Timer */}\r\n            <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n              {!isResendActive && (\r\n                <Timer\r\n                  key={timerKey}\r\n                  seconds={60}\r\n                  onComplete={handleTimerComplete}\r\n                />\r\n              )}\r\n              {isResendActive && (\r\n                <button\r\n                  onClick={handleResend}\r\n                  className=\"text-blue-600 hover:text-blue-700 font-medium\"\r\n                >\r\n                  Resend code\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Buttons */}\r\n            <div className=\"flex justify-between gap-4 w-full mt-4\">\r\n              <Button\r\n                variant=\"secondary\"\r\n                onClick={onCancel}\r\n                className=\"flex-1 py-2.5\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                onClick={() => onVerify(code.join(\"\"))}\r\n                className=\"flex-1 py-2.5 bg-blue-600 hover:bg-blue-700\"\r\n              >\r\n                Verify\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcO,MAAM,mBAAoD,CAAC,EAChE,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACT;;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,gCAAgC;IAC7E,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA+B,EAAE;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,aAAa;YACb;8CAAO,IAAM,aAAa;;QAC5B;qCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,UAAU,OAAO,GAAG,UAAU,OAAO,CAAC,KAAK,CAAC,GAAG;YAC/C,IAAI,eAAe,KAAK,cAAc,GAAG;gBACvC,UAAU,OAAO,CAAC,YAAY,EAAE;YAClC;QACF;qCAAG;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI,MAAM,MAAM,IAAI,GAAG;YACrB,MAAM,UAAU;mBAAI;aAAK;YACzB,OAAO,CAAC,MAAM,GAAG;YACjB,QAAQ;YAER,IAAI,UAAU,MAAM,QAAQ,GAAG;gBAC7B,eAAe,QAAQ;YACzB;QACF,OAAO;YACL,iCAAiC;YACjC,MAAM,SAAS,MAAM,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC;YAClD,MAAM,UAAU;mBAAI;aAAK;YACzB,OAAO,OAAO,CAAC,CAAC,OAAO;gBACrB,IAAI,QAAQ,IAAI,GAAG;oBACjB,OAAO,CAAC,QAAQ,EAAE,GAAG;gBACvB;YACF;YACA,QAAQ;YACR,eAAe,KAAK,GAAG,CAAC,QAAQ,OAAO,MAAM,EAAE;QACjD;IACF;IAEA,MAAM,gBAAgB,CACpB,GACA;QAEA,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG;YACtD,eAAe,QAAQ;QACzB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,MAAM,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC;QAC3C,MAAM,eAAe,WAAW,OAAO,CAAC,WAAW;QACnD,MAAM,UAAU;eAAI;SAAK;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,aAAa,MAAM,EAAE,IAAI,IAAK;YACzD,OAAO,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE;QAC9B;QACA,QAAQ;QACR,eAAe,KAAK,GAAG,CAAC,aAAa,MAAM,EAAE,KAAK;IACpD;IAEA,MAAM,sBAAsB;QAC1B,kBAAkB;IACpB;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,kBAAkB;QAClB,YAAY,CAAC,OAAS,OAAO,IAAI,uBAAuB;IAC1D;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,OAAM;0CAEN,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAY;oCACZ,GAAE;;;;;;;;;;;;;;;;sCAMR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,6LAAC;oCAAE,WAAU;;wCAAwB;sDAEnC,6LAAC;;;;;sDACD,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAKjD,6LAAC;4BAAI,WAAU;4BAAa,SAAS;sCAClC,KAAK,GAAG,CAAC,CAAC,OAAO,oBAChB,6LAAC,uLAAA,CAAA,oBAAiB;oCAEhB,OAAO;oCACP,UAAU,CAAC,QAAU,kBAAkB,KAAK;oCAC5C,WAAW,CAAC,IAAM,cAAc,GAAG;oCACnC,WAAW,gBAAgB;oCAC3B,UAAU,CAAC,KAAQ,UAAU,OAAO,CAAC,IAAI,GAAG;mCALvC;;;;;;;;;;sCAWX,6LAAC;4BAAI,WAAU;;gCACZ,CAAC,gCACA,6LAAC,2KAAA,CAAA,QAAK;oCAEJ,SAAS;oCACT,YAAY;mCAFP;;;;;gCAKR,gCACC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,SAAS,KAAK,IAAI,CAAC;oCAClC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA5Ka;KAAA"}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/apiClient.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios from \"axios\";\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n    Accept: \"application/json\",\r\n  },\r\n  // withCredentials: true,\r\n});\r\n\r\n// Request interceptor to handle dynamic headers or logging\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem(\"access_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle common errors globally\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle specific error responses (e.g., 401 Unauthorized)\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        if (\r\n          window.location.pathname.startsWith(\"/student\") ||\r\n          window.location.pathname.startsWith(\"/counselor\") ||\r\n          localStorage.getItem(\"access_token\")\r\n        ) {\r\n          console.error(\"Unauthorized. Redirecting to login...\");\r\n          localStorage.removeItem(\"access_token\");\r\n          window.location.href = \"/auth/login\";\r\n        }\r\n      } else if (status >= 500) {\r\n        console.error(\r\n          \"Server error:\",\r\n          error.response.data.message || \"Internal Server Error\"\r\n        );\r\n      }\r\n    } else {\r\n      console.error(\"Network error:\", error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAEA;AAGW;AALX;;AAIA,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,6DAAwC;IACjD,SAAS;QACP,gBAAgB;QAChB,QAAQ;IACV;AAEF;AAEA,2DAA2D;AAC3D,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wDAAwD;AACxD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,2DAA2D;IAC3D,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,IACE,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,eACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,aAAa,OAAO,CAAC,iBACrB;gBACA,QAAQ,KAAK,CAAC;gBACd,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAEnC;IACF,OAAO;QACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;IAC/C;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa"}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/auth.ts"], "sourcesContent": ["import { toast } from 'react-toastify';\r\nimport apiClient from '@lib/apiClient';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;\r\n\r\ninterface AuthError extends Error {\r\n  isAuthError?: boolean;\r\n}\r\n\r\nexport const handleGoogleLogin = async (token: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/google', \r\n      userType \r\n        ? { token, user_type: userType }  // Signup case\r\n        : { token }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('Google auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const handleLinkedInLogin = async (code: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/linkedin',\r\n      userType \r\n        ? { code, user_type: userType }  // Signup case\r\n        : { code }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('LinkedIn auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const initiateLinkedInLogin = (userType?: string) => {\r\n  const LINKEDIN_CLIENT_ID = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;\r\n  const LINKEDIN_REDIRECT_URI = process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI;\r\n  const scope = 'openid profile email';\r\n  \r\n  // Only include state (userType) for signup flow\r\n  const stateParam = userType ? `&state=${userType}` : '';\r\n  const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${LINKEDIN_REDIRECT_URI}${stateParam}&scope=${scope}`;\r\n  \r\n  window.location.href = url;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AAEgB;;AAAhB,MAAM;AAMC,MAAM,oBAAoB,OAAO,OAAe;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBACpC,WACI;YAAE;YAAO,WAAW;QAAS,EAAG,cAAc;WAC9C;YAAE;QAAM,EAAG,aAAa;;QAG9B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,MAAc;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,kBACpC,WACI;YAAE;YAAM,WAAW;QAAS,EAAG,cAAc;WAC7C;YAAE;QAAK,EAAG,aAAa;;QAG7B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,qBAAqB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,8BAA8B;IACrE,MAAM,wBAAwB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iCAAiC;IAC3E,MAAM,QAAQ;IAEd,gDAAgD;IAChD,MAAM,aAAa,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG;IACrD,MAAM,MAAM,CAAC,6EAA6E,EAAE,mBAAmB,cAAc,EAAE,wBAAwB,WAAW,OAAO,EAAE,OAAO;IAElL,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB"}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  handleGoogleLogin,\r\n  handleLinkedInLogin,\r\n  initiateLinkedInLogin,\r\n} from \"@/app/utils/auth\";\r\n\r\ninterface User {\r\n  avatar?: string;\r\n  firstName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface AuthState {\r\n  token: string | null;\r\n  user: User | null;\r\n  userType: string | null;\r\n  loading: boolean;\r\n  login: (username: string, password: string) => Promise<void>;\r\n  signup: (data: SignupData) => Promise<void>;\r\n  verifyEmail: (email: string, code: string) => Promise<void>;\r\n  forgotPassword: (email: string) => Promise<void>;\r\n  resetPassword: (\r\n    email: string,\r\n    code: string,\r\n    newPassword: string\r\n  ) => Promise<void>;\r\n  logout: () => void;\r\n  signupData: Partial<SignupData> | null;\r\n  initiateSignup: (\r\n    data: { first_name: string; last_name: string; email: string },\r\n    userType: \"student\" | \"counselor\"\r\n  ) => Promise<void>;\r\n  resetSignupData: () => void;\r\n  completeSignup: (password: string) => Promise<string>;\r\n  resendVerificationCode: (email: string) => Promise<void>;\r\n  verifySignupCode: (email: string, code: string) => Promise<void>;\r\n  googleAuth: (\r\n    token: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  linkedinAuth: (\r\n    code: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => void;\r\n  isAuthenticated: boolean;\r\n  auth: () => boolean;\r\n}\r\n\r\ninterface SignupData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  userType: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const useAuth = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      token: null as string | null,\r\n      user: null as User | null,\r\n      userType: null as string | null,\r\n      loading: false,\r\n      signupData: null,\r\n      isAuthenticated: false,\r\n\r\n      auth: () => {\r\n        const token = localStorage.getItem(\"access_token\");\r\n        const isAuthenticated = !!token;\r\n        set({ isAuthenticated });\r\n        return isAuthenticated;\r\n      },\r\n\r\n      login: async (username, password) => {\r\n        try {\r\n          set({ loading: true });\r\n          const formData = new URLSearchParams();\r\n          formData.append(\"username\", username);\r\n          formData.append(\"password\", password);\r\n\r\n          const response = await apiClient.post(\"/auth/signin\", formData, {\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n            },\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n          });\r\n\r\n          toast.success(\"Logged in successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Login failed\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      signup: async (data) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\r\n            `/auth/signup/${data.userType}`,\r\n            {\r\n              email: data.email,\r\n              password: data.password,\r\n              first_name: data.first_name,\r\n              last_name: data.last_name,\r\n            }\r\n          );\r\n\r\n          if (response.data.id) {\r\n            localStorage.setItem(\"email\", data.email);\r\n            toast.success(\"Verification code sent to your email!\");\r\n          }\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Signup failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifyEmail: async (email, code) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/verify-code\", { email, code });\r\n          toast.success(\"Code verified successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Verification failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      forgotPassword: async (email) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/forgot-password\", { email });\r\n          toast.success(\"Reset code sent to your email!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg ||\r\n              \"Failed to send reset code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetPassword: async (email, code, newPassword) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/reset-password\", {\r\n            email,\r\n            code,\r\n            new_password: newPassword,\r\n          });\r\n          toast.success(\"Password reset successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Password reset failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      logout: () => {\r\n        localStorage.removeItem(\"access_token\");\r\n        set({ token: null, userType: null, isAuthenticated: false });\r\n      },\r\n\r\n      initiateSignup: async (data, userType = \"student\") => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/initiate\", {\r\n            ...data,\r\n            user_type: userType,\r\n          });\r\n          set({ signupData: { ...data, userType } });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to initiate signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetSignupData: () => {\r\n        set({ signupData: null });\r\n      },\r\n\r\n      completeSignup: async (password: string) => {\r\n        const { signupData } = get();\r\n        if (!signupData?.email) {\r\n          toast.error(\"Missing signup data\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/complete\", {\r\n            email: signupData.email,\r\n            password,\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n\r\n          // Store token and update auth state\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n            signupData: null,\r\n          });\r\n\r\n          toast.success(\"Account created successfully!\");\r\n\r\n          // Return user type so pages can redirect appropriately\r\n          return user_type;\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to complete signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resendVerificationCode: async (email: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/resend\", {\r\n            email,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Failed to resend code\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifySignupCode: async (email: string, code: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/verify\", {\r\n            email,\r\n            code,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Invalid verification code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      googleAuth: async (\r\n        token: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleGoogleLogin(token, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with Google successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with Google successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/student/dashboard\";\r\n              } else {\r\n                window.location.href = \"/student/onboarding\";\r\n              }\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      linkedinAuth: async (\r\n        code: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleLinkedInLogin(code, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with LinkedIn successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with LinkedIn successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/student/dashboard\";\r\n              } else {\r\n                window.location.href = \"/student/onboarding\";\r\n              }\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => {\r\n        initiateLinkedInLogin(userType);\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AACA;AACA;AAJA;AACA;AAHA;;;;;;AA+DO,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,iBAAiB;QAEjB,MAAM;YACJ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,kBAAkB,CAAC,CAAC;YAC1B,IAAI;gBAAE;YAAgB;YACtB,OAAO;QACT;QAEA,OAAO,OAAO,UAAU;YACtB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,YAAY;gBAC5B,SAAS,MAAM,CAAC,YAAY;gBAE5B,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,UAAU;oBAC9D,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAC7D,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;gBACnB;gBAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ,OAAO;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,aAAa,EAAE,KAAK,QAAQ,EAAE,EAC/B;oBACE,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,WAAW,KAAK,SAAS;gBAC3B;gBAGF,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACpB,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,aAAa,OAAO,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,qBAAqB;oBAAE;oBAAO;gBAAK;gBACxD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAAE;gBAAM;gBACtD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OACjC;gBAEJ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,OAAO,MAAM;YACjC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,wBAAwB;oBAC3C;oBACA;oBACA,cAAc;gBAChB;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;YACxB,IAAI;gBAAE,OAAO;gBAAM,UAAU;gBAAM,iBAAiB;YAAM;QAC5D;QAEA,gBAAgB,OAAO,MAAM,WAAW,SAAS;YAC/C,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,GAAG,IAAI;oBACP,WAAW;gBACb;gBACA,IAAI;oBAAE,YAAY;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAAE;gBACxC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBAAE,YAAY;YAAK;QACzB;QAEA,gBAAgB,OAAO;YACrB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI,CAAC,YAAY,OAAO;gBACtB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,OAAO,WAAW,KAAK;oBACvB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAE7D,oCAAoC;gBACpC,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;oBACjB,YAAY;gBACd;gBAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,uDAAuD;gBACvD,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;gBACF;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO,OAAe;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;oBACA;gBACF;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OACV,OACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBAEhD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,IAAI,SAAS,iBAAiB,EAAE;4BAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,cAAc,OACZ,MACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;gBAEjD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,IAAI,SAAS,iBAAiB,EAAE;4BAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,sBAAsB,CAAC;YACrB,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;IACF,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/verification/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\nimport { VerificationCard } from \"../passwordPages/verificationCard\";\r\nimport { useAuth } from \"@hooks/useAuth\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface VerificationFormProps {\r\n  onVerify: (data: { email: string; code: string }) => Promise<void>;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport const VerificationForm: FC<VerificationFormProps> = ({\r\n  onVerify,\r\n  isLoading,\r\n}) => {\r\n  const router = useRouter();\r\n  const { resendVerificationCode, signupData } = useAuth();\r\n\r\n  const handleVerify = async (code: string) => {\r\n    let email = \"\";\r\n    if (typeof window !== \"undefined\") {\r\n      email = localStorage.getItem(\"email\") || \"\";\r\n    }\r\n    await onVerify({ email, code });\r\n  };\r\n\r\n  const handleResend = async () => {\r\n    const email = localStorage.getItem(\"email\") || \"\";\r\n    if (email) {\r\n      try {\r\n        await resendVerificationCode(email);\r\n      } catch (error) {\r\n        // Error handled by hook\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    // Clear any stored signup data\r\n    localStorage.removeItem(\"email\");\r\n    // Reset signup data in auth store\r\n    \r\n    // Redirect based on user type\r\n    const redirectPath = signupData?.userType === \"counselor\" \r\n      ? \"/auth/signup/counselor\"\r\n      : \"/auth/signup\";\r\n    router.push(redirectPath);\r\n  };\r\n\r\n  return (\r\n    <VerificationCard\r\n      email={typeof window !== \"undefined\" ? localStorage.getItem(\"email\") || \"\" : \"\"}\r\n      onVerify={handleVerify}\r\n      onCancel={handleCancel}\r\n      onResend={handleResend}\r\n    />\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAYO,MAAM,mBAA8C,CAAC,EAC1D,QAAQ,EACR,SAAS,EACV;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,sBAAsB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAErD,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ;QACZ,wCAAmC;YACjC,QAAQ,aAAa,OAAO,CAAC,YAAY;QAC3C;QACA,MAAM,SAAS;YAAE;YAAO;QAAK;IAC/B;IAEA,MAAM,eAAe;QACnB,MAAM,QAAQ,aAAa,OAAO,CAAC,YAAY;QAC/C,IAAI,OAAO;YACT,IAAI;gBACF,MAAM,uBAAuB;YAC/B,EAAE,OAAO,OAAO;YACd,wBAAwB;YAC1B;QACF;IACF;IAEA,MAAM,eAAe;QACnB,+BAA+B;QAC/B,aAAa,UAAU,CAAC;QACxB,kCAAkC;QAElC,8BAA8B;QAC9B,MAAM,eAAe,YAAY,aAAa,cAC1C,2BACA;QACJ,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,2KAAA,CAAA,mBAAgB;QACf,OAAO,uCAAgC,aAAa,OAAO,CAAC,YAAY;QACxE,UAAU;QACV,UAAU;QACV,UAAU;;;;;;AAGhB;GA9Ca;;QAII,qIAAA,CAAA,YAAS;QACuB,0HAAA,CAAA,UAAO;;;KAL3C"}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/auth/verify/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { VerificationForm } from \"@components/auth/verification\";\r\nimport { useAuth } from \"@hooks/useAuth\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nexport default function VerifyPage() {\r\n  const router = useRouter();\r\n  const { verifySignupCode, loading, signupData } = useAuth();\r\n\r\n  const handleVerify = async (data: { email: string; code: string }) => {\r\n    try {\r\n      await verifySignupCode(data.email, data.code);\r\n      // Redirect based on user type\r\n      const redirectPath = signupData?.userType === \"counselor\" \r\n        ? \"/auth/signup/counselor/set-password\"\r\n        : \"/auth/signup/set-password\";\r\n      router.replace(redirectPath);\r\n    } catch (error) {\r\n      // Error is handled by the hook\r\n    }\r\n  };\r\n\r\n  return <VerificationForm onVerify={handleVerify} isLoading={loading} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAExD,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,iBAAiB,KAAK,KAAK,EAAE,KAAK,IAAI;YAC5C,8BAA8B;YAC9B,MAAM,eAAe,YAAY,aAAa,cAC1C,wCACA;YACJ,OAAO,OAAO,CAAC;QACjB,EAAE,OAAO,OAAO;QACd,+BAA+B;QACjC;IACF;IAEA,qBAAO,6LAAC,sJAAA,CAAA,mBAAgB;QAAC,UAAU;QAAc,WAAW;;;;;;AAC9D;GAlBwB;;QACP,qIAAA,CAAA,YAAS;QAC0B,0HAAA,CAAA,UAAO;;;KAFnC"}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}