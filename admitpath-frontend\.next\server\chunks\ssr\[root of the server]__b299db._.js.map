{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/apiClient.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios from \"axios\";\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n    Accept: \"application/json\",\r\n  },\r\n  // withCredentials: true,\r\n});\r\n\r\n// Request interceptor to handle dynamic headers or logging\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem(\"access_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle common errors globally\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle specific error responses (e.g., 401 Unauthorized)\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        if (\r\n          window.location.pathname.startsWith(\"/student\") ||\r\n          window.location.pathname.startsWith(\"/counselor\") ||\r\n          localStorage.getItem(\"access_token\")\r\n        ) {\r\n          console.error(\"Unauthorized. Redirecting to login...\");\r\n          localStorage.removeItem(\"access_token\");\r\n          window.location.href = \"/auth/login\";\r\n        }\r\n      } else if (status >= 500) {\r\n        console.error(\r\n          \"Server error:\",\r\n          error.response.data.message || \"Internal Server Error\"\r\n        );\r\n      }\r\n    } else {\r\n      console.error(\"Network error:\", error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,6DAAwC;IACjD,SAAS;QACP,gBAAgB;QAChB,QAAQ;IACV;AAEF;AAEA,2DAA2D;AAC3D,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wDAAwD;AACxD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,2DAA2D;IAC3D,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,IACE,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,eACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,aAAa,OAAO,CAAC,iBACrB;gBACA,QAAQ,KAAK,CAAC;gBACd,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAEnC;IACF,OAAO;QACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;IAC/C;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa"}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useProfile.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  EducationInfo,\r\n  PersonalInfo,\r\n  ProfileState,\r\n} from \"@/app/types/student/profile\";\r\n\r\n// Create a store for managing student profile state\r\nexport const useProfile = create<ProfileState>((set, get) => ({\r\n  loading: false,\r\n  error: null,\r\n  currentStep: null,\r\n  userInfo: null,\r\n  personalInfo: null,\r\n  educationInfo: null,\r\n  servicesInfo: null,\r\n  setCurrentStep: (step) => set({ currentStep: step }),\r\n\r\n  fetchUserInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/user/me?timezone=${Intl.DateTimeFormat().resolvedOptions().timeZone}`\r\n      );\r\n      set({ userInfo: response.data, loading: false });\r\n    } catch (error) {\r\n      set({ error: \"Failed to fetch user info\", loading: false });\r\n    }\r\n  },\r\n\r\n  fetchPersonalInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/student/profile/personal-info\");\r\n      set({ personalInfo: response.data, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch personal info\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchEducationalBackground: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        \"/student/profile/educational-background\"\r\n      );\r\n      set({ educationInfo: response.data.items, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail ||\r\n          \"Failed to fetch educational background\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchServicesInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/student/profile/services-info\");\r\n      set({ servicesInfo: response.data, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch Services info\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  figureCurrentStep: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const {\r\n        fetchPersonalInfo,\r\n        fetchEducationalBackground,\r\n        // fetchServicesInfo,\r\n      } = get();\r\n\r\n      await Promise.all([\r\n        fetchPersonalInfo(),\r\n        fetchEducationalBackground(),\r\n        // fetchServicesInfo(),\r\n      ]);\r\n\r\n      // const { personalInfo, educationInfo, servicesInfo } = get();\r\n      const { personalInfo, educationInfo } = get();\r\n\r\n      let currentStep = 1;\r\n      if (personalInfo) {\r\n        currentStep = 2;\r\n        if (educationInfo && educationInfo.length > 0) {\r\n          currentStep = 3;\r\n          // if (servicesInfo) {\r\n          //   currentStep = 4;\r\n          // }\r\n        }\r\n      }\r\n\r\n      set({ currentStep, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail || \"Failed to determine current step\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  submitPersonalInfo: async (data: PersonalInfo) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const { personalInfo } = get();\r\n      // If data exists, use PUT, else use POST\r\n      const method = personalInfo ? \"put\" : \"post\";\r\n      await apiClient[method](\"/student/profile/personal-info\", data);\r\n      set({ personalInfo: data, currentStep: 2 });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  submitEducationInfo: async (data: EducationInfo[]) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const results = await Promise.all(\r\n        data.map(async (item) => {\r\n          const payload = {\r\n            ...item,\r\n            end_date: item.is_current ? undefined : item.end_date,\r\n          };\r\n\r\n          if (item.id) {\r\n            // Update existing entry\r\n            const response = await apiClient.put(\r\n              `/student/profile/educational-background/${item.id}`,\r\n              payload\r\n            );\r\n            return response.data;\r\n          } else {\r\n            // Create new entry\r\n            const response = await apiClient.post(\r\n              \"/student/profile/educational-background\",\r\n              payload\r\n            );\r\n            return response.data;\r\n          }\r\n        })\r\n      );\r\n\r\n      set({\r\n        educationInfo: results,\r\n        currentStep: Math.max(get().currentStep || 0, 3),\r\n        loading: false,\r\n      });\r\n\r\n      return results;\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail || \"Failed to submit education info\",\r\n        loading: false,\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  updateEducationInfo: async (id: number, data: Partial<EducationInfo>) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.put(\r\n        `/student/profile/educational-background/${id}`,\r\n        data\r\n      );\r\n\r\n      // Update the specific education item in the list\r\n      set((state) => ({\r\n        educationInfo:\r\n          state.educationInfo?.map((item) =>\r\n            item.id === id ? response.data : item\r\n          ) || null,\r\n      }));\r\n      toast.success(\"Education info updated successfully\");\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  deleteEducationInfo: async (id: number) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.delete(`/student/profile/educational-background/${id}`);\r\n\r\n      // Remove the education item from the list\r\n      set((state) => ({\r\n        educationInfo:\r\n          state.educationInfo?.filter((item) => item.id !== id) || null,\r\n      }));\r\n      toast.success(\"Education info deleted successfully\");\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  submitExpectedServices: async (data) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const { servicesInfo } = get();\r\n      const method = servicesInfo ? \"put\" : \"post\";\r\n      await apiClient[method](\"/student/profile/expected-services\", data);\r\n      set({ servicesInfo: data, currentStep: 4 });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  updateprofile_picture_url: (url: string | null) => {\r\n    const currentUserInfo = get().userInfo;\r\n    if (currentUserInfo) {\r\n      set({ userInfo: { ...currentUserInfo, profile_picture_url: url } });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AACA;AAFA;AAFA;;;;AAYO,MAAM,aAAa,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QAC5D,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAElD,eAAe;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAAE;gBAEzE,IAAI;oBAAE,UAAU,SAAS,IAAI;oBAAE,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;oBAA6B,SAAS;gBAAM;YAC3D;QACF;QAEA,mBAAmB;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;YACpD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,4BAA4B;YAC1B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC;gBAEF,IAAI;oBAAE,eAAe,SAAS,IAAI,CAAC,KAAK;oBAAE,SAAS;gBAAM;YAC3D,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACF,SAAS;gBACX;YACF;QACF;QAEA,mBAAmB;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;YACpD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,mBAAmB;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EACJ,iBAAiB,EACjB,0BAA0B,EAE3B,GAAG;gBAEJ,MAAM,QAAQ,GAAG,CAAC;oBAChB;oBACA;iBAED;gBAED,+DAA+D;gBAC/D,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;gBAExC,IAAI,cAAc;gBAClB,IAAI,cAAc;oBAChB,cAAc;oBACd,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;wBAC7C,cAAc;oBACd,sBAAsB;oBACtB,qBAAqB;oBACrB,IAAI;oBACN;gBACF;gBAEA,IAAI;oBAAE;oBAAa,SAAS;gBAAM;YACpC,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UAAU;oBAClC,SAAS;gBACX;YACF;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,yCAAyC;gBACzC,MAAM,SAAS,eAAe,QAAQ;gBACtC,MAAM,gHAAA,CAAA,UAAS,CAAC,OAAO,CAAC,kCAAkC;gBAC1D,IAAI;oBAAE,cAAc;oBAAM,aAAa;gBAAE;YAC3C,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,qBAAqB,OAAO;YAC1B,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,KAAK,GAAG,CAAC,OAAO;oBACd,MAAM,UAAU;wBACd,GAAG,IAAI;wBACP,UAAU,KAAK,UAAU,GAAG,YAAY,KAAK,QAAQ;oBACvD;oBAEA,IAAI,KAAK,EAAE,EAAE;wBACX,wBAAwB;wBACxB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,wCAAwC,EAAE,KAAK,EAAE,EAAE,EACpD;wBAEF,OAAO,SAAS,IAAI;oBACtB,OAAO;wBACL,mBAAmB;wBACnB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,2CACA;wBAEF,OAAO,SAAS,IAAI;oBACtB;gBACF;gBAGF,IAAI;oBACF,eAAe;oBACf,aAAa,KAAK,GAAG,CAAC,MAAM,WAAW,IAAI,GAAG;oBAC9C,SAAS;gBACX;gBAEA,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UAAU;oBAClC,SAAS;gBACX;gBACA,MAAM;YACR;QACF;QAEA,qBAAqB,OAAO,IAAY;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,wCAAwC,EAAE,IAAI,EAC/C;gBAGF,iDAAiD;gBACjD,IAAI,CAAC,QAAU,CAAC;wBACd,eACE,MAAM,aAAa,EAAE,IAAI,CAAC,OACxB,KAAK,EAAE,KAAK,KAAK,SAAS,IAAI,GAAG,SAC9B;oBACT,CAAC;gBACD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,qBAAqB,OAAO;YAC1B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,wCAAwC,EAAE,IAAI;gBAEtE,0CAA0C;gBAC1C,IAAI,CAAC,QAAU,CAAC;wBACd,eACE,MAAM,aAAa,EAAE,OAAO,CAAC,OAAS,KAAK,EAAE,KAAK,OAAO;oBAC7D,CAAC;gBACD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,SAAS,eAAe,QAAQ;gBACtC,MAAM,gHAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sCAAsC;gBAC9D,IAAI;oBAAE,cAAc;oBAAM,aAAa;gBAAE;YAC3C,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,2BAA2B,CAAC;YAC1B,MAAM,kBAAkB,MAAM,QAAQ;YACtC,IAAI,iBAAiB;gBACnB,IAAI;oBAAE,UAAU;wBAAE,GAAG,eAAe;wBAAE,qBAAqB;oBAAI;gBAAE;YACnE;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\n\r\nexport default function StudentLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { userInfo, fetchUserInfo } = useProfile();\r\n\r\n  useEffect(() => {\r\n    if (!localStorage.getItem(\"access_token\")) {\r\n      router.replace(\"/auth/login\");\r\n      return;\r\n    }\r\n    fetchUserInfo();\r\n  }, [fetchUserInfo]);\r\n\r\n  useEffect(() => {\r\n    if (userInfo) {\r\n      // If not a student, redirect to counselor dashboard\r\n      if (userInfo.userType !== \"student\") {\r\n        router.replace(\"/counselor/dashboard\");\r\n        return;\r\n      }\r\n\r\n      const isOnboardingPath = pathname?.startsWith(\"/student/onboarding\");\r\n      const isDashboardPath = pathname?.startsWith(\"/student/dashboard\");\r\n\r\n      // If profile is not complete and user is trying to access dashboard\r\n      if (!userInfo.isProfileComplete && isDashboardPath) {\r\n        router.replace(\"/student/onboarding\");\r\n        return;\r\n      }\r\n\r\n      // If profile is complete and user is trying to access onboarding\r\n      if (userInfo.isProfileComplete && isOnboardingPath) {\r\n        router.replace(\"/student/dashboard\");\r\n        return;\r\n      }\r\n    }\r\n  }, [userInfo, router, pathname]);\r\n\r\n  return children;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAMe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,CAAC,iBAAiB;YACzC,OAAO,OAAO,CAAC;YACf;QACF;QACA;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,oDAAoD;YACpD,IAAI,SAAS,QAAQ,KAAK,WAAW;gBACnC,OAAO,OAAO,CAAC;gBACf;YACF;YAEA,MAAM,mBAAmB,UAAU,WAAW;YAC9C,MAAM,kBAAkB,UAAU,WAAW;YAE7C,oEAAoE;YACpE,IAAI,CAAC,SAAS,iBAAiB,IAAI,iBAAiB;gBAClD,OAAO,OAAO,CAAC;gBACf;YACF;YAEA,iEAAiE;YACjE,IAAI,SAAS,iBAAiB,IAAI,kBAAkB;gBAClD,OAAO,OAAO,CAAC;gBACf;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,OAAO;AACT"}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}