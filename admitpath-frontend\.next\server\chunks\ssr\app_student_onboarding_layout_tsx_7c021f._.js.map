{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/onboarding/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport React, { ReactNode, useEffect } from \"react\";\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\nexport default function Layout({ children }: LayoutProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { userInfo, fetchUserInfo } = useProfile();\r\n\r\n  useEffect(() => {\r\n    if (!localStorage.getItem(\"access_token\")) {\r\n      router.replace(\"/auth/login\");\r\n      return;\r\n    }\r\n    fetchUserInfo();\r\n  }, [fetchUserInfo]);\r\n\r\n  useEffect(() => {\r\n    if (userInfo) {\r\n      // If not a student, redirect to counselor dashboard\r\n      if (userInfo.userType !== \"student\") {\r\n        router.replace(\"/counselor/dashboard\");\r\n        return;\r\n      }\r\n\r\n      // If profile is complete and user is trying to access onboarding\r\n      if (userInfo.isProfileComplete) {\r\n        router.replace(\"/student/dashboard\");\r\n        return;\r\n      }\r\n    }\r\n  }, [userInfo, router, pathname]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <div\r\n        className=\"fixed top-0 left-0 right-0 h-[400px] z-0\"\r\n        style={{\r\n          backgroundImage: 'url(\"/images/onboarding-banner.png\")',\r\n          backgroundSize: \"cover\",\r\n          backgroundPosition: \"center\",\r\n        }}\r\n      >\r\n        {/* <div className=\"absolute inset-0 bg-blue-600/90\" /> */}\r\n      </div>\r\n\r\n      <div className=\"relative z-10\">\r\n        {/* <Navbar /> */}\r\n        <main className=\"container mx-auto px-4 sm:px-8 md:mt-16 mt-8 pt-10 md:pt-20 xl:pt-32\">\r\n          {children}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AASe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,CAAC,iBAAiB;YACzC,OAAO,OAAO,CAAC;YACf;QACF;QACA;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,oDAAoD;YACpD,IAAI,SAAS,QAAQ,KAAK,WAAW;gBACnC,OAAO,OAAO,CAAC;gBACf;YACF;YAEA,iEAAiE;YACjE,IAAI,SAAS,iBAAiB,EAAE;gBAC9B,OAAO,OAAO,CAAC;gBACf;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;oBAChB,oBAAoB;gBACtB;;;;;;0BAKF,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}