{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/countries.ts"], "sourcesContent": ["export const countries = [\r\n  { name: \"Afghanistan\", code: \"AF\" },\r\n  { name: \"Åland Islands\", code: \"AX\" },\r\n  { name: \"Albania\", code: \"AL\" },\r\n  { name: \"Algeria\", code: \"DZ\" },\r\n  { name: \"American Samoa\", code: \"AS\" },\r\n  { name: \"Andorra\", code: \"AD\" },\r\n  { name: \"Angola\", code: \"AO\" },\r\n  { name: \"Ang<PERSON><PERSON>\", code: \"AI\" },\r\n  { name: \"Antarctica\", code: \"AQ\" },\r\n  { name: \"Antigua and Barbuda\", code: \"AG\" },\r\n  { name: \"Argentina\", code: \"AR\" },\r\n  { name: \"Armenia\", code: \"AM\" },\r\n  { name: \"Aruba\", code: \"AW\" },\r\n  { name: \"Australia\", code: \"AU\" },\r\n  { name: \"Austria\", code: \"AT\" },\r\n  { name: \"Azerbaijan\", code: \"AZ\" },\r\n  { name: \"Bahamas\", code: \"BS\" },\r\n  { name: \"Bahrain\", code: \"BH\" },\r\n  { name: \"Bangladesh\", code: \"BD\" },\r\n  { name: \"Barbados\", code: \"BB\" },\r\n  { name: \"Belarus\", code: \"BY\" },\r\n  { name: \"Belgium\", code: \"BE\" },\r\n  { name: \"Belize\", code: \"BZ\" },\r\n  { name: \"Benin\", code: \"BJ\" },\r\n  { name: \"Bermuda\", code: \"BM\" },\r\n  { name: \"Bhutan\", code: \"BT\" },\r\n  { name: \"Bolivia\", code: \"BO\" },\r\n  { name: \"Bosnia and Herzegovina\", code: \"BA\" },\r\n  { name: \"Botswana\", code: \"BW\" },\r\n  { name: \"Bouvet Island\", code: \"BV\" },\r\n  { name: \"Brazil\", code: \"BR\" },\r\n  { name: \"British Indian Ocean Territory\", code: \"IO\" },\r\n  { name: \"Brunei Darussalam\", code: \"BN\" },\r\n  { name: \"Bulgaria\", code: \"BG\" },\r\n  { name: \"Burkina Faso\", code: \"BF\" },\r\n  { name: \"Burundi\", code: \"BI\" },\r\n  { name: \"Cambodia\", code: \"KH\" },\r\n  { name: \"Cameroon\", code: \"CM\" },\r\n  { name: \"Canada\", code: \"CA\" },\r\n  { name: \"Cape Verde\", code: \"CV\" },\r\n  { name: \"Cayman Islands\", code: \"KY\" },\r\n  { name: \"Central African Republic\", code: \"CF\" },\r\n  { name: \"Chad\", code: \"TD\" },\r\n  { name: \"Chile\", code: \"CL\" },\r\n  { name: \"China\", code: \"CN\" },\r\n  { name: \"Christmas Island\", code: \"CX\" },\r\n  { name: \"Cocos (Keeling) Islands\", code: \"CC\" },\r\n  { name: \"Colombia\", code: \"CO\" },\r\n  { name: \"Comoros\", code: \"KM\" },\r\n  { name: \"Congo\", code: \"CG\" },\r\n  { name: \"Congo, The Democratic Republic of The\", code: \"CD\" },\r\n  { name: \"Cook Islands\", code: \"CK\" },\r\n  { name: \"Costa Rica\", code: \"CR\" },\r\n  { name: \"Cote D'ivoire\", code: \"CI\" },\r\n  { name: \"Croatia\", code: \"HR\" },\r\n  { name: \"Cuba\", code: \"CU\" },\r\n  { name: \"Cyprus\", code: \"CY\" },\r\n  { name: \"Czech Republic\", code: \"CZ\" },\r\n  { name: \"Denmark\", code: \"DK\" },\r\n  { name: \"Djibouti\", code: \"DJ\" },\r\n  { name: \"Dominica\", code: \"DM\" },\r\n  { name: \"Dominican Republic\", code: \"DO\" },\r\n  { name: \"Ecuador\", code: \"EC\" },\r\n  { name: \"Egypt\", code: \"EG\" },\r\n  { name: \"El Salvador\", code: \"SV\" },\r\n  { name: \"Equatorial Guinea\", code: \"GQ\" },\r\n  { name: \"Eritrea\", code: \"ER\" },\r\n  { name: \"Estonia\", code: \"EE\" },\r\n  { name: \"Ethiopia\", code: \"ET\" },\r\n  { name: \"Falkland Islands (Malvinas)\", code: \"FK\" },\r\n  { name: \"Faroe Islands\", code: \"FO\" },\r\n  { name: \"Fiji\", code: \"FJ\" },\r\n  { name: \"Finland\", code: \"FI\" },\r\n  { name: \"France\", code: \"FR\" },\r\n  { name: \"French Guiana\", code: \"GF\" },\r\n  { name: \"French Polynesia\", code: \"PF\" },\r\n  { name: \"French Southern Territories\", code: \"TF\" },\r\n  { name: \"Gabon\", code: \"GA\" },\r\n  { name: \"Gambia\", code: \"GM\" },\r\n  { name: \"Georgia\", code: \"GE\" },\r\n  { name: \"Germany\", code: \"DE\" },\r\n  { name: \"Ghana\", code: \"GH\" },\r\n  { name: \"Gibraltar\", code: \"GI\" },\r\n  { name: \"Greece\", code: \"GR\" },\r\n  { name: \"Greenland\", code: \"GL\" },\r\n  { name: \"Grenada\", code: \"GD\" },\r\n  { name: \"Guadeloupe\", code: \"GP\" },\r\n  { name: \"Guam\", code: \"GU\" },\r\n  { name: \"Guatemala\", code: \"GT\" },\r\n  { name: \"Guernsey\", code: \"GG\" },\r\n  { name: \"Guinea\", code: \"GN\" },\r\n  { name: \"Guinea-bissau\", code: \"GW\" },\r\n  { name: \"Guyana\", code: \"GY\" },\r\n  { name: \"Haiti\", code: \"HT\" },\r\n  { name: \"Heard Island and Mcdonald Islands\", code: \"HM\" },\r\n  { name: \"Holy See (Vatican City State)\", code: \"VA\" },\r\n  { name: \"Honduras\", code: \"HN\" },\r\n  { name: \"Hong Kong\", code: \"HK\" },\r\n  { name: \"Hungary\", code: \"HU\" },\r\n  { name: \"Iceland\", code: \"IS\" },\r\n  { name: \"India\", code: \"IN\" },\r\n  { name: \"Indonesia\", code: \"ID\" },\r\n  { name: \"Iran, Islamic Republic of\", code: \"IR\" },\r\n  { name: \"Iraq\", code: \"IQ\" },\r\n  { name: \"Ireland\", code: \"IE\" },\r\n  { name: \"Isle of Man\", code: \"IM\" },\r\n  { name: \"Israel\", code: \"IL\" },\r\n  { name: \"Italy\", code: \"IT\" },\r\n  { name: \"Jamaica\", code: \"JM\" },\r\n  { name: \"Japan\", code: \"JP\" },\r\n  { name: \"Jersey\", code: \"JE\" },\r\n  { name: \"Jordan\", code: \"JO\" },\r\n  { name: \"Kazakhstan\", code: \"KZ\" },\r\n  { name: \"Kenya\", code: \"KE\" },\r\n  { name: \"Kiribati\", code: \"KI\" },\r\n  { name: \"Korea, Democratic People's Republic of\", code: \"KP\" },\r\n  { name: \"Korea, Republic of\", code: \"KR\" },\r\n  { name: \"Kuwait\", code: \"KW\" },\r\n  { name: \"Kyrgyzstan\", code: \"KG\" },\r\n  { name: \"Lao People's Democratic Republic\", code: \"LA\" },\r\n  { name: \"Latvia\", code: \"LV\" },\r\n  { name: \"Lebanon\", code: \"LB\" },\r\n  { name: \"Lesotho\", code: \"LS\" },\r\n  { name: \"Liberia\", code: \"LR\" },\r\n  { name: \"Libyan Arab Jamahiriya\", code: \"LY\" },\r\n  { name: \"Liechtenstein\", code: \"LI\" },\r\n  { name: \"Lithuania\", code: \"LT\" },\r\n  { name: \"Luxembourg\", code: \"LU\" },\r\n  { name: \"Macao\", code: \"MO\" },\r\n  { name: \"Macedonia, The Former Yugoslav Republic of\", code: \"MK\" },\r\n  { name: \"Madagascar\", code: \"MG\" },\r\n  { name: \"Malawi\", code: \"MW\" },\r\n  { name: \"Malaysia\", code: \"MY\" },\r\n  { name: \"Maldives\", code: \"MV\" },\r\n  { name: \"Mali\", code: \"ML\" },\r\n  { name: \"Malta\", code: \"MT\" },\r\n  { name: \"Marshall Islands\", code: \"MH\" },\r\n  { name: \"Martinique\", code: \"MQ\" },\r\n  { name: \"Mauritania\", code: \"MR\" },\r\n  { name: \"Mauritius\", code: \"MU\" },\r\n  { name: \"Mayotte\", code: \"YT\" },\r\n  { name: \"Mexico\", code: \"MX\" },\r\n  { name: \"Micronesia, Federated States of\", code: \"FM\" },\r\n  { name: \"Moldova, Republic of\", code: \"MD\" },\r\n  { name: \"Monaco\", code: \"MC\" },\r\n  { name: \"Mongolia\", code: \"MN\" },\r\n  { name: \"Montenegro\", code: \"ME\" },\r\n  { name: \"Montserrat\", code: \"MS\" },\r\n  { name: \"Morocco\", code: \"MA\" },\r\n  { name: \"Mozambique\", code: \"MZ\" },\r\n  { name: \"Myanmar\", code: \"MM\" },\r\n  { name: \"Namibia\", code: \"NA\" },\r\n  { name: \"Nauru\", code: \"NR\" },\r\n  { name: \"Nepal\", code: \"NP\" },\r\n  { name: \"Netherlands\", code: \"NL\" },\r\n  { name: \"Netherlands Antilles\", code: \"AN\" },\r\n  { name: \"New Caledonia\", code: \"NC\" },\r\n  { name: \"New Zealand\", code: \"NZ\" },\r\n  { name: \"Nicaragua\", code: \"NI\" },\r\n  { name: \"Niger\", code: \"NE\" },\r\n  { name: \"Nigeria\", code: \"NG\" },\r\n  { name: \"Niue\", code: \"NU\" },\r\n  { name: \"Norfolk Island\", code: \"NF\" },\r\n  { name: \"Northern Mariana Islands\", code: \"MP\" },\r\n  { name: \"Norway\", code: \"NO\" },\r\n  { name: \"Oman\", code: \"OM\" },\r\n  { name: \"Pakistan\", code: \"PK\" },\r\n  { name: \"Palau\", code: \"PW\" },\r\n  { name: \"Palestinian Territory, Occupied\", code: \"PS\" },\r\n  { name: \"Panama\", code: \"PA\" },\r\n  { name: \"Papua New Guinea\", code: \"PG\" },\r\n  { name: \"Paraguay\", code: \"PY\" },\r\n  { name: \"Peru\", code: \"PE\" },\r\n  { name: \"Philippines\", code: \"PH\" },\r\n  { name: \"Pitcairn\", code: \"PN\" },\r\n  { name: \"Poland\", code: \"PL\" },\r\n  { name: \"Portugal\", code: \"PT\" },\r\n  { name: \"Puerto Rico\", code: \"PR\" },\r\n  { name: \"Qatar\", code: \"QA\" },\r\n  { name: \"Reunion\", code: \"RE\" },\r\n  { name: \"Romania\", code: \"RO\" },\r\n  { name: \"Russian Federation\", code: \"RU\" },\r\n  { name: \"Rwanda\", code: \"RW\" },\r\n  { name: \"Saint Helena\", code: \"SH\" },\r\n  { name: \"Saint Kitts and Nevis\", code: \"KN\" },\r\n  { name: \"Saint Lucia\", code: \"LC\" },\r\n  { name: \"Saint Pierre and Miquelon\", code: \"PM\" },\r\n  { name: \"Saint Vincent and The Grenadines\", code: \"VC\" },\r\n  { name: \"Samoa\", code: \"WS\" },\r\n  { name: \"San Marino\", code: \"SM\" },\r\n  { name: \"Sao Tome and Principe\", code: \"ST\" },\r\n  { name: \"Saudi Arabia\", code: \"SA\" },\r\n  { name: \"Senegal\", code: \"SN\" },\r\n  { name: \"Serbia\", code: \"RS\" },\r\n  { name: \"Seychelles\", code: \"SC\" },\r\n  { name: \"Sierra Leone\", code: \"SL\" },\r\n  { name: \"Singapore\", code: \"SG\" },\r\n  { name: \"Slovakia\", code: \"SK\" },\r\n  { name: \"Slovenia\", code: \"SI\" },\r\n  { name: \"Solomon Islands\", code: \"SB\" },\r\n  { name: \"Somalia\", code: \"SO\" },\r\n  { name: \"South Africa\", code: \"ZA\" },\r\n  { name: \"South Georgia and The South Sandwich Islands\", code: \"GS\" },\r\n  { name: \"Spain\", code: \"ES\" },\r\n  { name: \"Sri Lanka\", code: \"LK\" },\r\n  { name: \"Sudan\", code: \"SD\" },\r\n  { name: \"Suriname\", code: \"SR\" },\r\n  { name: \"Svalbard and Jan Mayen\", code: \"SJ\" },\r\n  { name: \"Swaziland\", code: \"SZ\" },\r\n  { name: \"Sweden\", code: \"SE\" },\r\n  { name: \"Switzerland\", code: \"CH\" },\r\n  { name: \"Syrian Arab Republic\", code: \"SY\" },\r\n  { name: \"Taiwan\", code: \"TW\" },\r\n  { name: \"Tajikistan\", code: \"TJ\" },\r\n  { name: \"Tanzania, United Republic of\", code: \"TZ\" },\r\n  { name: \"Thailand\", code: \"TH\" },\r\n  { name: \"Timor-leste\", code: \"TL\" },\r\n  { name: \"Togo\", code: \"TG\" },\r\n  { name: \"Tokelau\", code: \"TK\" },\r\n  { name: \"Tonga\", code: \"TO\" },\r\n  { name: \"Trinidad and Tobago\", code: \"TT\" },\r\n  { name: \"Tunisia\", code: \"TN\" },\r\n  { name: \"Turkey\", code: \"TR\" },\r\n  { name: \"Turkmenistan\", code: \"TM\" },\r\n  { name: \"Turks and Caicos Islands\", code: \"TC\" },\r\n  { name: \"Tuvalu\", code: \"TV\" },\r\n  { name: \"Uganda\", code: \"UG\" },\r\n  { name: \"Ukraine\", code: \"UA\" },\r\n  { name: \"United Arab Emirates\", code: \"AE\" },\r\n  { name: \"United Kingdom\", code: \"GB\" },\r\n  { name: \"United States\", code: \"US\" },\r\n  { name: \"United States Minor Outlying Islands\", code: \"UM\" },\r\n  { name: \"Uruguay\", code: \"UY\" },\r\n  { name: \"Uzbekistan\", code: \"UZ\" },\r\n  { name: \"Vanuatu\", code: \"VU\" },\r\n  { name: \"Venezuela\", code: \"VE\" },\r\n  { name: \"Viet Nam\", code: \"VN\" },\r\n  { name: \"Virgin Islands, British\", code: \"VG\" },\r\n  { name: \"Virgin Islands, U.S.\", code: \"VI\" },\r\n  { name: \"Wallis and Futuna\", code: \"WF\" },\r\n  { name: \"Western Sahara\", code: \"EH\" },\r\n  { name: \"Yemen\", code: \"YE\" },\r\n  { name: \"Zambia\", code: \"ZM\" },\r\n  { name: \"Zimbabwe\", code: \"ZW\" },\r\n];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACvB;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAuB,MAAM;IAAK;IAC1C;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAkC,MAAM;IAAK;IACrD;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAA2B,MAAM;IAAK;IAC9C;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAyC,MAAM;IAAK;IAC5D;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA+B,MAAM;IAAK;IAClD;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAA+B,MAAM;IAAK;IAClD;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAqC,MAAM;IAAK;IACxD;QAAE,MAAM;QAAiC,MAAM;IAAK;IACpD;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAA6B,MAAM;IAAK;IAChD;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA0C,MAAM;IAAK;IAC7D;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAoC,MAAM;IAAK;IACvD;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAA8C,MAAM;IAAK;IACjE;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAmC,MAAM;IAAK;IACtD;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAmC,MAAM;IAAK;IACtD;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAyB,MAAM;IAAK;IAC5C;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAA6B,MAAM;IAAK;IAChD;QAAE,MAAM;QAAoC,MAAM;IAAK;IACvD;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAyB,MAAM;IAAK;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAmB,MAAM;IAAK;IACtC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAgD,MAAM;IAAK;IACnE;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAgC,MAAM;IAAK;IACnD;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAuB,MAAM;IAAK;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAwC,MAAM;IAAK;IAC3D;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA2B,MAAM;IAAK;IAC9C;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;CAChC"}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\r\nimport { DayPicker } from \"react-day-picker\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\nimport { buttonVariants } from \"@/app/components/ui/button\";\r\n\r\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>;\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: CalendarProps) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\r\n        month: \"space-y-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"space-x-1 flex items-center\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-y-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"h-8 w-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start: \"day-range-start\",\r\n        day_range_end: \"day-range-end\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"h-4 w-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"h-4 w-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\nCalendar.displayName = \"Calendar\";\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAIA;AAEA;AACA;AAJA;AAAA;AAHA;;;;;;AAWA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACW;IACd,qBACE,6LAAC,iKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACL,qNACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBAAiB;YACjB,eAAe;YACf,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;YAE7D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;QAEhE;QACC,GAAG,KAAK;;;;;;AAGf;KA7DS;AA8DT,SAAS,WAAW,GAAG"}}, {"offset": {"line": 1183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/popover.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Popover = PopoverPrimitive.Root;\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger;\r\n\r\nconst PopoverAnchor = PopoverPrimitive.Anchor;\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n));\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName;\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,UAAU,uKAAiB,IAAI;AAErC,MAAM,iBAAiB,uKAAiB,OAAO;AAE/C,MAAM,gBAAgB,uKAAiB,MAAM;AAE7C,MAAM,+BAAiB,8JAAM,UAAU,MAGrC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,uKAAiB,MAAM;kBACtB,cAAA,6LAAC,uKAAiB,OAAO;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,uKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n);\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n));\r\nLabel.displayName = LabelPrimitive.Root.displayName;\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAHA;AAHA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,8JAAM,UAAU,MAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAe,IAAI;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,qKAAe,IAAI,CAAC,WAAW"}}, {"offset": {"line": 1308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,MAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,sKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,8JAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,sKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,8JAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,sKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,sKAAgB,MAAM;kBACrB,cAAA,6LAAC,sKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,sKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sKAAgB,aAAa;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6LAAC,sKAAgB,QAAQ;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,8JAAM,UAAU,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,sKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/date-of-birth/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { ChevronDown } from \"lucide-react\";\r\nimport { format, parse, isValid } from \"date-fns\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Calendar } from \"@components/ui/calendar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@components/ui/popover\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { Label } from \"@components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@components/ui/select\";\r\n\r\ninterface BirthdayPickerProps {\r\n  value?: string;\r\n  onChange: (date: string) => void;\r\n  required?: boolean;\r\n}\r\n\r\nexport default function BirthdayPicker({\r\n  value,\r\n  onChange,\r\n  required = false,\r\n}: BirthdayPickerProps) {\r\n  const [date, setDate] = useState<Date | undefined>(\r\n    value ? new Date(value) : undefined\r\n  );\r\n  const [day, setDay] = useState(\r\n    value ? new Date(value).getDate().toString().padStart(2, \"0\") : \"\"\r\n  );\r\n  const [month, setMonth] = useState(\r\n    value ? (new Date(value).getMonth() + 1).toString().padStart(2, \"0\") : \"\"\r\n  );\r\n  const [year, setYear] = useState(\r\n    value ? new Date(value).getFullYear().toString() : \"\"\r\n  );\r\n  const [calendarKey, setCalendarKey] = useState(0);\r\n\r\n  const currentYear = new Date().getFullYear();\r\n  const years = Array.from({ length: 100 }, (_, i) => currentYear - i);\r\n\r\n  useEffect(() => {\r\n    if (value) {\r\n      const newDate = new Date(value);\r\n      if (isValid(newDate)) {\r\n        setDate(newDate);\r\n        setDay(newDate.getDate().toString().padStart(2, \"0\"));\r\n        setMonth((newDate.getMonth() + 1).toString().padStart(2, \"0\"));\r\n        setYear(newDate.getFullYear().toString());\r\n      }\r\n    }\r\n  }, [value]);\r\n\r\n  const updateDateFromInputs = () => {\r\n    const dateString = `${year}-${month}-${day}`;\r\n    const newDate = parse(dateString, \"yyyy-MM-dd\", new Date());\r\n    if (isValid(newDate)) {\r\n      setDate(newDate);\r\n      onChange(format(newDate, \"yyyy-MM-dd\"));\r\n      setCalendarKey((prev) => prev + 1);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (\r\n    value: string,\r\n    setter: React.Dispatch<React.SetStateAction<string>>\r\n  ) => {\r\n    const numericValue = value.replace(/\\D/g, \"\");\r\n    setter(numericValue);\r\n  };\r\n\r\n  const handleYearChange = (selectedYear: string) => {\r\n    setYear(selectedYear);\r\n    const newDate = date ? new Date(date) : new Date();\r\n    newDate.setFullYear(Number.parseInt(selectedYear));\r\n    setDate(newDate);\r\n    onChange(format(newDate, \"yyyy-MM-dd\"));\r\n    setCalendarKey((prev) => prev + 1);\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <Label\r\n        htmlFor=\"dob-input\"\r\n        className=\"block mb-2 text-sm font-medium text-gray-900\"\r\n      >\r\n        Date of Birth {required && <span className=\"text-red-500\">*</span>}\r\n      </Label>\r\n      <div className=\"flex flex-col md:flex-row lg:flex-col 2xl:flex-row  gap-2\">\r\n        <div className=\"flex gap-2 w-full md:w-[60%] lg:w-full 2xl:w-[60%] \">\r\n          <Input\r\n            id=\"dob-day\"\r\n            value={day}\r\n            onChange={(e) => handleInputChange(e.target.value, setDay)}\r\n            onBlur={updateDateFromInputs}\r\n            placeholder=\"DD\"\r\n            className=\"h-full p-3 border rounded-lg\"\r\n            maxLength={2}\r\n            required={required}\r\n          />\r\n          <Input\r\n            id=\"dob-month\"\r\n            value={month}\r\n            onChange={(e) => handleInputChange(e.target.value, setMonth)}\r\n            onBlur={updateDateFromInputs}\r\n            placeholder=\"MM\"\r\n            className=\"h-full p-3 border rounded-lg\"\r\n            maxLength={2}\r\n            required={required}\r\n          />\r\n          <Input\r\n            id=\"dob-year\"\r\n            value={year}\r\n            onChange={(e) => handleInputChange(e.target.value, setYear)}\r\n            onBlur={updateDateFromInputs}\r\n            placeholder=\"YYYY\"\r\n            className=\"h-full p-3 border rounded-lg\"\r\n            maxLength={4}\r\n            required={required}\r\n          />\r\n        </div>\r\n        <Popover>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant={\"outline\"}\r\n              className={cn(\r\n                \"w-full md:w-[40%] lg:w-full 2xl:w-[40%] h-full justify-start text-left font-normal\",\r\n                !date && \"text-muted-foreground\"\r\n              )}\r\n            >\r\n              {date ? format(date, \"PPP\") : <span>Pick a date</span>}\r\n              <ChevronDown className=\"ml-auto h-4 w-4 opacity-50\" />\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-auto p-0 bg-white\" align=\"start\">\r\n            <div className=\"flex flex-col space-y-2 p-2\">\r\n              <Select value={year} onValueChange={handleYearChange}>\r\n                <SelectTrigger>\r\n                  <SelectValue placeholder=\"Select Year\" />\r\n                </SelectTrigger>\r\n                <SelectContent className=\"bg-white\">\r\n                  {years.map((y) => (\r\n                    <SelectItem\r\n                      key={y}\r\n                      value={y.toString()}\r\n                      className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                    >\r\n                      {y}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n              <Calendar\r\n                key={calendarKey}\r\n                mode=\"single\"\r\n                selected={date}\r\n                onSelect={(newDate) => {\r\n                  setDate(newDate);\r\n                  if (newDate) {\r\n                    setDay(newDate.getDate().toString().padStart(2, \"0\"));\r\n                    setMonth(\r\n                      (newDate.getMonth() + 1).toString().padStart(2, \"0\")\r\n                    );\r\n                    setYear(newDate.getFullYear().toString());\r\n                    onChange(format(newDate, \"yyyy-MM-dd\"));\r\n                  }\r\n                }}\r\n                defaultMonth={date}\r\n                fromYear={Number.parseInt(year) || currentYear - 100}\r\n                toYear={Number.parseInt(year) || currentYear}\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAGA;AACA;AACA;AACA;AAKA;AACA;AACA;AAXA;AAAA;AAAA;AADA;;;AAJA;;;;;;;;;;;AA8Be,SAAS,eAAe,EACrC,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EACI;;IACpB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7B,QAAQ,IAAI,KAAK,SAAS;IAE5B,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3B,QAAQ,IAAI,KAAK,OAAO,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,OAAO;IAElE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/B,QAAQ,CAAC,IAAI,KAAK,OAAO,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,OAAO;IAEzE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7B,QAAQ,IAAI,KAAK,OAAO,WAAW,GAAG,QAAQ,KAAK;IAErD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAI,GAAG,CAAC,GAAG,IAAM,cAAc;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,OAAO;gBACT,MAAM,UAAU,IAAI,KAAK;gBACzB,IAAI,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,UAAU;oBACpB,QAAQ;oBACR,OAAO,QAAQ,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAChD,SAAS,CAAC,QAAQ,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBACzD,QAAQ,QAAQ,WAAW,GAAG,QAAQ;gBACxC;YACF;QACF;mCAAG;QAAC;KAAM;IAEV,MAAM,uBAAuB;QAC3B,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;QAC5C,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,YAAY,cAAc,IAAI;QACpD,IAAI,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,UAAU;YACpB,QAAQ;YACR,SAAS,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;YACzB,eAAe,CAAC,OAAS,OAAO;QAClC;IACF;IAEA,MAAM,oBAAoB,CACxB,OACA;QAEA,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO;QAC1C,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ;QACR,MAAM,UAAU,OAAO,IAAI,KAAK,QAAQ,IAAI;QAC5C,QAAQ,WAAW,CAAC,OAAO,QAAQ,CAAC;QACpC,QAAQ;QACR,SAAS,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QACzB,eAAe,CAAC,OAAS,OAAO;IAClC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oIAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,WAAU;;oBACX;oBACgB,0BAAY,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK,EAAE;gCACnD,QAAQ;gCACR,aAAY;gCACZ,WAAU;gCACV,WAAW;gCACX,UAAU;;;;;;0CAEZ,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK,EAAE;gCACnD,QAAQ;gCACR,aAAY;gCACZ,WAAU;gCACV,WAAW;gCACX,UAAU;;;;;;0CAEZ,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK,EAAE;gCACnD,QAAQ;gCACR,aAAY;gCACZ,WAAU;gCACV,WAAW;gCACX,UAAU;;;;;;;;;;;;kCAGd,6LAAC,sIAAA,CAAA,UAAO;;0CACN,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sFACA,CAAC,QAAQ;;wCAGV,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,uBAAS,6LAAC;sDAAK;;;;;;sDACpC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG3B,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAsB,OAAM;0CACpD,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAM,eAAe;;8DAClC,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACtB,MAAM,GAAG,CAAC,CAAC,kBACV,6LAAC,qIAAA,CAAA,aAAU;4DAET,OAAO,EAAE,QAAQ;4DACjB,WAAU;sEAET;2DAJI;;;;;;;;;;;;;;;;sDASb,6LAAC,uIAAA,CAAA,WAAQ;4CAEP,MAAK;4CACL,UAAU;4CACV,UAAU,CAAC;gDACT,QAAQ;gDACR,IAAI,SAAS;oDACX,OAAO,QAAQ,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oDAChD,SACE,CAAC,QAAQ,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;oDAElD,QAAQ,QAAQ,WAAW,GAAG,QAAQ;oDACtC,SAAS,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;gDAC3B;4CACF;4CACA,cAAc;4CACd,UAAU,OAAO,QAAQ,CAAC,SAAS,cAAc;4CACjD,QAAQ,OAAO,QAAQ,CAAC,SAAS;2CAhB5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBrB;GA9JwB;KAAA"}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/forms/personal-info.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useProfile } from \"@hooks/student/useProfile\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\nimport { countries } from \"@/lib/countries\";\r\nimport { z } from \"zod\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport BirthdayPicker from \"@components/common/date-of-birth\";\r\nimport { Dialog } from \"@headlessui/react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@components/ui/select\";\r\n\r\nconst personalInfoSchema = z.object({\r\n  nationality: z.string().min(1, \"Nationality is required\"),\r\n  country_of_residence: z.string().min(1, \"Country of residence is required\"),\r\n  gender: z.string().min(1, \"Gender is required\"),\r\n  date_of_birth: z\r\n    .string()\r\n    .min(1, \"Date of birth is required\")\r\n    .refine((date) => new Date(date) < new Date(), {\r\n      message: \"Date of birth must be in the past\",\r\n    }),\r\n});\r\n\r\ntype PersonalInfoFormData = z.infer<typeof personalInfoSchema>;\r\n\r\nconst PersonalInfoForm = () => {\r\n  const {\r\n    submitPersonalInfo,\r\n    clearError,\r\n    currentStep,\r\n    setCurrentStep,\r\n    loading,\r\n    personalInfo,\r\n    userInfo,\r\n  } = useProfile();\r\n\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [selectedField, setSelectedField] = useState<\r\n    \"nationality\" | \"country_of_residence\" | null\r\n  >(null);\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    setValue,\r\n    watch,\r\n  } = useForm<PersonalInfoFormData>({\r\n    resolver: zodResolver(personalInfoSchema),\r\n    defaultValues: {\r\n      nationality: personalInfo?.nationality || \"\",\r\n      country_of_residence: personalInfo?.country_of_residence || \"\",\r\n      gender: personalInfo?.gender || \"\",\r\n      date_of_birth: personalInfo?.date_of_birth?.split(\"T\")[0] || \"\",\r\n    },\r\n  });\r\n\r\n  const formValues = watch();\r\n\r\n  useEffect(() => {\r\n    clearError();\r\n  }, [clearError]);\r\n\r\n  const onSubmit = async (data: PersonalInfoFormData) => {\r\n    if (!currentStep) return;\r\n    const loadingToast = toast.loading(\"Submitting personal information\");\r\n\r\n    const formattedData = {\r\n      ...data,\r\n      first_name: userInfo!.firstName,\r\n      last_name: userInfo!.lastName,\r\n      date_of_birth: new Date(data.date_of_birth).toISOString(),\r\n    };\r\n\r\n    try {\r\n      await submitPersonalInfo(formattedData);\r\n      toast.update(loadingToast, {\r\n        render: \"Personal information updated successfully\",\r\n        type: \"success\",\r\n        isLoading: false,\r\n        autoClose: 2000,\r\n      });\r\n      setCurrentStep(currentStep + 1);\r\n    } catch (error) {\r\n      toast.update(loadingToast, {\r\n        render: \"Failed to update personal information\",\r\n        type: \"error\",\r\n        isLoading: false,\r\n        autoClose: 2000,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleOpenModal = (field: \"nationality\" | \"country_of_residence\") => {\r\n    setSelectedField(field);\r\n    setSearchTerm(\"\");\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const handleCountrySelect = (code: string) => {\r\n    if (selectedField) {\r\n      setValue(selectedField, code);\r\n      setIsModalOpen(false);\r\n    }\r\n  };\r\n\r\n  const getFieldLabel = (field: string) => {\r\n    const country = countries.find((c) => c.code === field);\r\n    return country ? country.name : \"Please select\";\r\n  };\r\n\r\n  const filteredCountries = countries.filter((country) =>\r\n    country.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\r\n      <div>\r\n        <h2 className=\"sm:text-3xl text-2xl font-semibold\">\r\n          Tell us about <span className=\"text-blue-500\">yourself</span>\r\n        </h2>\r\n        <p className=\"text-gray-600 mt-2\">\r\n          Please complete the following details to let us know more about you.\r\n          It will take less than 5 minutes!\r\n        </p>\r\n      </div>\r\n      <div className=\"gap-y-6 gap-x-8 w-full grid lg:grid-cols-2 grid-cols-1\">\r\n        <div>\r\n          <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n            Nationality<span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleOpenModal(\"nationality\")}\r\n            className=\"w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white\"\r\n          >\r\n            {getFieldLabel(formValues.nationality)}\r\n          </button>\r\n          {errors.nationality && (\r\n            <p className=\"mt-1 text-sm text-red-600\">\r\n              {errors.nationality.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n            Country of Residence<span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleOpenModal(\"country_of_residence\")}\r\n            className=\"w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white\"\r\n          >\r\n            {getFieldLabel(formValues.country_of_residence)}\r\n          </button>\r\n          {errors.country_of_residence && (\r\n            <p className=\"mt-1 text-sm text-red-600\">\r\n              {errors.country_of_residence.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <label className=\"text-sm font-medium text-gray-700\">\r\n            Gender<span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <Select\r\n            onValueChange={(value) => setValue(\"gender\", value)}\r\n            value={watch(\"gender\")}\r\n          >\r\n            <SelectTrigger\r\n              className={`shadow-none bg-white ${\r\n                errors.gender ? \"border-red-500\" : \"\"\r\n              }`}\r\n            >\r\n              <SelectValue placeholder=\"Select gender\" />\r\n            </SelectTrigger>\r\n            <SelectContent className=\"bg-white\">\r\n              <SelectItem\r\n                className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                value=\"male\"\r\n              >\r\n                Male\r\n              </SelectItem>\r\n              <SelectItem\r\n                className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                value=\"female\"\r\n              >\r\n                Female\r\n              </SelectItem>\r\n              <SelectItem\r\n                className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                value=\"other\"\r\n              >\r\n                Other\r\n              </SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n          {errors.gender && (\r\n            <p className=\"text-red-500 text-sm\">{errors.gender.message}</p>\r\n          )}\r\n        </div>\r\n\r\n        <BirthdayPicker\r\n          value={formValues.date_of_birth}\r\n          onChange={(date) => setValue(\"date_of_birth\", date)}\r\n          required={true}\r\n        />\r\n        {errors.date_of_birth && (\r\n          <p className=\"mt-1 text-sm text-red-600\">\r\n            {errors.date_of_birth.message}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex justify-end space-x-4\">\r\n        <button\r\n          type=\"submit\"\r\n          disabled={loading}\r\n          className=\"inline-flex justify-center rounded-md border border-transparent bg-indigo-950 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-950/90 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2\"\r\n        >\r\n          {loading ? \"Saving...\" : \"Next\"}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Country Selection Modal */}\r\n      <Dialog\r\n        open={isModalOpen}\r\n        onClose={() => setIsModalOpen(false)}\r\n        className=\"relative z-50\"\r\n      >\r\n        <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\r\n        <div className=\"fixed inset-0 flex items-center justify-center p-4\">\r\n          <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n            <Dialog.Title className=\"text-lg font-medium leading-6 text-gray-900 mb-4\">\r\n              Select{\" \"}\r\n              {selectedField === \"nationality\"\r\n                ? \"Nationality\"\r\n                : \"Country of Residence\"}\r\n            </Dialog.Title>\r\n\r\n            {/* Search Input */}\r\n            <div className=\"mb-4\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search countries...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                autoFocus={true}\r\n                className=\"w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500\"\r\n              />\r\n            </div>\r\n\r\n            {/* Countries List */}\r\n            <div className=\"mt-2 max-h-[60vh] overflow-y-auto\">\r\n              {filteredCountries.map((country) => (\r\n                <button\r\n                  key={country.code}\r\n                  type=\"button\"\r\n                  className=\"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg\"\r\n                  onClick={() => handleCountrySelect(country.code)}\r\n                >\r\n                  {country.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </Dialog.Panel>\r\n        </div>\r\n      </Dialog>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default PersonalInfoForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AACA;AAEA;AALA;AACA;AAGA;;;AAVA;;;;;;;;;;;AAmBA,MAAM,qBAAqB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,sBAAsB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxC,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,eAAe,uIAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,GAAG,6BACP,MAAM,CAAC,CAAC,OAAS,IAAI,KAAK,QAAQ,IAAI,QAAQ;QAC7C,SAAS;IACX;AACJ;AAIA,MAAM,mBAAmB;;IACvB,MAAM,EACJ,kBAAkB,EAClB,UAAU,EACV,WAAW,EACX,cAAc,EACd,OAAO,EACP,YAAY,EACZ,QAAQ,EACT,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE/C;IAEF,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAwB;QAChC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,aAAa,cAAc,eAAe;YAC1C,sBAAsB,cAAc,wBAAwB;YAC5D,QAAQ,cAAc,UAAU;YAChC,eAAe,cAAc,eAAe,MAAM,IAAI,CAAC,EAAE,IAAI;QAC/D;IACF;IAEA,MAAM,aAAa;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAW;IAEf,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,aAAa;QAClB,MAAM,eAAe,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAEnC,MAAM,gBAAgB;YACpB,GAAG,IAAI;YACP,YAAY,SAAU,SAAS;YAC/B,WAAW,SAAU,QAAQ;YAC7B,eAAe,IAAI,KAAK,KAAK,aAAa,EAAE,WAAW;QACzD;QAEA,IAAI;YACF,MAAM,mBAAmB;YACzB,sJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,cAAc;gBACzB,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,WAAW;YACb;YACA,eAAe,cAAc;QAC/B,EAAE,OAAO,OAAO;YACd,sJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,cAAc;gBACzB,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,WAAW;YACb;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,eAAe;YACjB,SAAS,eAAe;YACxB,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAU,mHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;QACjD,OAAO,UAAU,QAAQ,IAAI,GAAG;IAClC;IAEA,MAAM,oBAAoB,mHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,UAC1C,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,qBACE,6LAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAChD,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;;4BAAqC;0CACnC,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;kCAEhD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;kDACnD,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE5C,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CAET,cAAc,WAAW,WAAW;;;;;;4BAEtC,OAAO,WAAW,kBACjB,6LAAC;gCAAE,WAAU;0CACV,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAKjC,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;kDAC1C,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAErD,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CAET,cAAc,WAAW,oBAAoB;;;;;;4BAE/C,OAAO,oBAAoB,kBAC1B,6LAAC;gCAAE,WAAU;0CACV,OAAO,oBAAoB,CAAC,OAAO;;;;;;;;;;;;kCAK1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;oCAAoC;kDAC7C,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,eAAe,CAAC,QAAU,SAAS,UAAU;gCAC7C,OAAO,MAAM;;kDAEb,6LAAC,qIAAA,CAAA,gBAAa;wCACZ,WAAW,CAAC,qBAAqB,EAC/B,OAAO,MAAM,GAAG,mBAAmB,IACnC;kDAEF,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,6LAAC,qIAAA,CAAA,aAAU;gDACT,WAAU;gDACV,OAAM;0DACP;;;;;;0DAGD,6LAAC,qIAAA,CAAA,aAAU;gDACT,WAAU;gDACV,OAAM;0DACP;;;;;;0DAGD,6LAAC,qIAAA,CAAA,aAAU;gDACT,WAAU;gDACV,OAAM;0DACP;;;;;;;;;;;;;;;;;;4BAKJ,OAAO,MAAM,kBACZ,6LAAC;gCAAE,WAAU;0CAAwB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;kCAI9D,6LAAC,+JAAA,CAAA,UAAc;wBACb,OAAO,WAAW,aAAa;wBAC/B,UAAU,CAAC,OAAS,SAAS,iBAAiB;wBAC9C,UAAU;;;;;;oBAEX,OAAO,aAAa,kBACnB,6LAAC;wBAAE,WAAU;kCACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;0BAKnC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,UAAU,cAAc;;;;;;;;;;;0BAK7B,6LAAC,kLAAA,CAAA,SAAM;gBACL,MAAM;gBACN,SAAS,IAAM,eAAe;gBAC9B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;wBAA4B,eAAY;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;wCAAmD;wCAClE;wCACN,kBAAkB,gBACf,gBACA;;;;;;;8CAIN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW;wCACX,WAAU;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,6LAAC;4CAEC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,oBAAoB,QAAQ,IAAI;sDAE9C,QAAQ,IAAI;2CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcnC;GAxPM;;QASA,wIAAA,CAAA,aAAU;QAcV,iKAAA,CAAA,UAAO;;;KAvBP;uCA0PS"}}, {"offset": {"line": 2290, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2296, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/forms/education-info.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useProfile } from \"@hooks/student/useProfile\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { EducationInfo } from \"@/app/types/student/profile\";\r\nimport { Plus, Trash2 } from \"lucide-react\";\r\n\r\nconst EducationInfoForm = () => {\r\n  const {\r\n    educationInfo,\r\n    submitEducationInfo,\r\n    loading,\r\n    currentStep,\r\n    setCurrentStep,\r\n    deleteEducationInfo,\r\n  } = useProfile();\r\n\r\n  const [formData, setFormData] = useState<EducationInfo[]>([\r\n    {\r\n      current_education_level: \"high_school\",\r\n      institution_name: \"\",\r\n      institution_type: \"school\",\r\n      start_date: \"\",\r\n      end_date: \"\",\r\n      is_current: false,\r\n    },\r\n  ]);\r\n\r\n  const [isValid, setIsValid] = useState(false);\r\n  const [isSubmitted, setIsSubmitted] = useState(false);\r\n  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);\r\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\r\n\r\n  useEffect(() => {\r\n    if (educationInfo) {\r\n      if (educationInfo.length > 0) {\r\n        setFormData(\r\n          educationInfo.map((edu) => ({\r\n            ...edu,\r\n            institution_type: edu.institution_type || \"school\",\r\n            start_date: edu.start_date ? edu.start_date.split(\"T\")[0] : \"\",\r\n            end_date: edu.end_date ? edu.end_date.split(\"T\")[0] : \"\",\r\n          }))\r\n        );\r\n      }\r\n    } else {\r\n      setFormData([\r\n        {\r\n          current_education_level: \"high_school\",\r\n          institution_name: \"\",\r\n          institution_type: \"school\",\r\n          start_date: \"\",\r\n          end_date: \"\",\r\n          is_current: false,\r\n        },\r\n      ]);\r\n    }\r\n  }, [educationInfo]);\r\n\r\n  const validateForm = () => {\r\n    const newErrors: { [key: string]: string } = {};\r\n    let isFormValid = true;\r\n\r\n    formData.forEach((item, index) => {\r\n      if (!item.current_education_level) {\r\n        newErrors[`education_level_${index}`] = \"Education level is required\";\r\n        isFormValid = false;\r\n      }\r\n\r\n      if (!item.institution_name.trim()) {\r\n        newErrors[`institution_name_${index}`] = \"Institution name is required\";\r\n        isFormValid = false;\r\n      }\r\n\r\n      if (!item.start_date) {\r\n        newErrors[`start_date_${index}`] = \"Start date is required\";\r\n        isFormValid = false;\r\n      }\r\n\r\n      if (!item.is_current) {\r\n        if (!item.end_date) {\r\n          newErrors[`end_date_${index}`] = \"End date is required\";\r\n          isFormValid = false;\r\n        } else {\r\n          const startDate = new Date(item.start_date);\r\n          const endDate = new Date(item.end_date);\r\n\r\n          if (endDate < startDate) {\r\n            newErrors[`end_date_${index}`] =\r\n              \"End date must be after start date\";\r\n            isFormValid = false;\r\n          }\r\n\r\n          if (endDate > new Date()) {\r\n            newErrors[`end_date_${index}`] = \"End date cannot be in the future\";\r\n            isFormValid = false;\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    if (hasAttemptedSubmit) {\r\n      setErrors(newErrors);\r\n    }\r\n    setIsValid(isFormValid);\r\n    return isFormValid;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (hasAttemptedSubmit) {\r\n      validateForm();\r\n    }\r\n  }, [formData, hasAttemptedSubmit]);\r\n\r\n  const handleSubmit = async () => {\r\n    setHasAttemptedSubmit(true);\r\n    const isFormValid = validateForm();\r\n\r\n    if (!isFormValid) {\r\n      toast.error(\"Please fill in all required fields correctly\");\r\n      return;\r\n    }\r\n\r\n    if (isSubmitted) {\r\n      if (currentStep) setCurrentStep(currentStep + 1);\r\n      return;\r\n    }\r\n\r\n    const loadingToast = toast.loading(\"Submitting educational information\");\r\n\r\n    try {\r\n      const formattedData = formData.map((item) => ({\r\n        ...item,\r\n        start_date: new Date(item.start_date).toISOString(),\r\n        end_date:\r\n          item.is_current || !item.end_date\r\n            ? \"\"\r\n            : new Date(item.end_date).toISOString(),\r\n      }));\r\n\r\n      await submitEducationInfo(formattedData);\r\n\r\n      toast.dismiss(loadingToast);\r\n      toast.success(\"Education information saved successfully!\");\r\n      setIsSubmitted(true);\r\n      if (currentStep) setCurrentStep(currentStep + 1);\r\n    } catch (error) {\r\n      console.error(error);\r\n      toast.dismiss(loadingToast);\r\n      toast.error(\"Failed to save education information\");\r\n    }\r\n  };\r\n\r\n  const handleAddMore = () => {\r\n    setFormData((prev) => [\r\n      ...prev,\r\n      {\r\n        current_education_level: \"high_school\",\r\n        institution_name: \"\",\r\n        institution_type: \"school\",\r\n        start_date: \"\",\r\n        end_date: \"\",\r\n        is_current: false,\r\n      },\r\n    ]);\r\n  };\r\n\r\n  const handleDelete = async (index: number) => {\r\n    if (formData.length === 1) {\r\n      toast.error(\"You must have at least one education entry\");\r\n      return;\r\n    }\r\n\r\n    const education = formData[index];\r\n    if (education.id) {\r\n      await deleteEducationInfo(education.id);\r\n    }\r\n\r\n    setFormData((prev) => prev.filter((_, i) => i !== index));\r\n    setErrors((prev) => {\r\n      const newErrors = { ...prev };\r\n      Object.keys(newErrors).forEach((key) => {\r\n        if (key.includes(`_${index}`)) {\r\n          delete newErrors[key];\r\n        }\r\n      });\r\n      return newErrors;\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      <div>\r\n        <h2 className=\"sm:text-3xl text-2xl font-semibold\">\r\n          Educational <span className=\"text-blue-500\">Background</span>\r\n        </h2>\r\n        <p className=\"text-gray-600 mt-2\">\r\n          Please provide information about your education history.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"space-y-6\">\r\n        {formData.map((item, index) => (\r\n          <div key={index} className=\"relative space-y-6 p-6 border rounded-xl\">\r\n            {formData.length > 1 && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => handleDelete(index)}\r\n                className=\"absolute top-4 right-4 p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors\"\r\n              >\r\n                <Trash2 className=\"w-5 h-5\" />\r\n              </button>\r\n            )}\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div className=\"space-y-2\">\r\n                <label className=\"block text-sm font-medium\">\r\n                  Current Education Level{\" \"}\r\n                  <span className=\"text-red-500\">*</span>\r\n                </label>\r\n                <select\r\n                  className={`w-full p-3 bg-gray-50 border rounded-xl ${\r\n                    errors[`education_level_${index}`] ? \"border-red-500\" : \"\"\r\n                  }`}\r\n                  value={item.current_education_level}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) =>\r\n                      prev.map((item, i) =>\r\n                        i === index\r\n                          ? {\r\n                              ...item,\r\n                              current_education_level: e.target\r\n                                .value as EducationInfo[\"current_education_level\"],\r\n                            }\r\n                          : item\r\n                      )\r\n                    )\r\n                  }\r\n                  required\r\n                >\r\n                  <option value=\"\">Select level</option>\r\n                  <option value=\"high_school\">High School</option>\r\n                  <option value=\"bachelors\">Bachelor's Degree</option>\r\n                  <option value=\"masters\">Master's Degree</option>\r\n                  <option value=\"phd\">PhD</option>\r\n                  <option value=\"other\">Other</option>\r\n                </select>\r\n                {hasAttemptedSubmit && errors[`education_level_${index}`] && (\r\n                  <p className=\"text-sm text-red-500\">\r\n                    {errors[`education_level_${index}`]}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <label className=\"block text-sm font-medium\">\r\n                  Institution Type <span className=\"text-red-500\">*</span>\r\n                </label>\r\n                <select\r\n                  className=\"w-full p-3 bg-gray-50 border rounded-xl\"\r\n                  value={item.institution_type}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) =>\r\n                      prev.map((item, i) =>\r\n                        i === index\r\n                          ? {\r\n                              ...item,\r\n                              institution_type: e.target\r\n                                .value as EducationInfo[\"institution_type\"],\r\n                            }\r\n                          : item\r\n                      )\r\n                    )\r\n                  }\r\n                  required\r\n                >\r\n                  <option value=\"\">Select type</option>\r\n                  <option value=\"school\">School</option>\r\n                  <option value=\"college\">College</option>\r\n                  <option value=\"university\">University</option>\r\n                  <option value=\"other\">Other</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"space-y-2 md:col-span-2\">\r\n                <label className=\"block text-sm font-medium\">\r\n                  Institution Name <span className=\"text-red-500\">*</span>\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={item.institution_name}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) =>\r\n                      prev.map((item, i) =>\r\n                        i === index\r\n                          ? { ...item, institution_name: e.target.value }\r\n                          : item\r\n                      )\r\n                    )\r\n                  }\r\n                  className={`w-full p-3 bg-gray-50 border rounded-xl ${\r\n                    errors[`institution_name_${index}`] ? \"border-red-500\" : \"\"\r\n                  }`}\r\n                  placeholder=\"Enter institution name\"\r\n                  required\r\n                />\r\n                {hasAttemptedSubmit && errors[`institution_name_${index}`] && (\r\n                  <p className=\"text-sm text-red-500\">\r\n                    {errors[`institution_name_${index}`]}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <label className=\"block text-sm font-medium\">\r\n                  Start Date <span className=\"text-red-500\">*</span>\r\n                </label>\r\n                <input\r\n                  type=\"date\"\r\n                  value={item.start_date}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) =>\r\n                      prev.map((item, i) =>\r\n                        i === index\r\n                          ? { ...item, start_date: e.target.value }\r\n                          : item\r\n                      )\r\n                    )\r\n                  }\r\n                  className={`w-full p-3 bg-gray-50 border rounded-xl ${\r\n                    errors[`start_date_${index}`] ? \"border-red-500\" : \"\"\r\n                  }`}\r\n                  required\r\n                  max={new Date().toISOString().split(\"T\")[0]}\r\n                />\r\n                {hasAttemptedSubmit && errors[`start_date_${index}`] && (\r\n                  <p className=\"text-sm text-red-500\">\r\n                    {errors[`start_date_${index}`]}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <label className=\"block text-sm font-medium\">\r\n                  End Date{\" \"}\r\n                  {!item.is_current && <span className=\"text-red-500\">*</span>}\r\n                </label>\r\n                <input\r\n                  type=\"date\"\r\n                  value={item.is_current ? \"\" : item.end_date || \"\"}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) =>\r\n                      prev.map((item, i) =>\r\n                        i === index\r\n                          ? { ...item, end_date: e.target.value }\r\n                          : item\r\n                      )\r\n                    )\r\n                  }\r\n                  className={`w-full p-3 bg-gray-50 border rounded-xl ${\r\n                    errors[`end_date_${index}`] ? \"border-red-500\" : \"\"\r\n                  } ${item.is_current ? \"opacity-50 cursor-not-allowed\" : \"\"}`}\r\n                  disabled={item.is_current}\r\n                  required={!item.is_current}\r\n                  min={item.start_date}\r\n                  max={new Date().toISOString().split(\"T\")[0]}\r\n                />\r\n                {hasAttemptedSubmit && errors[`end_date_${index}`] && (\r\n                  <p className=\"text-sm text-red-500\">\r\n                    {errors[`end_date_${index}`]}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <label className=\"flex items-center gap-2\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={item.is_current}\r\n                    onChange={(e) =>\r\n                      setFormData((prev) =>\r\n                        prev.map((item, i) =>\r\n                          i === index\r\n                            ? {\r\n                                ...item,\r\n                                is_current: e.target.checked,\r\n                                end_date: e.target.checked\r\n                                  ? \"\"\r\n                                  : item.end_date || \"\",\r\n                              }\r\n                            : item\r\n                        )\r\n                      )\r\n                    }\r\n                    className=\"rounded border-gray-300\"\r\n                  />\r\n                  <span className=\"text-sm font-medium\">\r\n                    Currently studying here\r\n                  </span>\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"outline\"\r\n          className=\"w-full\"\r\n          onClick={handleAddMore}\r\n        >\r\n          <Plus className=\"w-4 h-4 mr-2\" />\r\n          Add another education\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end gap-4\">\r\n        <Button\r\n          onClick={() => currentStep && setCurrentStep(currentStep - 1)}\r\n          variant=\"outline\"\r\n          disabled={loading}\r\n        >\r\n          Back\r\n        </Button>\r\n        <Button onClick={handleSubmit} disabled={loading}>\r\n          Next\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EducationInfoForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;;;AAPA;;;;;;AASA,MAAM,oBAAoB;;IACxB,MAAM,EACJ,aAAa,EACb,mBAAmB,EACnB,OAAO,EACP,WAAW,EACX,cAAc,EACd,mBAAmB,EACpB,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD;YACE,yBAAyB;YACzB,kBAAkB;YAClB,kBAAkB;YAClB,YAAY;YACZ,UAAU;YACV,YAAY;QACd;KACD;IAED,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,eAAe;gBACjB,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,YACE,cAAc,GAAG;uDAAC,CAAC,MAAQ,CAAC;gCAC1B,GAAG,GAAG;gCACN,kBAAkB,IAAI,gBAAgB,IAAI;gCAC1C,YAAY,IAAI,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gCAC5D,UAAU,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;4BACxD,CAAC;;gBAEL;YACF,OAAO;gBACL,YAAY;oBACV;wBACE,yBAAyB;wBACzB,kBAAkB;wBAClB,kBAAkB;wBAClB,YAAY;wBACZ,UAAU;wBACV,YAAY;oBACd;iBACD;YACH;QACF;sCAAG;QAAC;KAAc;IAElB,MAAM,eAAe;QACnB,MAAM,YAAuC,CAAC;QAC9C,IAAI,cAAc;QAElB,SAAS,OAAO,CAAC,CAAC,MAAM;YACtB,IAAI,CAAC,KAAK,uBAAuB,EAAE;gBACjC,SAAS,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,GAAG;gBACxC,cAAc;YAChB;YAEA,IAAI,CAAC,KAAK,gBAAgB,CAAC,IAAI,IAAI;gBACjC,SAAS,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,GAAG;gBACzC,cAAc;YAChB;YAEA,IAAI,CAAC,KAAK,UAAU,EAAE;gBACpB,SAAS,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG;gBACnC,cAAc;YAChB;YAEA,IAAI,CAAC,KAAK,UAAU,EAAE;gBACpB,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAClB,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG;oBACjC,cAAc;gBAChB,OAAO;oBACL,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU;oBAC1C,MAAM,UAAU,IAAI,KAAK,KAAK,QAAQ;oBAEtC,IAAI,UAAU,WAAW;wBACvB,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,GAC5B;wBACF,cAAc;oBAChB;oBAEA,IAAI,UAAU,IAAI,QAAQ;wBACxB,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG;wBACjC,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,IAAI,oBAAoB;YACtB,UAAU;QACZ;QACA,WAAW;QACX,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,oBAAoB;gBACtB;YACF;QACF;sCAAG;QAAC;QAAU;KAAmB;IAEjC,MAAM,eAAe;QACnB,sBAAsB;QACtB,MAAM,cAAc;QAEpB,IAAI,CAAC,aAAa;YAChB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,aAAa;YACf,IAAI,aAAa,eAAe,cAAc;YAC9C;QACF;QAEA,MAAM,eAAe,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAEnC,IAAI;YACF,MAAM,gBAAgB,SAAS,GAAG,CAAC,CAAC,OAAS,CAAC;oBAC5C,GAAG,IAAI;oBACP,YAAY,IAAI,KAAK,KAAK,UAAU,EAAE,WAAW;oBACjD,UACE,KAAK,UAAU,IAAI,CAAC,KAAK,QAAQ,GAC7B,KACA,IAAI,KAAK,KAAK,QAAQ,EAAE,WAAW;gBAC3C,CAAC;YAED,MAAM,oBAAoB;YAE1B,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,eAAe;YACf,IAAI,aAAa,eAAe,cAAc;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY,CAAC,OAAS;mBACjB;gBACH;oBACE,yBAAyB;oBACzB,kBAAkB;oBAClB,kBAAkB;oBAClB,YAAY;oBACZ,UAAU;oBACV,YAAY;gBACd;aACD;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,YAAY,QAAQ,CAAC,MAAM;QACjC,IAAI,UAAU,EAAE,EAAE;YAChB,MAAM,oBAAoB,UAAU,EAAE;QACxC;QAEA,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAClD,UAAU,CAAC;YACT,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAC;gBAC9B,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG;oBAC7B,OAAO,SAAS,CAAC,IAAI;gBACvB;YACF;YACA,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;;4BAAqC;0CACrC,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;kCAE9C,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;oBACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;4BAAgB,WAAU;;gCACxB,SAAS,MAAM,GAAG,mBACjB,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAItB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAA4B;wDACnB;sEACxB,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,6LAAC;oDACC,WAAW,CAAC,wCAAwC,EAClD,MAAM,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,GAAG,mBAAmB,IACxD;oDACF,OAAO,KAAK,uBAAuB;oDACnC,UAAU,CAAC,IACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAM,IACd,MAAM,QACF;oEACE,GAAG,IAAI;oEACP,yBAAyB,EAAE,MAAM,CAC9B,KAAK;gEACV,IACA;oDAIV,QAAQ;;sEAER,6LAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;gDAEvB,sBAAsB,MAAM,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,kBACvD,6LAAC;oDAAE,WAAU;8DACV,MAAM,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC;;;;;;;;;;;;sDAKzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAA4B;sEAC1B,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAElD,6LAAC;oDACC,WAAU;oDACV,OAAO,KAAK,gBAAgB;oDAC5B,UAAU,CAAC,IACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAM,IACd,MAAM,QACF;oEACE,GAAG,IAAI;oEACP,kBAAkB,EAAE,MAAM,CACvB,KAAK;gEACV,IACA;oDAIV,QAAQ;;sEAER,6LAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;sDAI1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAA4B;sEAC1B,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAElD,6LAAC;oDACC,MAAK;oDACL,OAAO,KAAK,gBAAgB;oDAC5B,UAAU,CAAC,IACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAM,IACd,MAAM,QACF;oEAAE,GAAG,IAAI;oEAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEAAC,IAC5C;oDAIV,WAAW,CAAC,wCAAwC,EAClD,MAAM,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,GAAG,mBAAmB,IACzD;oDACF,aAAY;oDACZ,QAAQ;;;;;;gDAET,sBAAsB,MAAM,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,kBACxD,6LAAC;oDAAE,WAAU;8DACV,MAAM,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC;;;;;;;;;;;;sDAK1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAA4B;sEAChC,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE5C,6LAAC;oDACC,MAAK;oDACL,OAAO,KAAK,UAAU;oDACtB,UAAU,CAAC,IACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAM,IACd,MAAM,QACF;oEAAE,GAAG,IAAI;oEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gEAAC,IACtC;oDAIV,WAAW,CAAC,wCAAwC,EAClD,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,mBAAmB,IACnD;oDACF,QAAQ;oDACR,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;gDAE5C,sBAAsB,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,kBAClD,6LAAC;oDAAE,WAAU;8DACV,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;;;;;;;;;;;;sDAKpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAA4B;wDAClC;wDACR,CAAC,KAAK,UAAU,kBAAI,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEtD,6LAAC;oDACC,MAAK;oDACL,OAAO,KAAK,UAAU,GAAG,KAAK,KAAK,QAAQ,IAAI;oDAC/C,UAAU,CAAC,IACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAM,IACd,MAAM,QACF;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,IACpC;oDAIV,WAAW,CAAC,wCAAwC,EAClD,MAAM,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,mBAAmB,GAClD,CAAC,EAAE,KAAK,UAAU,GAAG,kCAAkC,IAAI;oDAC5D,UAAU,KAAK,UAAU;oDACzB,UAAU,CAAC,KAAK,UAAU;oDAC1B,KAAK,KAAK,UAAU;oDACpB,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;gDAE5C,sBAAsB,MAAM,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,kBAChD,6LAAC;oDAAE,WAAU;8DACV,MAAM,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC;;;;;;;;;;;;sDAKlC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,KAAK,UAAU;wDACxB,UAAU,CAAC,IACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAM,IACd,MAAM,QACF;wEACE,GAAG,IAAI;wEACP,YAAY,EAAE,MAAM,CAAC,OAAO;wEAC5B,UAAU,EAAE,MAAM,CAAC,OAAO,GACtB,KACA,KAAK,QAAQ,IAAI;oEACvB,IACA;wDAIV,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;2BAjMpC;;;;;kCA0MZ,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;;0CAET,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,eAAe,eAAe,cAAc;wBAC3D,SAAQ;wBACR,UAAU;kCACX;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,UAAU;kCAAS;;;;;;;;;;;;;;;;;;AAM1D;GAvaM;;QAQA,wIAAA,CAAA,aAAU;;;KARV;uCAyaS"}}, {"offset": {"line": 2993, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2999, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/forms/services.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useProfile } from \"@hooks/student/useProfile\";\r\nimport { useState } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst ServicesForm = () => {\r\n  const router = useRouter();\r\n  const {\r\n    submitExpectedServices,\r\n    currentStep,\r\n    setCurrentStep,\r\n    loading,\r\n    servicesInfo,\r\n  } = useProfile();\r\n  const [formData, setFormData] = useState<{\r\n    selected_services: string[];\r\n    additional_details: string;\r\n  }>({\r\n    selected_services: servicesInfo?.selected_services || [],\r\n    additional_details: servicesInfo?.additional_details || \"\",\r\n  });\r\n\r\n  const handleServiceSelect = (service: string) => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      selected_services: prev.selected_services.includes(service)\r\n        ? prev.selected_services.filter((s) => s !== service)\r\n        : [...prev.selected_services, service],\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (formData.selected_services.length === 0) {\r\n      toast.error(\"Please select at least one service\");\r\n      return;\r\n    }\r\n\r\n    const loadingToast = toast.loading(\"Submitting service preferences\");\r\n    try {\r\n      await submitExpectedServices(formData);\r\n      toast.dismiss(loadingToast);\r\n      toast.success(\"Service preferences saved successfully!\");\r\n    } catch (error: any) {\r\n      toast.dismiss(loadingToast);\r\n      toast.error(error.message || \"Failed to save service preferences\");\r\n    }\r\n  };\r\n\r\n  const handleBack = () => {\r\n    if (currentStep) {\r\n      setCurrentStep(currentStep - 1);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      <h2 className=\"sm:text-3xl text-2xl font-semibold\">Expected Services</h2>\r\n      <div className=\"space-y-6\">\r\n        <div className=\"space-y-4\">\r\n          <label className=\"block text-sm font-medium\">\r\n            Select Services <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          {[\"Essay Review\", \"Interview Prep\", \"Application Review\"].map(\r\n            (service) => (\r\n              <label\r\n                key={service}\r\n                className=\"flex items-center space-x-3 p-4 border rounded-xl hover:bg-gray-50 cursor-pointer\"\r\n              >\r\n                <input\r\n                  type=\"checkbox\"\r\n                  className=\"h-5 w-5 rounded border-gray-300\"\r\n                  checked={formData.selected_services.includes(service)}\r\n                  onChange={() => handleServiceSelect(service)}\r\n                />\r\n                <span>{service}</span>\r\n              </label>\r\n            )\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <label className=\"block text-sm font-medium\">Other Preferences</label>\r\n          <textarea\r\n            className=\"w-full p-3 bg-gray-50 border rounded-xl h-32\"\r\n            placeholder=\"Tell us more about what you're looking for...\"\r\n            value={formData.additional_details}\r\n            onChange={(e) =>\r\n              setFormData((prev) => ({\r\n                ...prev,\r\n                additional_details: e.target.value,\r\n              }))\r\n            }\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex justify-end gap-4 mt-8\">\r\n          <button\r\n            onClick={handleBack}\r\n            disabled={loading}\r\n            className=\"px-6 py-3 bg-gray-100 rounded-full text-black sm:text-xl text-lg font-medium hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            Back\r\n          </button>\r\n          <button\r\n            onClick={handleSubmit}\r\n            disabled={loading}\r\n            className=\"px-6 py-3 bg-gray-900 rounded-full text-white sm:text-xl text-lg font-medium hover:bg-[#15154B]/90 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            Submit\r\n            <svg\r\n              width=\"24\"\r\n              height=\"24\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                d=\"M9 18L15 12L9 6\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ServicesForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,eAAe;;IACnB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,sBAAsB,EACtB,WAAW,EACX,cAAc,EACd,OAAO,EACP,YAAY,EACb,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGpC;QACD,mBAAmB,cAAc,qBAAqB,EAAE;QACxD,oBAAoB,cAAc,sBAAsB;IAC1D;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,CAAC,QAAQ,CAAC,WAC/C,KAAK,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAM,MAAM,WAC3C;uBAAI,KAAK,iBAAiB;oBAAE;iBAAQ;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,iBAAiB,CAAC,MAAM,KAAK,GAAG;YAC3C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACnC,IAAI;YACF,MAAM,uBAAuB;YAC7B,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,aAAa;YACf,eAAe,cAAc;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;oCAA4B;kDAC3B,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;4BAEhD;gCAAC;gCAAgB;gCAAkB;6BAAqB,CAAC,GAAG,CAC3D,CAAC,wBACC,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,SAAS,iBAAiB,CAAC,QAAQ,CAAC;4CAC7C,UAAU,IAAM,oBAAoB;;;;;;sDAEtC,6LAAC;sDAAM;;;;;;;mCATF;;;;;;;;;;;kCAeb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAA4B;;;;;;0CAC7C,6LAAC;gCACC,WAAU;gCACV,aAAY;gCACZ,OAAO,SAAS,kBAAkB;gCAClC,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;4CACrB,GAAG,IAAI;4CACP,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACpC,CAAC;;;;;;;;;;;;kCAKP,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCACX;kDAEC,6LAAC;wCACC,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,OAAM;kDAEN,cAAA,6LAAC;4CACC,GAAE;4CACF,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GA5HM;;QACW,qIAAA,CAAA,YAAS;QAOpB,wIAAA,CAAA,aAAU;;;KARV;uCA8HS"}}, {"offset": {"line": 3231, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3237, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 3244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3261, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/redirecting-loader.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\n\r\nexport default function RedirectingLoader() {\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    router.push(\"/explore\");\r\n  }, [router]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center items-center h-max md:gap-y-6 gap-y-4\">\r\n      <div className=\"relative inline-flex items-center justify-center md:p-4 p-2\">\r\n        <Image\r\n          src={\"/icons/logo.png\"}\r\n          alt=\"logo\"\r\n          width={40}\r\n          height={40}\r\n          className=\"rounded-full \"\r\n        />\r\n        <div\r\n          className=\"absolute rounded-full border-4  border-t-transparent animate-spin\"\r\n          style={{\r\n            width: \"120%\",\r\n            height: \"120%\",\r\n            borderColor: \"rgba(239, 68, 68, 0.2)\",\r\n            borderTopColor: \"rgb(239, 68, 68)\",\r\n          }}\r\n        />\r\n      </div>\r\n      <h1 className=\"sm:text-2xl text-xl text-center font-semibold italic text-gray-800/60\">\r\n        Profile completed!\r\n      </h1>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,OAAO,IAAI,CAAC;QACd;sCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBAClB;;;;;;;;;;;;0BAGJ,6LAAC;gBAAG,WAAU;0BAAwE;;;;;;;;;;;;AAK5F;GAhCwB;;QACP,qIAAA,CAAA,YAAS;;;KADF"}}, {"offset": {"line": 3345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3371, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/steps.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { PersonalInfoForm, EducationForm, ServicesForm } from \"./forms\";\r\nimport { useProfile } from \"@hooks/student/useProfile\";\r\nimport RedirectingLoader from \"./redirecting-loader\";\r\n\r\nconst OnboardingSteps = () => {\r\n  const { currentStep } = useProfile();\r\n\r\n  const renderForm = () => {\r\n    switch (currentStep) {\r\n      case 1:\r\n        return <PersonalInfoForm />;\r\n      case 2:\r\n        return <EducationForm />;\r\n      // case 3:\r\n      //   return <ServicesForm />;\r\n      case 3:\r\n        return <RedirectingLoader />;\r\n      default:\r\n        return <PersonalInfoForm />;\r\n    }\r\n  };\r\n\r\n  return <div className=\"space-y-8\">{renderForm()}</div>;\r\n};\r\n\r\nexport default OnboardingSteps;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAFA;AAAA;;;AAFA;;;;AAMA,MAAM,kBAAkB;;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAEjC,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,0NAAA,CAAA,mBAAgB;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,wNAAA,CAAA,gBAAa;;;;;YACvB,UAAU;YACV,6BAA6B;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uKAAA,CAAA,UAAiB;;;;;YAC3B;gBACE,qBAAO,6LAAC,0NAAA,CAAA,mBAAgB;;;;;QAC5B;IACF;IAEA,qBAAO,6LAAC;QAAI,WAAU;kBAAa;;;;;;AACrC;GAnBM;;QACoB,wIAAA,CAAA,aAAU;;;KAD9B;uCAqBS"}}, {"offset": {"line": 3440, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3446, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@components/lib/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf;KAVS"}}, {"offset": {"line": 3470, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3476, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/loading-skeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@components/ui/skeleton\";\r\n\r\nexport function OnboardingSkeleton() {\r\n  return (\r\n    <div className=\"w-full flex flex-col justify-start md:flex-row md:items-start md:justify-center gap-6\">\r\n      {/* Left sidebar skeleton */}\r\n      <div className=\"w-full md:w-[280px] xl:w-[320px] bg-white rounded-2xl shadow-lg p-6 md:sticky md:top-8\">\r\n        <div className=\"space-y-6\">\r\n          {[1, 2, 3].map((_, index) => (\r\n            <div key={index} className=\"flex items-center gap-3\">\r\n              <div\r\n                className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n                  index === 0\r\n                    ? \"bg-blue-600 text-white\"\r\n                    : \"border-2 border-gray-200\"\r\n                }`}\r\n              >\r\n                {index + 1}\r\n              </div>\r\n              <Skeleton className=\"h-4 w-32\" />\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content skeleton */}\r\n      <div className=\"flex-1 bg-white rounded-2xl shadow-lg p-6 sm:p-8\">\r\n        <div className=\"space-y-8\">\r\n          {/* Title and subtitle */}\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Skeleton className=\"h-8 w-48\" />\r\n              <Skeleton className=\"h-4 w-full max-w-[600px]\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Form fields */}\r\n          <div className=\"space-y-6\">\r\n            {/* Section title */}\r\n            <Skeleton className=\"h-6 w-40\" />\r\n\r\n            {/* Name fields */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-24\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-24\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Nationality and Country fields */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-24\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-24\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Gender and Date fields */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-24\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-24\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Buttons */}\r\n          <div className=\"flex justify-between pt-6\">\r\n            <Skeleton className=\"h-10 w-24\" />\r\n            <Skeleton className=\"h-10 w-24\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,sBACjB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCACC,WAAW,CAAC,sDAAsD,EAChE,UAAU,IACN,2BACA,4BACJ;8CAED,QAAQ;;;;;;8CAEX,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;2BAVZ;;;;;;;;;;;;;;;0BAiBhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKxB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CAGpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAM1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhC;KArFgB"}}, {"offset": {"line": 3793, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3799, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport OnboardingSteps from \"@/app/components/student/onboarding/steps\";\r\nimport { useProfile } from \"@hooks/student/useProfile\";\r\nimport { OnboardingSkeleton } from \"./loading-skeleton\";\r\nimport { useEffect } from \"react\";\r\n\r\nconst steps = [\"Personal information\", \"Educational background\"] as const;\r\n\r\nexport default function CreateProfile() {\r\n  const { currentStep, figureCurrentStep } = useProfile();\r\n\r\n  useEffect(() => {\r\n    figureCurrentStep();\r\n  }, [figureCurrentStep]);\r\n\r\n  return !currentStep ? (\r\n    <OnboardingSkeleton />\r\n  ) : (\r\n    <div className=\"w-full flex flex-col justify-start md:flex-row md:items-start md:justify-center gap-6\">\r\n      {/* Left sidebar with steps */}\r\n      <div className=\"w-full md:w-[280px] xl:w-[320px] bg-white rounded-2xl shadow-lg p-6 md:sticky md:top-8\">\r\n        <div className=\"space-y-6\">\r\n          {steps.map((label, index) => (\r\n            <div key={index} className=\"flex items-center gap-3\">\r\n              <div\r\n                className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n                  index + 1 <= currentStep\r\n                    ? \"bg-blue-600 text-white\"\r\n                    : \"border-2\"\r\n                }`}\r\n              >\r\n                {index + 1}\r\n              </div>\r\n              <span\r\n                className={\r\n                  index + 1 <= currentStep ? \"font-medium\" : \"opacity-60\"\r\n                }\r\n              >\r\n                {label}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 bg-white rounded-2xl shadow-lg p-6 sm:p-8\">\r\n        <OnboardingSteps />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,QAAQ;IAAC;IAAwB;CAAyB;AAEjD,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAkB;IAEtB,OAAO,CAAC,4BACN,6LAAC,qKAAA,CAAA,qBAAkB;;;;6BAEnB,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,OAAO,sBACjB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCACC,WAAW,CAAC,sDAAsD,EAChE,QAAQ,KAAK,cACT,2BACA,YACJ;8CAED,QAAQ;;;;;;8CAEX,6LAAC;oCACC,WACE,QAAQ,KAAK,cAAc,gBAAgB;8CAG5C;;;;;;;2BAfK;;;;;;;;;;;;;;;0BAuBhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uJAAA,CAAA,UAAe;;;;;;;;;;;;;;;;AAIxB;GA3CwB;;QACqB,wIAAA,CAAA,aAAU;;;KAD/B"}}, {"offset": {"line": 3904, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3910, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/onboarding/onboarding-head-wrapper.tsx"], "sourcesContent": ["import Head from \"next/head\";\r\n\r\nexport default function OnboardingHead() {\r\n  return (\r\n    <Head>\r\n      <title>{`Setup Your Profile | AdmitPath`}</title>\r\n      <meta\r\n        name=\"description\"\r\n        content={`Onboarding page for setting up your profile. This is the first step to getting started with our service. | AdmitPath`}\r\n      />\r\n    </Head>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC,uKAAA,CAAA,UAAI;;0BACH,6LAAC;0BAAO,CAAC,8BAA8B,CAAC;;;;;;0BACxC,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC,oHAAoH,CAAC;;;;;;;;;;;;AAIvI;KAVwB"}}, {"offset": {"line": 3948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3954, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/onboarding/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport CreateProfile from \"@/app/components/student/onboarding\";\r\nimport OnboardingHead from \"@/app/components/student/onboarding/onboarding-head-wrapper\";\r\n\r\nexport default function CreateProfilePage() {\r\n  return (\r\n    <>\r\n      <OnboardingHead />\r\n      <CreateProfile />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE;;0BACE,6LAAC,+KAAA,CAAA,UAAc;;;;;0BACf,6LAAC,uJAAA,CAAA,UAAa;;;;;;;AAGpB;KAPwB"}}, {"offset": {"line": 3986, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}