export interface PersonalInfo {
  nationality: string;
  country_of_residence: string;
  gender: string;
  date_of_birth: string;
  first_name?: string;
  last_name?: string;
  goals?: string;
}

export interface EducationInfo {
  id?: number;
  student_id?: number;
  current_education_level:
    | ""
    | "high_school"
    | "bachelors"
    | "masters"
    | "phd"
    | "other";
  institution_name: string;
  institution_type: "school" | "university" | "other";
  start_date: string;
  end_date?: string; // Make it optional
  is_current: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface NewEducationInfo
  extends Omit<
    EducationInfo,
    "id" | "student_id" | "created_at" | "updated_at"
  > {}

// Response type
export interface EducationResponse extends EducationInfo {
  id: number;
  student_id: number;
  created_at: string;
  updated_at: string;
}

export interface PersonalInfoResponse extends PersonalInfo {
  id: number;
  student_id: number;
}

export interface ServiceInfo {
  selected_services: string[];
  additional_details: string;
}

export interface UserInfo {
  user_id: number;
  student_id: number | null;
  counselor_id: number | null;

  firstName: string;
  lastName: string;
  email: string;
  userType: string;
  isProfileComplete: boolean;
  profile_picture_url: string | null;
}

export interface ProfileState {
  loading: boolean;
  error: string | null;
  currentStep: number | null;
  userInfo: UserInfo | null;
  personalInfo: PersonalInfo | null;
  educationInfo: EducationInfo[] | null;
  servicesInfo: ServiceInfo | null;
  setCurrentStep: (step: number) => void;
  fetchUserInfo: () => Promise<void>;
  fetchPersonalInfo: () => Promise<void>;
  fetchEducationalBackground: () => Promise<void>;
  fetchServicesInfo: () => Promise<void>;
  figureCurrentStep: () => Promise<void>;
  submitPersonalInfo: (data: PersonalInfo) => Promise<void>;
  submitEducationInfo: (data: EducationInfo[]) => any;
  updateEducationInfo: (
    id: number,
    data: Partial<EducationInfo>
  ) => Promise<void>;
  deleteEducationInfo: (id: number) => Promise<void>;
  submitExpectedServices: (data: ServiceInfo) => Promise<void>;
  updateprofile_picture_url: (url: string | null) => void;
  clearError: () => void;
}

export const educationLevelMap = {
  high_school: "High School",
  bachelors: "Bachelor's Degree",
  masters: "Master's Degree",
  phd: "Ph.D.",
  other: "Other",
} as const;

export const genderMap = {
  male: "Male",
  female: "Female",
  other: "Other",
} as const;

export type EducationLevel = keyof typeof educationLevelMap;
export type Gender = keyof typeof genderMap;

export const humanizeValue = (
  value: string,
  type: "current_education_level" | "gender"
): string => {
  const map =
    type === "current_education_level" ? educationLevelMap : genderMap;
  return map[value as keyof typeof map] || value;
};
