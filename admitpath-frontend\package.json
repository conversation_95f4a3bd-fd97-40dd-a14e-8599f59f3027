{"name": "admitpath-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env PORT=3001 next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "axios": "^1.7.9", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "country-flag-icons": "^1.5.18", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "framer-motion": "^12.0.6", "lottie-react": "^2.4.0", "lucide-react": "^0.469.0", "next": "15.1.0", "next-auth": "^4.24.11", "next-query-params": "^5.1.0", "pusher": "^5.2.0", "pusher-http-edge": "^0.4.0", "pusher-js": "^8.4.0-rc2", "query-string": "^9.1.1", "react": "^18.2.0", "react-big-calendar": "^1.17.1", "react-calendly": "^4.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-infinite-logo-slider": "^1.1.4", "react-redux": "^9.2.0", "react-select": "^5.10.0", "react-toastify": "^11.0.1", "swiper": "^11.2.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-query-params": "^2.2.1", "world-universities": "^1.0.0", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-big-calendar": "^1.16.0", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5"}, "browser": {"crypto": false}}