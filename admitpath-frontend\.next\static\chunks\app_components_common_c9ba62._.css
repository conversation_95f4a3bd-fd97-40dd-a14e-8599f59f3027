/* [project]/app/components/common/passwordInputField/index.module.css [app-client] (css) */
.index-module__zj4Tga__passwordContainer {
  position: relative;
}

.index-module__zj4Tga__inputIcon {
  position: absolute;
  right: 1rem;
  top: 2.3rem;
  z-index: 10;
  cursor: pointer;
  border-style: none;
  background-color: #0000;
  padding: .25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.index-module__zj4Tga__eyeIcon {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.index-module__zj4Tga__eyeIcon:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.index-module__zj4Tga__passwordTooltip {
  position: absolute;
  top: 2.5rem;
  right: 0;
  z-index: 10;
  margin-top: .5rem;
  width: 250px;
  border-radius: .5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1rem;
  font-size: .875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.index-module__zj4Tga__passwordTooltip ul {
  list-style-type: none;
}

.index-module__zj4Tga__passwordTooltip ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.index-module__zj4Tga__passwordTooltip li:before {
  content: "✔";
  margin-right: .5rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}


/* [project]/app/components/common/inputField/index.module.css [app-client] (css) */
.index-module__8yU72G__container {
  display: flex;
  min-width: 240px;
  flex: 1;
  flex-shrink: 1;
  flex-basis: 0;
  flex-direction: column;
}

.index-module__8yU72G__label {
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}

.index-module__8yU72G__required {
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}

.index-module__8yU72G__input {
  margin-top: .25rem;
  width: 100%;
  border-radius: .25rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: .75rem;
  padding-bottom: .75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -.025em;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.index-module__8yU72G__input:focus {
  outline: 2px solid #0000;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.index-module__8yU72G__input[type="password"] {
  padding-right: 3rem;
}

.index-module__8yU72G__input.index-module__8yU72G__error {
  --tw-border-opacity: 1;
  border-color: rgb(244 63 94 / var(--tw-border-opacity, 1));
}

.index-module__8yU72G__input.index-module__8yU72G__error:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 63 94 / var(--tw-ring-opacity, 1));
}

.index-module__8yU72G__errorMessage {
  margin-top: .25rem;
  font-size: .875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}


/* [project]/app/components/common/button/index.module.css [app-client] (css) */
.index-module__Mmy9Oa__buttonBase {
  margin-top: auto;
  margin-bottom: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: .75rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: .5rem;
  padding-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
  letter-spacing: .025em;
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.index-module__Mmy9Oa__buttonPrimary {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.index-module__Mmy9Oa__buttonSecondary {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.index-module__Mmy9Oa__buttonBase:disabled {
  cursor: not-allowed;
  opacity: .5;
}

.index-module__Mmy9Oa__buttonContent {
  display: flex;
  align-items: center;
  gap: .5rem;
  text-wrap: nowrap;
}

.index-module__Mmy9Oa__icon {
  width: .75rem;
}


/*# sourceMappingURL=app_components_common_c9ba62._.css.map*/
