{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/StarBackground.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef } from \"react\";\r\n\r\nconst StarBackground = () => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n\r\n  useEffect(() => {\r\n    const canvas = canvasRef.current;\r\n    if (!canvas) return;\r\n\r\n    const ctx = canvas.getContext(\"2d\");\r\n    if (!ctx) return;\r\n\r\n    let stars: Array<{ x: number; y: number; size: number; brightness: number }> = [];\r\n    let mouseX = window.innerWidth / 2;\r\n    let mouseY = window.innerHeight / 2;\r\n    let animationFrameId: number;\r\n\r\n    const createStars = () => {\r\n      stars = [];\r\n      const numberOfStars = 100;\r\n      \r\n      for (let i = 0; i < numberOfStars; i++) {\r\n        // Make about 30% of stars bright by default\r\n        const brightness = Math.random() < 0.3 ? 1 : 0.3;\r\n        stars.push({\r\n          x: Math.random() * canvas.width,\r\n          y: Math.random() * canvas.height,\r\n          size: 1.5,\r\n          brightness\r\n        });\r\n      }\r\n    };\r\n\r\n    const drawStars = () => {\r\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n      \r\n      stars.forEach((star) => {\r\n        const distance = Math.sqrt(\r\n          Math.pow(mouseX - star.x, 2) + Math.pow(mouseY - star.y, 2)\r\n        );\r\n        \r\n        // If mouse is nearby, brighten the star, otherwise use its default brightness\r\n        const opacity = distance < 100 ? 1 : star.brightness;\r\n        ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;\r\n        \r\n        ctx.beginPath();\r\n        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);\r\n        ctx.fill();\r\n      });\r\n\r\n      animationFrameId = requestAnimationFrame(drawStars);\r\n    };\r\n\r\n    const handleResize = () => {\r\n      canvas.width = canvas.offsetWidth;\r\n      canvas.height = canvas.offsetHeight;\r\n      createStars();\r\n    };\r\n\r\n    const handleMouseMove = (e: MouseEvent) => {\r\n      const rect = canvas.getBoundingClientRect();\r\n      mouseX = e.clientX - rect.left;\r\n      mouseY = e.clientY - rect.top;\r\n    };\r\n\r\n    canvas.width = canvas.offsetWidth;\r\n    canvas.height = canvas.offsetHeight;\r\n    \r\n    window.addEventListener(\"resize\", handleResize);\r\n    canvas.addEventListener(\"mousemove\", handleMouseMove);\r\n    \r\n    createStars();\r\n    drawStars();\r\n\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n      canvas.removeEventListener(\"mousemove\", handleMouseMove);\r\n      cancelAnimationFrame(animationFrameId);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <canvas\r\n      ref={canvasRef}\r\n      className=\"absolute top-0 left-0 w-full h-full\"\r\n    />\r\n  );\r\n};\r\n\r\nexport default StarBackground;\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,iBAAiB;;IACrB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,IAAI,QAA2E,EAAE;YACjF,IAAI,SAAS,OAAO,UAAU,GAAG;YACjC,IAAI,SAAS,OAAO,WAAW,GAAG;YAClC,IAAI;YAEJ,MAAM;wDAAc;oBAClB,QAAQ,EAAE;oBACV,MAAM,gBAAgB;oBAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,4CAA4C;wBAC5C,MAAM,aAAa,KAAK,MAAM,KAAK,MAAM,IAAI;wBAC7C,MAAM,IAAI,CAAC;4BACT,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;4BAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;4BAChC,MAAM;4BACN;wBACF;oBACF;gBACF;;YAEA,MAAM;sDAAY;oBAChB,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,MAAM,OAAO;8DAAC,CAAC;4BACb,MAAM,WAAW,KAAK,IAAI,CACxB,KAAK,GAAG,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,SAAS,KAAK,CAAC,EAAE;4BAG3D,8EAA8E;4BAC9E,MAAM,UAAU,WAAW,MAAM,IAAI,KAAK,UAAU;4BACpD,IAAI,SAAS,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;4BAEjD,IAAI,SAAS;4BACb,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;4BAChD,IAAI,IAAI;wBACV;;oBAEA,mBAAmB,sBAAsB;gBAC3C;;YAEA,MAAM;yDAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,WAAW;oBACjC,OAAO,MAAM,GAAG,OAAO,YAAY;oBACnC;gBACF;;YAEA,MAAM;4DAAkB,CAAC;oBACvB,MAAM,OAAO,OAAO,qBAAqB;oBACzC,SAAS,EAAE,OAAO,GAAG,KAAK,IAAI;oBAC9B,SAAS,EAAE,OAAO,GAAG,KAAK,GAAG;gBAC/B;;YAEA,OAAO,KAAK,GAAG,OAAO,WAAW;YACjC,OAAO,MAAM,GAAG,OAAO,YAAY;YAEnC,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,aAAa;YAErC;YACA;YAEA;4CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,aAAa;oBACxC,qBAAqB;gBACvB;;QACF;mCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;;;;;AAGhB;GArFM;KAAA;uCAuFS"}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      ref={ref}\r\n      className={cn(sheetVariants({ side }), className)}\r\n      {...props}\r\n    >\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n      {children}\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAEA;AAGA;AAJA;AAEA;AALA;;;;;;;AASA,MAAM,QAAQ,sKAAe,IAAI;AAEjC,MAAM,eAAe,sKAAe,OAAO;AAE3C,MAAM,aAAa,sKAAe,KAAK;AAEvC,MAAM,cAAc,sKAAe,MAAM;AAEzC,MAAM,6BAAe,8JAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAe,OAAO;QACrB,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,aAAa,WAAW,GAAG,sKAAe,OAAO,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,8JAAM,UAAU,OAGnC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAe,OAAO;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;kCAET,6LAAC,sKAAe,KAAK;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;oBAE3B;;;;;;;;;;;;;;AAIP,aAAa,WAAW,GAAG,sKAAe,OAAO,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAe,KAAK;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,sKAAe,KAAK,CAAC,WAAW;AAEzD,MAAM,iCAAmB,8JAAM,UAAU,OAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAe,WAAW;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,sKAAe,WAAW,CAAC,WAAW"}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,eAAe,gLAAsB,IAAI;AAE/C,MAAM,sBAAsB,gLAAsB,OAAO;AAEzD,MAAM,oBAAoB,gLAAsB,KAAK;AAErD,MAAM,qBAAqB,gLAAsB,MAAM;AAEvD,MAAM,kBAAkB,gLAAsB,GAAG;AAEjD,MAAM,yBAAyB,gLAAsB,UAAU;AAE/D,MAAM,uCAAyB,8JAAM,UAAU,MAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,8JAAM,UAAU,OAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,8JAAM,UAAU,OAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,gLAAsB,MAAM;kBAC3B,cAAA,6LAAC,gLAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,wGACA,oVACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gLAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,8JAAM,UAAU,OAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gLAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,8JAAM,UAAU,OAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,gLAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,gLAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,8JAAM,UAAU,QAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,gLAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/apiClient.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios from \"axios\";\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n    Accept: \"application/json\",\r\n  },\r\n  // withCredentials: true,\r\n});\r\n\r\n// Request interceptor to handle dynamic headers or logging\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem(\"access_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle common errors globally\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle specific error responses (e.g., 401 Unauthorized)\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        if (\r\n          window.location.pathname.startsWith(\"/student\") ||\r\n          window.location.pathname.startsWith(\"/counselor\") ||\r\n          localStorage.getItem(\"access_token\")\r\n        ) {\r\n          console.error(\"Unauthorized. Redirecting to login...\");\r\n          localStorage.removeItem(\"access_token\");\r\n          window.location.href = \"/auth/login\";\r\n        }\r\n      } else if (status >= 500) {\r\n        console.error(\r\n          \"Server error:\",\r\n          error.response.data.message || \"Internal Server Error\"\r\n        );\r\n      }\r\n    } else {\r\n      console.error(\"Network error:\", error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAEA;AAGW;AALX;;AAIA,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,6DAAwC;IACjD,SAAS;QACP,gBAAgB;QAChB,QAAQ;IACV;AAEF;AAEA,2DAA2D;AAC3D,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wDAAwD;AACxD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,2DAA2D;IAC3D,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,IACE,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,eACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,aAAa,OAAO,CAAC,iBACrB;gBACA,QAAQ,KAAK,CAAC;gBACd,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAEnC;IACF,OAAO;QACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;IAC/C;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa"}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useProfile.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  EducationInfo,\r\n  PersonalInfo,\r\n  ProfileState,\r\n} from \"@/app/types/student/profile\";\r\n\r\n// Create a store for managing student profile state\r\nexport const useProfile = create<ProfileState>((set, get) => ({\r\n  loading: false,\r\n  error: null,\r\n  currentStep: null,\r\n  userInfo: null,\r\n  personalInfo: null,\r\n  educationInfo: null,\r\n  servicesInfo: null,\r\n  setCurrentStep: (step) => set({ currentStep: step }),\r\n\r\n  fetchUserInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/user/me?timezone=${Intl.DateTimeFormat().resolvedOptions().timeZone}`\r\n      );\r\n      set({ userInfo: response.data, loading: false });\r\n    } catch (error) {\r\n      set({ error: \"Failed to fetch user info\", loading: false });\r\n    }\r\n  },\r\n\r\n  fetchPersonalInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/student/profile/personal-info\");\r\n      set({ personalInfo: response.data, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch personal info\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchEducationalBackground: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        \"/student/profile/educational-background\"\r\n      );\r\n      set({ educationInfo: response.data.items, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail ||\r\n          \"Failed to fetch educational background\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchServicesInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/student/profile/services-info\");\r\n      set({ servicesInfo: response.data, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch Services info\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  figureCurrentStep: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const {\r\n        fetchPersonalInfo,\r\n        fetchEducationalBackground,\r\n        // fetchServicesInfo,\r\n      } = get();\r\n\r\n      await Promise.all([\r\n        fetchPersonalInfo(),\r\n        fetchEducationalBackground(),\r\n        // fetchServicesInfo(),\r\n      ]);\r\n\r\n      // const { personalInfo, educationInfo, servicesInfo } = get();\r\n      const { personalInfo, educationInfo } = get();\r\n\r\n      let currentStep = 1;\r\n      if (personalInfo) {\r\n        currentStep = 2;\r\n        if (educationInfo && educationInfo.length > 0) {\r\n          currentStep = 3;\r\n          // if (servicesInfo) {\r\n          //   currentStep = 4;\r\n          // }\r\n        }\r\n      }\r\n\r\n      set({ currentStep, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail || \"Failed to determine current step\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  submitPersonalInfo: async (data: PersonalInfo) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const { personalInfo } = get();\r\n      // If data exists, use PUT, else use POST\r\n      const method = personalInfo ? \"put\" : \"post\";\r\n      await apiClient[method](\"/student/profile/personal-info\", data);\r\n      set({ personalInfo: data, currentStep: 2 });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  submitEducationInfo: async (data: EducationInfo[]) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const results = await Promise.all(\r\n        data.map(async (item) => {\r\n          const payload = {\r\n            ...item,\r\n            end_date: item.is_current ? undefined : item.end_date,\r\n          };\r\n\r\n          if (item.id) {\r\n            // Update existing entry\r\n            const response = await apiClient.put(\r\n              `/student/profile/educational-background/${item.id}`,\r\n              payload\r\n            );\r\n            return response.data;\r\n          } else {\r\n            // Create new entry\r\n            const response = await apiClient.post(\r\n              \"/student/profile/educational-background\",\r\n              payload\r\n            );\r\n            return response.data;\r\n          }\r\n        })\r\n      );\r\n\r\n      set({\r\n        educationInfo: results,\r\n        currentStep: Math.max(get().currentStep || 0, 3),\r\n        loading: false,\r\n      });\r\n\r\n      return results;\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail || \"Failed to submit education info\",\r\n        loading: false,\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  updateEducationInfo: async (id: number, data: Partial<EducationInfo>) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.put(\r\n        `/student/profile/educational-background/${id}`,\r\n        data\r\n      );\r\n\r\n      // Update the specific education item in the list\r\n      set((state) => ({\r\n        educationInfo:\r\n          state.educationInfo?.map((item) =>\r\n            item.id === id ? response.data : item\r\n          ) || null,\r\n      }));\r\n      toast.success(\"Education info updated successfully\");\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  deleteEducationInfo: async (id: number) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.delete(`/student/profile/educational-background/${id}`);\r\n\r\n      // Remove the education item from the list\r\n      set((state) => ({\r\n        educationInfo:\r\n          state.educationInfo?.filter((item) => item.id !== id) || null,\r\n      }));\r\n      toast.success(\"Education info deleted successfully\");\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  submitExpectedServices: async (data) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const { servicesInfo } = get();\r\n      const method = servicesInfo ? \"put\" : \"post\";\r\n      await apiClient[method](\"/student/profile/expected-services\", data);\r\n      set({ servicesInfo: data, currentStep: 4 });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      toast.error(error.response?.data?.detail || \"Something went wrong\");\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  updateprofile_picture_url: (url: string | null) => {\r\n    const currentUserInfo = get().userInfo;\r\n    if (currentUserInfo) {\r\n      set({ userInfo: { ...currentUserInfo, profile_picture_url: url } });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AACA;AAFA;AAFA;;;;AAYO,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QAC5D,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAElD,eAAe;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAAE;gBAEzE,IAAI;oBAAE,UAAU,SAAS,IAAI;oBAAE,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;oBAA6B,SAAS;gBAAM;YAC3D;QACF;QAEA,mBAAmB;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;YACpD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,4BAA4B;YAC1B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC;gBAEF,IAAI;oBAAE,eAAe,SAAS,IAAI,CAAC,KAAK;oBAAE,SAAS;gBAAM;YAC3D,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACF,SAAS;gBACX;YACF;QACF;QAEA,mBAAmB;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;YACpD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,mBAAmB;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EACJ,iBAAiB,EACjB,0BAA0B,EAE3B,GAAG;gBAEJ,MAAM,QAAQ,GAAG,CAAC;oBAChB;oBACA;iBAED;gBAED,+DAA+D;gBAC/D,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;gBAExC,IAAI,cAAc;gBAClB,IAAI,cAAc;oBAChB,cAAc;oBACd,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;wBAC7C,cAAc;oBACd,sBAAsB;oBACtB,qBAAqB;oBACrB,IAAI;oBACN;gBACF;gBAEA,IAAI;oBAAE;oBAAa,SAAS;gBAAM;YACpC,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UAAU;oBAClC,SAAS;gBACX;YACF;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,yCAAyC;gBACzC,MAAM,SAAS,eAAe,QAAQ;gBACtC,MAAM,mHAAA,CAAA,UAAS,CAAC,OAAO,CAAC,kCAAkC;gBAC1D,IAAI;oBAAE,cAAc;oBAAM,aAAa;gBAAE;YAC3C,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,qBAAqB,OAAO;YAC1B,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,KAAK,GAAG,CAAC,OAAO;oBACd,MAAM,UAAU;wBACd,GAAG,IAAI;wBACP,UAAU,KAAK,UAAU,GAAG,YAAY,KAAK,QAAQ;oBACvD;oBAEA,IAAI,KAAK,EAAE,EAAE;wBACX,wBAAwB;wBACxB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,wCAAwC,EAAE,KAAK,EAAE,EAAE,EACpD;wBAEF,OAAO,SAAS,IAAI;oBACtB,OAAO;wBACL,mBAAmB;wBACnB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,2CACA;wBAEF,OAAO,SAAS,IAAI;oBACtB;gBACF;gBAGF,IAAI;oBACF,eAAe;oBACf,aAAa,KAAK,GAAG,CAAC,MAAM,WAAW,IAAI,GAAG;oBAC9C,SAAS;gBACX;gBAEA,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UAAU;oBAClC,SAAS;gBACX;gBACA,MAAM;YACR;QACF;QAEA,qBAAqB,OAAO,IAAY;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,wCAAwC,EAAE,IAAI,EAC/C;gBAGF,iDAAiD;gBACjD,IAAI,CAAC,QAAU,CAAC;wBACd,eACE,MAAM,aAAa,EAAE,IAAI,CAAC,OACxB,KAAK,EAAE,KAAK,KAAK,SAAS,IAAI,GAAG,SAC9B;oBACT,CAAC;gBACD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,qBAAqB,OAAO;YAC1B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,wCAAwC,EAAE,IAAI;gBAEtE,0CAA0C;gBAC1C,IAAI,CAAC,QAAU,CAAC;wBACd,eACE,MAAM,aAAa,EAAE,OAAO,CAAC,OAAS,KAAK,EAAE,KAAK,OAAO;oBAC7D,CAAC;gBACD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,SAAS,eAAe,QAAQ;gBACtC,MAAM,mHAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sCAAsC;gBAC9D,IAAI;oBAAE,cAAc;oBAAM,aAAa;gBAAE;YAC3C,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,2BAA2B,CAAC;YAC1B,MAAM,kBAAkB,MAAM,QAAQ;YACtC,IAAI,iBAAiB;gBACnB,IAAI;oBAAE,UAAU;wBAAE,GAAG,eAAe;wBAAE,qBAAqB;oBAAI;gBAAE;YACnE;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@components/lib/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf;KAVS"}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/auth.ts"], "sourcesContent": ["import { toast } from 'react-toastify';\r\nimport apiClient from '@lib/apiClient';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;\r\n\r\ninterface AuthError extends Error {\r\n  isAuthError?: boolean;\r\n}\r\n\r\nexport const handleGoogleLogin = async (token: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/google', \r\n      userType \r\n        ? { token, user_type: userType }  // Signup case\r\n        : { token }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('Google auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const handleLinkedInLogin = async (code: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/linkedin',\r\n      userType \r\n        ? { code, user_type: userType }  // Signup case\r\n        : { code }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('LinkedIn auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const initiateLinkedInLogin = (userType?: string) => {\r\n  const LINKEDIN_CLIENT_ID = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;\r\n  const LINKEDIN_REDIRECT_URI = process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI;\r\n  const scope = 'openid profile email';\r\n  \r\n  // Only include state (userType) for signup flow\r\n  const stateParam = userType ? `&state=${userType}` : '';\r\n  const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${LINKEDIN_REDIRECT_URI}${stateParam}&scope=${scope}`;\r\n  \r\n  window.location.href = url;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AAEgB;;AAAhB,MAAM;AAMC,MAAM,oBAAoB,OAAO,OAAe;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBACpC,WACI;YAAE;YAAO,WAAW;QAAS,EAAG,cAAc;WAC9C;YAAE;QAAM,EAAG,aAAa;;QAG9B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,MAAc;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,kBACpC,WACI;YAAE;YAAM,WAAW;QAAS,EAAG,cAAc;WAC7C;YAAE;QAAK,EAAG,aAAa;;QAG7B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,qBAAqB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,8BAA8B;IACrE,MAAM,wBAAwB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iCAAiC;IAC3E,MAAM,QAAQ;IAEd,gDAAgD;IAChD,MAAM,aAAa,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG;IACrD,MAAM,MAAM,CAAC,6EAA6E,EAAE,mBAAmB,cAAc,EAAE,wBAAwB,WAAW,OAAO,EAAE,OAAO;IAElL,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB"}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  handleGoogleLogin,\r\n  handleLinkedInLogin,\r\n  initiateLinkedInLogin,\r\n} from \"@/app/utils/auth\";\r\n\r\ninterface User {\r\n  avatar?: string;\r\n  firstName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface AuthState {\r\n  token: string | null;\r\n  user: User | null;\r\n  userType: string | null;\r\n  loading: boolean;\r\n  login: (username: string, password: string) => Promise<void>;\r\n  signup: (data: SignupData) => Promise<void>;\r\n  verifyEmail: (email: string, code: string) => Promise<void>;\r\n  forgotPassword: (email: string) => Promise<void>;\r\n  resetPassword: (\r\n    email: string,\r\n    code: string,\r\n    newPassword: string\r\n  ) => Promise<void>;\r\n  logout: () => void;\r\n  signupData: Partial<SignupData> | null;\r\n  initiateSignup: (\r\n    data: { first_name: string; last_name: string; email: string },\r\n    userType: \"student\" | \"counselor\"\r\n  ) => Promise<void>;\r\n  resetSignupData: () => void;\r\n  completeSignup: (password: string) => Promise<string>;\r\n  resendVerificationCode: (email: string) => Promise<void>;\r\n  verifySignupCode: (email: string, code: string) => Promise<void>;\r\n  googleAuth: (\r\n    token: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  linkedinAuth: (\r\n    code: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => void;\r\n  isAuthenticated: boolean;\r\n  auth: () => boolean;\r\n}\r\n\r\ninterface SignupData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  userType: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const useAuth = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      token: null as string | null,\r\n      user: null as User | null,\r\n      userType: null as string | null,\r\n      loading: false,\r\n      signupData: null,\r\n      isAuthenticated: false,\r\n\r\n      auth: () => {\r\n        const token = localStorage.getItem(\"access_token\");\r\n        const isAuthenticated = !!token;\r\n        set({ isAuthenticated });\r\n        return isAuthenticated;\r\n      },\r\n\r\n      login: async (username, password) => {\r\n        try {\r\n          set({ loading: true });\r\n          const formData = new URLSearchParams();\r\n          formData.append(\"username\", username);\r\n          formData.append(\"password\", password);\r\n\r\n          const response = await apiClient.post(\"/auth/signin\", formData, {\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n            },\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n          });\r\n\r\n          toast.success(\"Logged in successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Login failed\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      signup: async (data) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\r\n            `/auth/signup/${data.userType}`,\r\n            {\r\n              email: data.email,\r\n              password: data.password,\r\n              first_name: data.first_name,\r\n              last_name: data.last_name,\r\n            }\r\n          );\r\n\r\n          if (response.data.id) {\r\n            localStorage.setItem(\"email\", data.email);\r\n            toast.success(\"Verification code sent to your email!\");\r\n          }\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Signup failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifyEmail: async (email, code) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/verify-code\", { email, code });\r\n          toast.success(\"Code verified successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Verification failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      forgotPassword: async (email) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/forgot-password\", { email });\r\n          toast.success(\"Reset code sent to your email!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg ||\r\n              \"Failed to send reset code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetPassword: async (email, code, newPassword) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/reset-password\", {\r\n            email,\r\n            code,\r\n            new_password: newPassword,\r\n          });\r\n          toast.success(\"Password reset successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Password reset failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      logout: () => {\r\n        localStorage.removeItem(\"access_token\");\r\n        set({ token: null, userType: null, isAuthenticated: false });\r\n      },\r\n\r\n      initiateSignup: async (data, userType = \"student\") => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/initiate\", {\r\n            ...data,\r\n            user_type: userType,\r\n          });\r\n          set({ signupData: { ...data, userType } });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to initiate signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetSignupData: () => {\r\n        set({ signupData: null });\r\n      },\r\n\r\n      completeSignup: async (password: string) => {\r\n        const { signupData } = get();\r\n        if (!signupData?.email) {\r\n          toast.error(\"Missing signup data\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/complete\", {\r\n            email: signupData.email,\r\n            password,\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n\r\n          // Store token and update auth state\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n            signupData: null,\r\n          });\r\n\r\n          toast.success(\"Account created successfully!\");\r\n\r\n          // Return user type so pages can redirect appropriately\r\n          return user_type;\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to complete signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resendVerificationCode: async (email: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/resend\", {\r\n            email,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Failed to resend code\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifySignupCode: async (email: string, code: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/verify\", {\r\n            email,\r\n            code,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Invalid verification code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      googleAuth: async (\r\n        token: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleGoogleLogin(token, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with Google successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with Google successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/student/dashboard\";\r\n              } else {\r\n                window.location.href = \"/student/onboarding\";\r\n              }\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      linkedinAuth: async (\r\n        code: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleLinkedInLogin(code, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with LinkedIn successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with LinkedIn successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/student/dashboard\";\r\n              } else {\r\n                window.location.href = \"/student/onboarding\";\r\n              }\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => {\r\n        initiateLinkedInLogin(userType);\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AACA;AACA;AAJA;AACA;AAHA;;;;;;AA+DO,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,iBAAiB;QAEjB,MAAM;YACJ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,kBAAkB,CAAC,CAAC;YAC1B,IAAI;gBAAE;YAAgB;YACtB,OAAO;QACT;QAEA,OAAO,OAAO,UAAU;YACtB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,YAAY;gBAC5B,SAAS,MAAM,CAAC,YAAY;gBAE5B,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,UAAU;oBAC9D,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAC7D,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;gBACnB;gBAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ,OAAO;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,aAAa,EAAE,KAAK,QAAQ,EAAE,EAC/B;oBACE,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,WAAW,KAAK,SAAS;gBAC3B;gBAGF,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACpB,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,aAAa,OAAO,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,qBAAqB;oBAAE;oBAAO;gBAAK;gBACxD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAAE;gBAAM;gBACtD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OACjC;gBAEJ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,OAAO,MAAM;YACjC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,wBAAwB;oBAC3C;oBACA;oBACA,cAAc;gBAChB;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;YACxB,IAAI;gBAAE,OAAO;gBAAM,UAAU;gBAAM,iBAAiB;YAAM;QAC5D;QAEA,gBAAgB,OAAO,MAAM,WAAW,SAAS;YAC/C,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,GAAG,IAAI;oBACP,WAAW;gBACb;gBACA,IAAI;oBAAE,YAAY;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAAE;gBACxC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBAAE,YAAY;YAAK;QACzB;QAEA,gBAAgB,OAAO;YACrB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI,CAAC,YAAY,OAAO;gBACtB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,OAAO,WAAW,KAAK;oBACvB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAE7D,oCAAoC;gBACpC,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;oBACjB,YAAY;gBACd;gBAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,uDAAuD;gBACvD,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;gBACF;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO,OAAe;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;oBACA;gBACF;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OACV,OACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBAEhD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,IAAI,SAAS,iBAAiB,EAAE;4BAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,cAAc,OACZ,MACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;gBAEjD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,IAAI,SAAS,iBAAiB,EAAE;4BAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,sBAAsB,CAAC;YACrB,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;IACF,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/image.ts"], "sourcesContent": ["const backgroundColors: Record<string, string> = {\r\n  A: \"#fef3c7\",\r\n  B: \"#fde68a\",\r\n  C: \"#fcd34d\",\r\n  D: \"#fbbf24\",\r\n  E: \"#f9a8d4\",\r\n  F: \"#f472b6\",\r\n  G: \"#ec4899\",\r\n  H: \"#e879f9\",\r\n  I: \"#d8b4fe\",\r\n  J: \"#c4b5fd\",\r\n  K: \"#a78bfa\",\r\n  L: \"#818cf8\",\r\n  M: \"#60a5fa\",\r\n  N: \"#38bdf8\",\r\n  O: \"#22d3ee\",\r\n  P: \"#2dd4bf\",\r\n  Q: \"#34d399\",\r\n  R: \"#4ade80\",\r\n  S: \"#a3e635\",\r\n  T: \"#facc15\",\r\n  U: \"#f97316\",\r\n  V: \"#fb923c\",\r\n  W: \"#f87171\",\r\n  X: \"#ef4444\",\r\n  Y: \"#dc2626\",\r\n  Z: \"#b91c1c\",\r\n};\r\n\r\nexport function generatePlaceholder(\r\n  firstName: string | null | undefined,\r\n  lastName: string | null | undefined\r\n): string {\r\n  if (\r\n    !firstName ||\r\n    typeof firstName !== \"string\" ||\r\n    !firstName.trim() ||\r\n    !lastName\r\n  ) {\r\n    return \"/images/person.png\";\r\n  }\r\n\r\n  const firstLetter = firstName.trim()[0].toUpperCase();\r\n  const secondLetter =\r\n    lastName && typeof lastName === \"string\" && lastName.trim()\r\n      ? lastName.trim()[0].toUpperCase()\r\n      : \"\";\r\n  const initials = firstLetter + secondLetter;\r\n\r\n  const bgColor = backgroundColors[firstLetter] || \"#adb5bd\"; // Default grey shade\r\n  \r\n  return `https://placehold.co/100x100/${bgColor.slice(\r\n    1\r\n  )}/000000?text=${initials}`;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAA2C;IAC/C,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAEO,SAAS,oBACd,SAAoC,EACpC,QAAmC;IAEnC,IACE,CAAC,aACD,OAAO,cAAc,YACrB,CAAC,UAAU,IAAI,MACf,CAAC,UACD;QACA,OAAO;IACT;IAEA,MAAM,cAAc,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,WAAW;IACnD,MAAM,eACJ,YAAY,OAAO,aAAa,YAAY,SAAS,IAAI,KACrD,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,WAAW,KAC9B;IACN,MAAM,WAAW,cAAc;IAE/B,MAAM,UAAU,gBAAgB,CAAC,YAAY,IAAI,WAAW,qBAAqB;IAEjF,OAAO,CAAC,6BAA6B,EAAE,QAAQ,KAAK,CAClD,GACA,aAAa,EAAE,UAAU;AAC7B"}}, {"offset": {"line": 1438, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/UserButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ChevronDown, ArrowRight, LogOut } from \"lucide-react\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuItem,\r\n} from \"@/app/components/ui/dropdown-menu\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { Skeleton } from \"../ui/skeleton\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"../ui/button\";\r\nimport { useAuth } from \"@/app/hooks/useAuth\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\nexport const UserButton = ({}) => {\r\n  const { fetchUserInfo, userInfo, loading } = useProfile();\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const { logout } = useAuth();\r\n  const [windowLoaded, setWindowLoaded] = useState(false);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    setWindowLoaded(true);\r\n    if (localStorage.getItem(\"access_token\")) {\r\n      fetchUserInfo();\r\n    }\r\n  }, []);\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      logout();\r\n      router.push(\"/auth/login\");\r\n    } catch (error) {\r\n      console.error(\"Logout failed:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {!userInfo && !loading && (\r\n        <div className=\"flex items-center gap-4\">\r\n          <Link href=\"/auth/login\" className=\"md:inline-block hidden\">\r\n            <Button className=\"bg-[#9C0E22] hover:bg-[#9C0E22]/90 text-white px-6\">\r\n              Login\r\n            </Button>\r\n          </Link>\r\n          <Link\r\n            href={\r\n              windowLoaded && window.location.pathname === \"/for-counselors\"\r\n                ? \"/auth/signup/counselor\"\r\n                : \"/auth/signup\"\r\n            }\r\n          >\r\n            <Button className=\"bg-blue-950 hover:bg-blue-950/90 rounded-xl px-5 py-2 text-white\">\r\n              Signup\r\n              <ArrowRight className=\"h-6 w-6 flex-shrink-0\" />\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      )}\r\n      {!userInfo && loading && localStorage.getItem(\"access_token\") ? (\r\n        <Skeleton className=\"flex items-center gap-4 rounded-full p-5 border\"></Skeleton>\r\n      ) : (\r\n        userInfo && (\r\n          <div className=\"flex gap-4 items-center justify-center\">\r\n            <Link\r\n              href={\r\n                userInfo.userType === \"counselor\"\r\n                  ? \"/counselor/dashboard\"\r\n                  : \"/student/dashboard\"\r\n              }\r\n            >\r\n              <Button className=\"bg-blue-950 hover:bg-blue-950/90 rounded-xl md:inline-block hidden px-5 py-2 text-white\">\r\n                Dashboard\r\n                <ArrowRight className=\"h-6 w-6\" />\r\n              </Button>\r\n            </Link>\r\n            <div className=\"flex items-center gap-2 rounded-xl p-2 border\">\r\n              <Image\r\n                className=\"rounded-full object-cover w-[40px] h-[40px]\"\r\n                src={\r\n                  userInfo?.profile_picture_url ||\r\n                  generatePlaceholder(userInfo?.firstName, userInfo?.lastName)\r\n                }\r\n                alt={`${userInfo?.firstName}`}\r\n                width={40}\r\n                height={40}\r\n              />\r\n\r\n              <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\r\n                <DropdownMenuTrigger asChild>\r\n                  <button className=\"outline-none hover:opacity-80\">\r\n                    <ChevronDown\r\n                      className={`w-6 h-6 transform transition-transform duration-200 ${\r\n                        isOpen ? \"rotate-180\" : \"rotate-0\"\r\n                      }`}\r\n                    />\r\n                  </button>\r\n                </DropdownMenuTrigger>\r\n\r\n                <DropdownMenuContent\r\n                  align=\"end\"\r\n                  className=\"p-2 flex flex-col items-start bg-white\"\r\n                >\r\n                  <DropdownMenuItem className=\"cursor-pointer w-full mb-2\">\r\n                    <Link\r\n                      href={\r\n                        userInfo.userType === \"counselor\"\r\n                          ? \"/counselor/dashboard\"\r\n                          : \"/student/dashboard\"\r\n                      }\r\n                      className=\"w-full\"\r\n                    >\r\n                      <Button className=\"bg-blue-950 hover:bg-blue-950/90 rounded-xl md:hidden w-full px-5 py-2 text-white\">\r\n                        Dashboard\r\n                        <ArrowRight className=\"h-6 w-6 flex-shrink-0\" />\r\n                      </Button>\r\n                    </Link>\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem\r\n                    className=\"cursor-pointer w-full\"\r\n                    onClick={() => handleLogout()}\r\n                  >\r\n                    <Button className=\"w-full flex items-center justify-center gap-2 text-white bg-red-500 hover:bg-red-600/90\">\r\n                      Logout\r\n                      <LogOut className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            </div>\r\n          </div>\r\n        )\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;AAAA;AAAA;;;AAFA;;;;;;;;;;;;AAmBO,MAAM,aAAa,CAAC,EAAE;;IAC3B,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,gBAAgB;YAChB,IAAI,aAAa,OAAO,CAAC,iBAAiB;gBACxC;YACF;QACF;+BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,qBACE;;YACG,CAAC,YAAY,CAAC,yBACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAc,WAAU;kCACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;sCAAqD;;;;;;;;;;;kCAIzE,6LAAC,+JAAA,CAAA,UAAI;wBACH,MACE,gBAAgB,OAAO,QAAQ,CAAC,QAAQ,KAAK,oBACzC,2BACA;kCAGN,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;gCAAmE;8CAEnF,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK7B,CAAC,YAAY,WAAW,aAAa,OAAO,CAAC,gCAC5C,6LAAC,uIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;uBAEpB,0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MACE,SAAS,QAAQ,KAAK,cAClB,yBACA;kCAGN,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;gCAA0F;8CAE1G,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,KACE,UAAU,uBACV,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,WAAW,UAAU;gCAErD,KAAK,GAAG,UAAU,WAAW;gCAC7B,OAAO;gCACP,QAAQ;;;;;;0CAGV,6LAAC,+IAAA,CAAA,eAAY;gCAAC,MAAM;gCAAQ,cAAc;;kDACxC,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,uNAAA,CAAA,cAAW;gDACV,WAAW,CAAC,oDAAoD,EAC9D,SAAS,eAAe,YACxB;;;;;;;;;;;;;;;;kDAKR,6LAAC,+IAAA,CAAA,sBAAmB;wCAClB,OAAM;wCACN,WAAU;;0DAEV,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MACE,SAAS,QAAQ,KAAK,cAClB,yBACA;oDAEN,WAAU;8DAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;;4DAAoF;0EAEpG,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAI5B,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS,IAAM;0DAEf,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;;wDAA0F;sEAE1G,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxC;GA1Ha;;QACkC,wIAAA,CAAA,aAAU;QAEpC,0HAAA,CAAA,UAAO;QAEX,qIAAA,CAAA,YAAS;;;KALb"}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\nimport { Sheet, SheetContent, SheetTitle } from \"@components/ui/sheet\";\r\n\r\nimport * as React from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { UserButton } from \"./UserButton\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\n\r\nexport default function LandingNavbar() {\r\n  const { userInfo } = useProfile();\r\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\r\n  const [scrolled, setScrolled] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const isScrolled = window.scrollY > 20;\r\n      setScrolled(isScrolled);\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", handleScroll);\r\n    return () => window.removeEventListener(\"scroll\", handleScroll);\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      className={`fixed top-0 left-0 right-0 bg-white z-50 transition-all duration-300 ${\r\n        scrolled ? \"shadow-md\" : \"shadow-none\"\r\n      } border-b`}\r\n    >\r\n      <div className=\"container mx-auto px-4 md:px-8 py-2 z-50\">\r\n        <div className=\"flex items-center justify-between\">\r\n          {/* Left side - Logo */}\r\n          <div className=\"lg:w-1/4\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform duration-300 hover:scale-105\"\r\n            >\r\n              <Image src=\"/icons/logo.png\" alt=\"logo\" width={70} height={70} />\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Middle - Navigation */}\r\n          <div className=\"hidden md:flex justify-center gap-8 lg:w-2/4 font-clash-display\">\r\n            <Link\r\n              href=\"/explore\"\r\n              className=\"text-lg font-medium relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#9C0E22] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full\"\r\n            >\r\n              Explore\r\n            </Link>\r\n            <Link\r\n              href=\"/for-counselors\"\r\n              className=\"text-lg font-medium relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#9C0E22] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full\"\r\n            >\r\n              For Counselors\r\n            </Link>\r\n            <Link\r\n              href=\"/library\"\r\n              className=\"text-lg font-medium relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#9C0E22] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full\"\r\n            >\r\n              Library\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Right side - User button and menu */}\r\n          <div className=\"flex items-center justify-end space-x-4 lg:w-1/4\">\r\n            <UserButton />\r\n\r\n            <button\r\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n              className=\"md:hidden flex flex-col gap-1.5 w-8\"\r\n            >\r\n              <span\r\n                className={`block w-full h-[0.25rem] rounded-full bg-gray-800 transition-all duration-300 ${\r\n                  isMenuOpen ? \"rotate-45 translate-y-2\" : \"\"\r\n                }`}\r\n              />\r\n              <span\r\n                className={`block w-full h-[0.25rem] rounded-full bg-gray-800 transition-all duration-300 ${\r\n                  isMenuOpen ? \"opacity-0\" : \"\"\r\n                }`}\r\n              />\r\n              <span\r\n                className={`block w-full h-[0.25rem] rounded-full bg-gray-800 transition-all duration-300 ${\r\n                  isMenuOpen ? \"-rotate-45 -translate-y-2\" : \"\"\r\n                }`}\r\n              />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>\r\n        <SheetContent side=\"right\" className=\"w-[300px] sm:w-[400px]\">\r\n          <SheetTitle className=\"sr-only\">Navigation Menu</SheetTitle>\r\n          <nav className=\"flex flex-col gap-4  font-clash-display\">\r\n            <Link\r\n              onClick={() => setIsMenuOpen(false)}\r\n              href=\"/explore\"\r\n              className=\"text-lg font-medium p-2 hover:bg-gray-50 rounded-lg transition-all duration-300 hover:pl-4\"\r\n            >\r\n              Explore\r\n            </Link>\r\n            <Link\r\n              onClick={() => setIsMenuOpen(false)}\r\n              href=\"/for-counselors\"\r\n              className=\"text-lg font-medium p-2 hover:bg-gray-50 rounded-lg transition-all duration-300 hover:pl-4\"\r\n            >\r\n              For Counselors\r\n            </Link>\r\n            <Link\r\n              onClick={() => setIsMenuOpen(false)}\r\n              href=\"/library\"\r\n              className=\"text-lg font-medium p-2 hover:bg-gray-50 rounded-lg transition-all duration-300 hover:pl-4\"\r\n            >\r\n              Library\r\n            </Link>\r\n            <Link\r\n              onClick={() => setIsMenuOpen(false)}\r\n              href=\"/auth/login\"\r\n              className={`inline-flex text-lg font-medium px-6 py-2 mt-2 bg-[#9C0E22] hover:bg-[#9C0E22]/90 text-white rounded-lg transition-all duration-300 hover:translate-y-[-2px] hover:shadow-md w-fit ${\r\n                userInfo ? \"hidden\" : \"\"\r\n              }`}\r\n            >\r\n              Login\r\n            </Link>\r\n          </nav>\r\n        </SheetContent>\r\n      </Sheet>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAEA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,8JAAM,QAAQ,CAAC;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;wDAAe;oBACnB,MAAM,aAAa,OAAO,OAAO,GAAG;oBACpC,YAAY;gBACd;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,CAAC,qEAAqE,EAC/E,WAAW,cAAc,cAC1B,SAAS,CAAC;;0BAEX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAI;oCAAkB,KAAI;oCAAO,OAAO;oCAAI,QAAQ;;;;;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6IAAA,CAAA,aAAU;;;;;8CAEX,6LAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;;sDAEV,6LAAC;4CACC,WAAW,CAAC,8EAA8E,EACxF,aAAa,4BAA4B,IACzC;;;;;;sDAEJ,6LAAC;4CACC,WAAW,CAAC,8EAA8E,EACxF,aAAa,cAAc,IAC3B;;;;;;sDAEJ,6LAAC;4CACC,WAAW,CAAC,8EAA8E,EACxF,aAAa,8BAA8B,IAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOZ,6LAAC,oIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAY,cAAc;0BACrC,cAAA,6LAAC,oIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAQ,WAAU;;sCACnC,6LAAC,oIAAA,CAAA,aAAU;4BAAC,WAAU;sCAAU;;;;;;sCAChC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,SAAS,IAAM,cAAc;oCAC7B,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,SAAS,IAAM,cAAc;oCAC7B,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,SAAS,IAAM,cAAc;oCAC7B,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,SAAS,IAAM,cAAc;oCAC7B,MAAK;oCACL,WAAW,CAAC,mLAAmL,EAC7L,WAAW,WAAW,IACtB;8CACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA1HwB;;QACD,wIAAA,CAAA,aAAU;;;KADT"}}, {"offset": {"line": 1985, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}