{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/passwordInputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"eyeIcon\": \"index-module__zj4Tga__eyeIcon\",\n  \"inputIcon\": \"index-module__zj4Tga__inputIcon\",\n  \"passwordContainer\": \"index-module__zj4Tga__passwordContainer\",\n  \"passwordTooltip\": \"index-module__zj4Tga__passwordTooltip\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/inputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"container\": \"index-module__8yU72G__container\",\n  \"error\": \"index-module__8yU72G__error\",\n  \"errorMessage\": \"index-module__8yU72G__errorMessage\",\n  \"input\": \"index-module__8yU72G__input\",\n  \"label\": \"index-module__8yU72G__label\",\n  \"required\": \"index-module__8yU72G__required\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/inputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, InputHTMLAttributes } from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  label: string;\r\n  error?: string;\r\n  required?: boolean;\r\n}\r\n\r\nexport const InputField: FC<InputFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  ...inputProps\r\n}) => {\r\n  return (\r\n    <div className={styles.container}>\r\n      <label\r\n        htmlFor={inputProps.id || inputProps.name}\r\n        className={styles.label}\r\n      >\r\n        {label} {required && <span className={styles.required}>*</span>}\r\n      </label>\r\n\r\n      <input\r\n        {...inputProps}\r\n        className={`${styles.input} ${error ? styles.error : \"\"}`}\r\n        aria-required={required}\r\n        aria-invalid={!!error}\r\n        aria-describedby={error ? `${inputProps.id}-error` : undefined}\r\n      />\r\n\r\n      {error && (\r\n        <span\r\n          id={`${inputProps.id}-error`}\r\n          className={styles.errorMessage}\r\n          role=\"alert\"\r\n        >\r\n          {error}\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,MAAM,aAAkC,CAAC,EAC9C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,YACJ;IACC,qBACE,8OAAC;QAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,8OAAC;gBACC,SAAS,WAAW,EAAE,IAAI,WAAW,IAAI;gBACzC,WAAW,iKAAA,CAAA,UAAM,CAAC,KAAK;;oBAEtB;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAW,iKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAGzD,8OAAC;gBACE,GAAG,UAAU;gBACd,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,iKAAA,CAAA,UAAM,CAAC,KAAK,GAAG,IAAI;gBACzD,iBAAe;gBACf,gBAAc,CAAC,CAAC;gBAChB,oBAAkB,QAAQ,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG;;;;;;YAGtD,uBACC,8OAAC;gBACC,IAAI,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC;gBAC5B,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;gBAC9B,MAAK;0BAEJ;;;;;;;;;;;;AAKX"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/passwordInputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, useState } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';\r\nimport styles from './index.module.css';\r\nimport { InputField } from '../inputField';\r\n\r\ninterface PasswordInputFieldProps {\r\n  label: string;\r\n  name: string;\r\n  value: string;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  error?: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  showPasswordTooltip?: boolean;\r\n}\r\n\r\nexport const PasswordInputField: FC<PasswordInputFieldProps> = ({\r\n  label,\r\n  showPasswordTooltip = false,\r\n  ...props\r\n}) => {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showTooltip, setShowTooltip] = useState(false);\r\n\r\n  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);\r\n\r\n  return (\r\n    <div className={styles.passwordContainer}>\r\n      <InputField\r\n        {...props}\r\n        type={showPassword ? 'text' : 'password'}\r\n        label={label}\r\n        onFocus={() => setShowTooltip(true)}\r\n        onBlur={() => setShowTooltip(false)}\r\n      />\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={togglePasswordVisibility}\r\n        className={styles.inputIcon}\r\n        aria-label={showPassword ? 'Hide password' : 'Show password'}\r\n      >\r\n        <FontAwesomeIcon\r\n          icon={showPassword ? faEyeSlash : faEye}\r\n          className={styles.eyeIcon}\r\n        />\r\n      </button>\r\n\r\n      {showPasswordTooltip && showTooltip && (\r\n        <div className={styles.passwordTooltip}>\r\n          <ul>\r\n            <li>Minimum 8 characters</li>\r\n            <li>At least 1 number</li>\r\n            <li>At least 1 uppercase letter</li>\r\n            <li>At least 1 symbol (!@#$%^&*)</li>\r\n          </ul>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAJA;;;;;;;AAmBO,MAAM,qBAAkD,CAAC,EAC9D,KAAK,EACL,sBAAsB,KAAK,EAC3B,GAAG,OACJ;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,2BAA2B,IAAM,gBAAgB,CAAC,OAAS,CAAC;IAElE,qBACE,8OAAC;QAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,iBAAiB;;0BACtC,8OAAC,mJAAA,CAAA,aAAU;gBACR,GAAG,KAAK;gBACT,MAAM,eAAe,SAAS;gBAC9B,OAAO;gBACP,SAAS,IAAM,eAAe;gBAC9B,QAAQ,IAAM,eAAe;;;;;;0BAG/B,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAW,yKAAA,CAAA,UAAM,CAAC,SAAS;gBAC3B,cAAY,eAAe,kBAAkB;0BAE7C,cAAA,8OAAC,oKAAA,CAAA,kBAAe;oBACd,MAAM,eAAe,wKAAA,CAAA,aAAU,GAAG,wKAAA,CAAA,QAAK;oBACvC,WAAW,yKAAA,CAAA,UAAM,CAAC,OAAO;;;;;;;;;;;YAI5B,uBAAuB,6BACtB,8OAAC;gBAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,eAAe;0BACpC,cAAA,8OAAC;;sCACC,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAG;;;;;;;;;;;;;;;;;;;;;;;AAMhB"}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/button/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"buttonBase\": \"index-module__Mmy9Oa__buttonBase\",\n  \"buttonContent\": \"index-module__Mmy9Oa__buttonContent\",\n  \"buttonPrimary\": \"index-module__Mmy9Oa__buttonPrimary\",\n  \"buttonSecondary\": \"index-module__Mmy9Oa__buttonSecondary\",\n  \"icon\": \"index-module__Mmy9Oa__icon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/button/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface ButtonProps {\r\n  variant: \"primary\" | \"secondary\";\r\n  children: React.ReactNode;\r\n  onClick?: (e: any) => void;\r\n  type?: \"button\" | \"submit\" | \"reset\";\r\n  disabled?: boolean;\r\n  icon?: React.ReactNode;\r\n}\r\n\r\nexport const Button: React.FC<ButtonProps> = ({\r\n  variant,\r\n  children,\r\n  onClick,\r\n  type = \"button\",\r\n  disabled = false,\r\n  icon,\r\n}) => {\r\n  const buttonClasses = `${styles.buttonBase} ${\r\n    variant === \"primary\" ? styles.buttonPrimary : styles.buttonSecondary\r\n  }`;\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      onClick={onClick}\r\n      className={buttonClasses}\r\n      disabled={disabled}\r\n    >\r\n      <span className={styles.buttonContent}>\r\n        {children}\r\n        {icon && <span className={styles.icon}>{icon}</span>}\r\n      </span>\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAcO,MAAM,SAAgC,CAAC,EAC5C,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,IAAI,EACL;IACC,MAAM,gBAAgB,GAAG,6JAAA,CAAA,UAAM,CAAC,UAAU,CAAC,CAAC,EAC1C,YAAY,YAAY,6JAAA,CAAA,UAAM,CAAC,aAAa,GAAG,6JAAA,CAAA,UAAM,CAAC,eAAe,EACrE;IAEF,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,WAAW;QACX,UAAU;kBAEV,cAAA,8OAAC;YAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,aAAa;;gBAClC;gBACA,sBAAQ,8OAAC;oBAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,IAAI;8BAAG;;;;;;;;;;;;;;;;;AAIhD"}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/passwordPages/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactNode } from \"react\";\r\nimport { Button } from \"@components/common/button\";\r\nimport { FaArrowRight } from \"react-icons/fa\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\n\r\ninterface PasswordPagesLayoutProps {\r\n  children: ReactNode;\r\n  title: ReactNode;\r\n  buttonText: string;\r\n  onBackClick: () => void;\r\n  onSubmitClick: (e: any) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport const PasswordPagesLayout: React.FC<PasswordPagesLayoutProps> = ({\r\n  children,\r\n  title,\r\n  buttonText,\r\n  onBackClick,\r\n  onSubmitClick,\r\n  isLoading = false,\r\n}) => {\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <header className=\"w-full p-4 bg-white\">\r\n        <div className=\"flex justify-between items-center max-w-7xl mx-auto px-4\">\r\n          {/* Logo */}\r\n          <Image\r\n            src=\"/icons/logo.png\"\r\n            alt=\"AdmitPath Logo\"\r\n            width={100}\r\n            height={100}\r\n            priority\r\n            className=\"w-[5rem]\"\r\n          />\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={() => router.push(\"/auth/login\")}\r\n            icon={<FaArrowRight />}\r\n          >\r\n            Login\r\n          </Button>\r\n        </div>\r\n      </header>\r\n      <main className=\"flex items-center justify-center min-h-[calc(100vh-72px)] p-4\">\r\n        <div className=\"bg-white rounded-lg shadow-md w-full max-w-[480px] p-6 sm:p-8\">\r\n          <div className=\"text-2xl mb-8 flex items-center gap-2 text-gray-900 font-medium\">\r\n            {title}\r\n          </div>\r\n\r\n          {children}\r\n\r\n          <div className=\"flex justify-between items-center mt-8\">\r\n            <Button variant=\"secondary\" onClick={onBackClick}>\r\n              Back\r\n            </Button>\r\n            <Button\r\n              variant=\"primary\"\r\n              onClick={onSubmitClick}\r\n              type=\"button\"\r\n              icon={<FaArrowRight />}\r\n              disabled={isLoading}\r\n            >\r\n              {buttonText}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AAFA;AAJA;;;;;;AAiBO,MAAM,sBAA0D,CAAC,EACtE,QAAQ,EACR,KAAK,EACL,UAAU,EACV,WAAW,EACX,aAAa,EACb,YAAY,KAAK,EAClB;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC,+IAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,oBAAM,8OAAC,8IAAA,CAAA,eAAY;;;;;sCACpB;;;;;;;;;;;;;;;;;0BAKL,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ;;;;;;wBAGF;sCAED,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,+IAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,SAAS;8CAAa;;;;;;8CAGlD,8OAAC,+IAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,MAAK;oCACL,oBAAM,8OAAC,8IAAA,CAAA,eAAY;;;;;oCACnB,UAAU;8CAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf"}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/passwordRequirements.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\nimport { FaCheck, FaTimes, FaRegSquare } from \"react-icons/fa\";\r\n\r\ninterface PasswordRequirement {\r\n  label: string;\r\n  validator: (password: string, confirmPassword?: string) => boolean;\r\n}\r\n\r\ninterface PasswordRequirementsProps {\r\n  password: string;\r\n  confirmPassword?: string;\r\n}\r\n\r\nconst requirements: PasswordRequirement[] = [\r\n  {\r\n    label: \"At least 8 characters long\",\r\n    validator: (password) => password.length >= 8,\r\n  },\r\n  {\r\n    label: \"Contains at least one uppercase letter\",\r\n    validator: (password) => /[A-Z]/.test(password),\r\n  },\r\n  {\r\n    label: \"Contains at least one lowercase letter\",\r\n    validator: (password) => /[a-z]/.test(password),\r\n  },\r\n  {\r\n    label: \"Contains at least one number\",\r\n    validator: (password) => /\\d/.test(password),\r\n  },\r\n  {\r\n    label: \"Contains at least one special character\",\r\n    validator: (password) => /[!@#$%^&*(),.?\":{}|<>]/.test(password),\r\n  },\r\n  {\r\n    label: \"Passwords match\",\r\n    validator: (password, confirmPassword) => password === confirmPassword,\r\n  },\r\n];\r\n\r\nexport const PasswordRequirements: FC<PasswordRequirementsProps> = ({\r\n  password,\r\n  confirmPassword = \"\",\r\n}) => {\r\n  return (\r\n    <div className=\"mt-2 space-y-2 text-sm\">\r\n      {requirements.map((requirement, index) => {\r\n        const isMet = requirement.validator(password, confirmPassword);\r\n        const shouldShow = password || requirement.label === \"Passwords match\";\r\n\r\n        return (\r\n          <div\r\n            key={index}\r\n            className={`flex items-center space-x-2 ${\r\n              isMet\r\n                ? \"text-green-600\"\r\n                : shouldShow\r\n                ? \"text-red-600\"\r\n                : \"text-gray-500\"\r\n            }`}\r\n          >\r\n            <div className=\"w-4 h-4 flex items-center justify-center\">\r\n              {shouldShow ? (\r\n                isMet ? (\r\n                  <FaCheck className=\"w-full h-full\" />\r\n                ) : (\r\n                  <FaTimes className=\"w-full h-full\" />\r\n                )\r\n              ) : (\r\n                <FaRegSquare className=\"w-[0.875rem] h-[0.875rem]\" />\r\n              )}\r\n            </div>\r\n            <span>{requirement.label}</span>\r\n          </div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const validatePassword = (\r\n  password: string,\r\n  confirmPassword?: string\r\n): boolean => {\r\n  return requirements.every((requirement) =>\r\n    requirement.validator(password, confirmPassword)\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAeA,MAAM,eAAsC;IAC1C;QACE,OAAO;QACP,WAAW,CAAC,WAAa,SAAS,MAAM,IAAI;IAC9C;IACA;QACE,OAAO;QACP,WAAW,CAAC,WAAa,QAAQ,IAAI,CAAC;IACxC;IACA;QACE,OAAO;QACP,WAAW,CAAC,WAAa,QAAQ,IAAI,CAAC;IACxC;IACA;QACE,OAAO;QACP,WAAW,CAAC,WAAa,KAAK,IAAI,CAAC;IACrC;IACA;QACE,OAAO;QACP,WAAW,CAAC,WAAa,yBAAyB,IAAI,CAAC;IACzD;IACA;QACE,OAAO;QACP,WAAW,CAAC,UAAU,kBAAoB,aAAa;IACzD;CACD;AAEM,MAAM,uBAAsD,CAAC,EAClE,QAAQ,EACR,kBAAkB,EAAE,EACrB;IACC,qBACE,8OAAC;QAAI,WAAU;kBACZ,aAAa,GAAG,CAAC,CAAC,aAAa;YAC9B,MAAM,QAAQ,YAAY,SAAS,CAAC,UAAU;YAC9C,MAAM,aAAa,YAAY,YAAY,KAAK,KAAK;YAErD,qBACE,8OAAC;gBAEC,WAAW,CAAC,4BAA4B,EACtC,QACI,mBACA,aACA,iBACA,iBACJ;;kCAEF,8OAAC;wBAAI,WAAU;kCACZ,aACC,sBACE,8OAAC,8IAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,8OAAC,8IAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAGrB,8OAAC,8IAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAG3B,8OAAC;kCAAM,YAAY,KAAK;;;;;;;eApBnB;;;;;QAuBX;;;;;;AAGN;AAEO,MAAM,mBAAmB,CAC9B,UACA;IAEA,OAAO,aAAa,KAAK,CAAC,CAAC,cACzB,YAAY,SAAS,CAAC,UAAU;AAEpC"}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/signup/setPassword/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\nimport { PasswordInputField } from \"@components/common/passwordInputField\";\r\nimport { PasswordPagesLayout } from \"../../passwordPages/layout\";\r\nimport { useState } from \"react\";\r\nimport {\r\n  PasswordRequirements,\r\n  validatePassword,\r\n} from \"@components/common/passwordRequirements\";\r\n\r\ninterface SetPasswordFormProps {\r\n  onSubmit: (password: string) => Promise<void>;\r\n  onBack: () => void;\r\n  isLoading: boolean;\r\n  userType?: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const SetPasswordForm: FC<SetPasswordFormProps> = ({\r\n  onSubmit,\r\n  onBack,\r\n  isLoading,\r\n  userType = \"student\",\r\n}) => {\r\n  const [password, setPassword] = useState(\"\");\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\r\n  const [error, setError] = useState<string | undefined>();\r\n  const [showRequirements, setShowRequirements] = useState(false);\r\n\r\n  const handleSubmit = async () => {\r\n    setError(undefined);\r\n\r\n    if (!validatePassword(password, confirmPassword)) {\r\n      setError(\"Please meet all password requirements\");\r\n      setShowRequirements(true);\r\n      return;\r\n    }\r\n\r\n    await onSubmit(password);\r\n  };\r\n\r\n  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setPassword(e.target.value);\r\n    setError(undefined);\r\n  };\r\n\r\n  const handleConfirmPasswordChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    setConfirmPassword(e.target.value);\r\n    setError(undefined);\r\n  };\r\n\r\n  return (\r\n    <PasswordPagesLayout\r\n      title={\r\n        userType === \"student\"\r\n          ? \"Set Your Password\"\r\n          : \"Set Your Counselor Password\"\r\n      }\r\n      buttonText=\"Complete Signup\"\r\n      onBackClick={onBack}\r\n      onSubmitClick={handleSubmit}\r\n      isLoading={isLoading}\r\n    >\r\n      <div className=\"w-full space-y-6\">\r\n        <div className=\"space-y-4\">\r\n          <div>\r\n            <PasswordInputField\r\n              label=\"Password\"\r\n              name=\"password\"\r\n              placeholder=\"Enter your password\"\r\n              required\r\n              value={password}\r\n              onChange={handlePasswordChange}\r\n              error={error}\r\n            />\r\n            {showRequirements && (\r\n              <PasswordRequirements\r\n                password={password}\r\n                confirmPassword={confirmPassword}\r\n              />\r\n            )}\r\n          </div>\r\n          <PasswordInputField\r\n            label=\"Confirm Password\"\r\n            name=\"confirmPassword\"\r\n            placeholder=\"Confirm your password\"\r\n            required\r\n            value={confirmPassword}\r\n            onChange={handleConfirmPasswordChange}\r\n            error={error}\r\n          />\r\n        </div>\r\n      </div>\r\n    </PasswordPagesLayout>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAkBO,MAAM,kBAA4C,CAAC,EACxD,QAAQ,EACR,MAAM,EACN,SAAS,EACT,WAAW,SAAS,EACrB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,eAAe;QACnB,SAAS;QAET,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,kBAAkB;YAChD,SAAS;YACT,oBAAoB;YACpB;QACF;QAEA,MAAM,SAAS;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,EAAE,MAAM,CAAC,KAAK;QAC1B,SAAS;IACX;IAEA,MAAM,8BAA8B,CAClC;QAEA,mBAAmB,EAAE,MAAM,CAAC,KAAK;QACjC,SAAS;IACX;IAEA,qBACE,8OAAC,qJAAA,CAAA,sBAAmB;QAClB,OACE,aAAa,YACT,sBACA;QAEN,YAAW;QACX,aAAa;QACb,eAAe;QACf,WAAW;kBAEX,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC,2JAAA,CAAA,qBAAkB;gCACjB,OAAM;gCACN,MAAK;gCACL,aAAY;gCACZ,QAAQ;gCACR,OAAO;gCACP,UAAU;gCACV,OAAO;;;;;;4BAER,kCACC,8OAAC,oJAAA,CAAA,uBAAoB;gCACnB,UAAU;gCACV,iBAAiB;;;;;;;;;;;;kCAIvB,8OAAC,2JAAA,CAAA,qBAAkB;wBACjB,OAAM;wBACN,MAAK;wBACL,aAAY;wBACZ,QAAQ;wBACR,OAAO;wBACP,UAAU;wBACV,OAAO;;;;;;;;;;;;;;;;;;;;;;AAMnB"}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 718, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/apiClient.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios from \"axios\";\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n    Accept: \"application/json\",\r\n  },\r\n  // withCredentials: true,\r\n});\r\n\r\n// Request interceptor to handle dynamic headers or logging\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem(\"access_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle common errors globally\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle specific error responses (e.g., 401 Unauthorized)\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        if (\r\n          window.location.pathname.startsWith(\"/student\") ||\r\n          window.location.pathname.startsWith(\"/counselor\") ||\r\n          localStorage.getItem(\"access_token\")\r\n        ) {\r\n          console.error(\"Unauthorized. Redirecting to login...\");\r\n          localStorage.removeItem(\"access_token\");\r\n          window.location.href = \"/auth/login\";\r\n        }\r\n      } else if (status >= 500) {\r\n        console.error(\r\n          \"Server error:\",\r\n          error.response.data.message || \"Internal Server Error\"\r\n        );\r\n      }\r\n    } else {\r\n      console.error(\"Network error:\", error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,6DAAwC;IACjD,SAAS;QACP,gBAAgB;QAChB,QAAQ;IACV;AAEF;AAEA,2DAA2D;AAC3D,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wDAAwD;AACxD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,2DAA2D;IAC3D,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,IACE,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,eACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,aAAa,OAAO,CAAC,iBACrB;gBACA,QAAQ,KAAK,CAAC;gBACd,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAEnC;IACF,OAAO;QACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;IAC/C;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa"}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/auth.ts"], "sourcesContent": ["import { toast } from 'react-toastify';\r\nimport apiClient from '@lib/apiClient';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;\r\n\r\ninterface AuthError extends Error {\r\n  isAuthError?: boolean;\r\n}\r\n\r\nexport const handleGoogleLogin = async (token: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/google', \r\n      userType \r\n        ? { token, user_type: userType }  // Signup case\r\n        : { token }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('Google auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const handleLinkedInLogin = async (code: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/linkedin',\r\n      userType \r\n        ? { code, user_type: userType }  // Signup case\r\n        : { code }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('LinkedIn auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const initiateLinkedInLogin = (userType?: string) => {\r\n  const LINKEDIN_CLIENT_ID = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;\r\n  const LINKEDIN_REDIRECT_URI = process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI;\r\n  const scope = 'openid profile email';\r\n  \r\n  // Only include state (userType) for signup flow\r\n  const stateParam = userType ? `&state=${userType}` : '';\r\n  const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${LINKEDIN_REDIRECT_URI}${stateParam}&scope=${scope}`;\r\n  \r\n  window.location.href = url;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;;AAEA,MAAM;AAMC,MAAM,oBAAoB,OAAO,OAAe;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBACpC,WACI;YAAE;YAAO,WAAW;QAAS,EAAG,cAAc;WAC9C;YAAE;QAAM,EAAG,aAAa;;QAG9B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,MAAc;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,kBACpC,WACI;YAAE;YAAM,WAAW;QAAS,EAAG,cAAc;WAC7C;YAAE;QAAK,EAAG,aAAa;;QAG7B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,qBAAqB,QAAQ,GAAG,CAAC,8BAA8B;IACrE,MAAM,wBAAwB,QAAQ,GAAG,CAAC,iCAAiC;IAC3E,MAAM,QAAQ;IAEd,gDAAgD;IAChD,MAAM,aAAa,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG;IACrD,MAAM,MAAM,CAAC,6EAA6E,EAAE,mBAAmB,cAAc,EAAE,wBAAwB,WAAW,OAAO,EAAE,OAAO;IAElL,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB"}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  handleGoogleLogin,\r\n  handleLinkedInLogin,\r\n  initiateLinkedInLogin,\r\n} from \"@/app/utils/auth\";\r\n\r\ninterface User {\r\n  avatar?: string;\r\n  firstName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface AuthState {\r\n  token: string | null;\r\n  user: User | null;\r\n  userType: string | null;\r\n  loading: boolean;\r\n  login: (username: string, password: string) => Promise<void>;\r\n  signup: (data: SignupData) => Promise<void>;\r\n  verifyEmail: (email: string, code: string) => Promise<void>;\r\n  forgotPassword: (email: string) => Promise<void>;\r\n  resetPassword: (\r\n    email: string,\r\n    code: string,\r\n    newPassword: string\r\n  ) => Promise<void>;\r\n  logout: () => void;\r\n  signupData: Partial<SignupData> | null;\r\n  initiateSignup: (\r\n    data: { first_name: string; last_name: string; email: string },\r\n    userType: \"student\" | \"counselor\"\r\n  ) => Promise<void>;\r\n  resetSignupData: () => void;\r\n  completeSignup: (password: string) => Promise<string>;\r\n  resendVerificationCode: (email: string) => Promise<void>;\r\n  verifySignupCode: (email: string, code: string) => Promise<void>;\r\n  googleAuth: (\r\n    token: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  linkedinAuth: (\r\n    code: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => void;\r\n  isAuthenticated: boolean;\r\n  auth: () => boolean;\r\n}\r\n\r\ninterface SignupData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  userType: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const useAuth = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      token: null as string | null,\r\n      user: null as User | null,\r\n      userType: null as string | null,\r\n      loading: false,\r\n      signupData: null,\r\n      isAuthenticated: false,\r\n\r\n      auth: () => {\r\n        const token = localStorage.getItem(\"access_token\");\r\n        const isAuthenticated = !!token;\r\n        set({ isAuthenticated });\r\n        return isAuthenticated;\r\n      },\r\n\r\n      login: async (username, password) => {\r\n        try {\r\n          set({ loading: true });\r\n          const formData = new URLSearchParams();\r\n          formData.append(\"username\", username);\r\n          formData.append(\"password\", password);\r\n\r\n          const response = await apiClient.post(\"/auth/signin\", formData, {\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n            },\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n          });\r\n\r\n          toast.success(\"Logged in successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Login failed\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      signup: async (data) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\r\n            `/auth/signup/${data.userType}`,\r\n            {\r\n              email: data.email,\r\n              password: data.password,\r\n              first_name: data.first_name,\r\n              last_name: data.last_name,\r\n            }\r\n          );\r\n\r\n          if (response.data.id) {\r\n            localStorage.setItem(\"email\", data.email);\r\n            toast.success(\"Verification code sent to your email!\");\r\n          }\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Signup failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifyEmail: async (email, code) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/verify-code\", { email, code });\r\n          toast.success(\"Code verified successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Verification failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      forgotPassword: async (email) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/forgot-password\", { email });\r\n          toast.success(\"Reset code sent to your email!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg ||\r\n              \"Failed to send reset code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetPassword: async (email, code, newPassword) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/reset-password\", {\r\n            email,\r\n            code,\r\n            new_password: newPassword,\r\n          });\r\n          toast.success(\"Password reset successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Password reset failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      logout: () => {\r\n        localStorage.removeItem(\"access_token\");\r\n        set({ token: null, userType: null, isAuthenticated: false });\r\n      },\r\n\r\n      initiateSignup: async (data, userType = \"student\") => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/initiate\", {\r\n            ...data,\r\n            user_type: userType,\r\n          });\r\n          set({ signupData: { ...data, userType } });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to initiate signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetSignupData: () => {\r\n        set({ signupData: null });\r\n      },\r\n\r\n      completeSignup: async (password: string) => {\r\n        const { signupData } = get();\r\n        if (!signupData?.email) {\r\n          toast.error(\"Missing signup data\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/complete\", {\r\n            email: signupData.email,\r\n            password,\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n\r\n          // Store token and update auth state\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n            signupData: null,\r\n          });\r\n\r\n          toast.success(\"Account created successfully!\");\r\n\r\n          // Return user type so pages can redirect appropriately\r\n          return user_type;\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to complete signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resendVerificationCode: async (email: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/resend\", {\r\n            email,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Failed to resend code\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifySignupCode: async (email: string, code: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/verify\", {\r\n            email,\r\n            code,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Invalid verification code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      googleAuth: async (\r\n        token: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleGoogleLogin(token, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with Google successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with Google successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/student/dashboard\";\r\n              } else {\r\n                window.location.href = \"/student/onboarding\";\r\n              }\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      linkedinAuth: async (\r\n        code: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleLinkedInLogin(code, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with LinkedIn successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with LinkedIn successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/student/dashboard\";\r\n              } else {\r\n                window.location.href = \"/student/onboarding\";\r\n              }\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => {\r\n        initiateLinkedInLogin(userType);\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AACA;AACA;AAJA;AACA;AAHA;;;;;;AA+DO,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,iBAAiB;QAEjB,MAAM;YACJ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,kBAAkB,CAAC,CAAC;YAC1B,IAAI;gBAAE;YAAgB;YACtB,OAAO;QACT;QAEA,OAAO,OAAO,UAAU;YACtB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,YAAY;gBAC5B,SAAS,MAAM,CAAC,YAAY;gBAE5B,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,UAAU;oBAC9D,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAC7D,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;gBACnB;gBAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ,OAAO;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,aAAa,EAAE,KAAK,QAAQ,EAAE,EAC/B;oBACE,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,WAAW,KAAK,SAAS;gBAC3B;gBAGF,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACpB,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,aAAa,OAAO,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,qBAAqB;oBAAE;oBAAO;gBAAK;gBACxD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAAE;gBAAM;gBACtD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OACjC;gBAEJ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,OAAO,MAAM;YACjC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,wBAAwB;oBAC3C;oBACA;oBACA,cAAc;gBAChB;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;YACxB,IAAI;gBAAE,OAAO;gBAAM,UAAU;gBAAM,iBAAiB;YAAM;QAC5D;QAEA,gBAAgB,OAAO,MAAM,WAAW,SAAS;YAC/C,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,GAAG,IAAI;oBACP,WAAW;gBACb;gBACA,IAAI;oBAAE,YAAY;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAAE;gBACxC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBAAE,YAAY;YAAK;QACzB;QAEA,gBAAgB,OAAO;YACrB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI,CAAC,YAAY,OAAO;gBACtB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,OAAO,WAAW,KAAK;oBACvB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAE7D,oCAAoC;gBACpC,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;oBACjB,YAAY;gBACd;gBAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,uDAAuD;gBACvD,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;gBACF;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO,OAAe;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;oBACA;gBACF;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OACV,OACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBAEhD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,IAAI,SAAS,iBAAiB,EAAE;4BAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,cAAc,OACZ,MACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;gBAEjD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,IAAI,SAAS,iBAAiB,EAAE;4BAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,sBAAsB,CAAC;YACrB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;IACF,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/auth/signup/set-password/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { SetPasswordForm } from \"@components/auth/signup/setPassword\";\r\nimport { useAuth } from \"@hooks/useAuth\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\n\r\nexport default function SetPasswordPage() {\r\n  const { completeSignup, loading, signupData } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const handleSetPassword = async (password: string) => {\r\n    const email = localStorage.getItem(\"email\");\r\n    if (!email) {\r\n      router.push(\"/auth/signup\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const userType = await completeSignup(password);\r\n      // Redirect to appropriate onboarding page\r\n      if (userType === \"student\") {\r\n        router.push(\"/student/onboarding\");\r\n      } else {\r\n        router.push(\"/counselor/onboarding\");\r\n      }\r\n    } catch (error) {\r\n      // Error handled by hook\r\n    }\r\n  };\r\n\r\n  const handleBack = () => {\r\n    router.push(\"/auth/verify\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Redirect if no email or signup data\r\n    const email = localStorage.getItem(\"email\");\r\n    if (!email || !signupData) {\r\n      router.push(\"/auth/signup\");\r\n    }\r\n  }, [signupData, router]);\r\n\r\n  return (\r\n    <SetPasswordForm\r\n      onSubmit={handleSetPassword}\r\n      onBack={handleBack}\r\n      isLoading={loading}\r\n      userType=\"student\"\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACtD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,OAAO;QAC/B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,eAAe;YACtC,0CAA0C;YAC1C,IAAI,aAAa,WAAW;gBAC1B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,YAAY;YACzB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,qBACE,8OAAC,4JAAA,CAAA,kBAAe;QACd,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAS;;;;;;AAGf"}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}