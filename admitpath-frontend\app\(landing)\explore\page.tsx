"use client";
import { useCounselors } from "@/app/hooks/public/useCounselors";
import CounselorCardList from "@components/public/explore/CounselorCardList";
import { FiltersSidebar } from "@components/public/explore/filters-sidebar";
import { useEffect, useRef,  useMemo } from "react";

export default function ExplorePage() {
  const {
    loading: counselorsLoading,
    fetchAllCounselors,
    counselors,
    loadMore,
    hasMore,
  } = useCounselors();

  const observerTarget = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef(0);
  const contentRef = useRef<HTMLDivElement>(null);

  // Memoize the observer callback to prevent unnecessary re-renders
  const handleObserver = useMemo(
    () =>
      function (entries: IntersectionObserverEntry[]) {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !counselorsLoading) {
          // Save current scroll position before loading more
          if (contentRef.current) {
            scrollPositionRef.current = window.scrollY;
          }
          loadMore();
        }
      },
    [hasMore, counselorsLoading, loadMore]
  );

  useEffect(() => {
    const element = observerTarget.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleObserver, {
      root: null,
      rootMargin: "200px", // Increased margin to load earlier
      threshold: 0.1,
    });

    observer.observe(element);

    return () => {
      if (element) observer.unobserve(element);
    };
  }, [handleObserver]);

  // Restore scroll position after new content is loaded
  useEffect(() => {
    if (!counselorsLoading && scrollPositionRef.current > 0) {
      window.scrollTo(0, scrollPositionRef.current);
    }
  }, [counselors, counselorsLoading]);

  // Track if initial fetch has been done
  const initialFetchRef = useRef(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!initialFetchRef.current) {
          await fetchAllCounselors();
          initialFetchRef.current = true;
        }
      } catch (error) {
        console.error("Error fetching counselors:", error);
      }
    };

    fetchData();
  }, [fetchAllCounselors]);

  return (
    <div className="min-h-screen bg-white px-4 py-8 sm:px-8 md:px-12 lg:px-20 font-clash-display my-6 sm:my-8 lg:my-10">
      <h1 className="md:mb-12 mb-8 text-center text-3xl md:text-4xl font-[500]">
        Explore Counselors
      </h1>
      <div className="flex flex-col md:flex-row gap-x-8 gap-y-4">
        <FiltersSidebar />
        <div ref={contentRef} className="flex-1 flex flex-col gap-8">
          <CounselorCardList
            counselors={counselors}
            loading={counselorsLoading}
          />
          {hasMore && (
            <div
              ref={observerTarget}
              className="h-10 flex items-center justify-center"
            >
              {counselorsLoading && (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900" />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
