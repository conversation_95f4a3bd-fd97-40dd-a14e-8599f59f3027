"use client";

import { useEffect, useState } from "react";
import { useProfile } from "@/app/hooks/student/useProfile";
import { ProfileHeader } from "./profile-header";
import {  GoalsBlock } from "./goals";
import { ProfileInfoGrid } from "./profile-info";
import { EducationBlock } from "./education";
import { ProfileEditor } from "./edit-mode";

type EditModeState = {
  isEditing: boolean;
  initialTab: "personal" | "educational";
};

export default function Profile() {
  const {
    userInfo,
    personalInfo,
    educationInfo,
    fetchUserInfo,
    fetchPersonalInfo,
    fetchEducationalBackground,
  } = useProfile();

  const [editMode, setEditMode] = useState<EditModeState>({
    isEditing: false,
    initialTab: "personal",
  });

  useEffect(() => {
    fetchUserInfo();
    fetchPersonalInfo();
    fetchEducationalBackground();
  }, [fetchUserInfo, fetchPersonalInfo, fetchEducationalBackground]);

  const handleEditClick = (section: "personal" | "educational") => {
    setEditMode({
      isEditing: true,
      initialTab: section,
    });
  };

  return (
    <div className="w-full">
      <div className="max-w-7xl mx-auto bg-white rounded-xl p-4 sm:p-6 lg:p-8 flex flex-col gap-y-4 lg:gap-y-6">
        {editMode.isEditing ? (
          <ProfileEditor
            initialTab={editMode.initialTab}
            onClose={() =>
              setEditMode({ isEditing: false, initialTab: "personal" })
            }
          />
        ) : (
          <>
            <ProfileHeader
              userInfo={userInfo}
              educationInfo={educationInfo}
              onEditClick={() => handleEditClick("personal")}
            />
            <ProfileInfoGrid userInfo={userInfo} personalInfo={personalInfo} />
            <GoalsBlock
              personalInfo={personalInfo}
              onEditClick={() => handleEditClick("personal")}
            />
            <EducationBlock
              educationInfo={educationInfo}
              onEditClick={() => handleEditClick("educational")}
            />
          </>
        )}
      </div>
    </div>
  );
}
