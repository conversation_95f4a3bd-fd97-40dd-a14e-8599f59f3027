"use client";

import { useCounselors } from "@/app/hooks/public/useCounselors";
import { useDebounce } from "@/app/hooks/student/useDebounce";
import { Badge } from "@components/ui/badge";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etHeader,
  SheetT<PERSON>le,
  Sheet<PERSON>rigger,
} from "@components/ui/sheet";
import { ChevronDown, FilterIcon, Search, X } from "lucide-react";
import { useEffect, useMemo, useState, useRef } from "react";

// Country mapping with display name to country code
const COUNTRY_MAPPING = {
  "United States": "US",
  "United Kingdom": "GB",
  Canada: "CA",
  Australia: "AU",
  India: "IN",
  Singapore: "SG",
  "Hong Kong": "HK",
} as const;

// Reverse mapping for display purposes
const COUNTRY_CODE_TO_NAME = Object.entries(COUNTRY_MAPPING).reduce(
  (acc, [name, code]) => {
    acc[code] = name;
    return acc;
  },
  {} as Record<string, string>
);

// Available filters configuration
const SERVICES = [
  "Free 15 Minutes Intro Call",
  "Personal Statement Guidance",
  "Essay Review",
  "University Shortlisting",
  "Extra Curricular Profile Building",
  "Supplementary Essay Guidance",
  "Financial Aid Advice",
  "Interview Preparation",
] as const;

// Use the keys from the mapping for type safety
const COUNTRIES = Object.keys(COUNTRY_MAPPING) as readonly string[];

const UNIVERSITIES = [
  "University of Oxford",
  "University of Cambridge",
  "Harvard University",
  "Stanford University",
  "Yale University",
  "University of Pennsylvania",
  "University of British Columbia",
  "University of Toronto",
  "Princeton University",
  "Brown University",
  "Rice University",
  "Bryn Mawr College",
  "Colby College",
  "Colorado College",
  "Imperial College London",
  "yale-Nus",
  "NUS",
] as const;

export function FiltersSidebar() {
  const [isOpen, setIsOpen] = useState<Record<string, boolean>>({
    services: true,
    countries: false,
    universities: false,
    pricing: true,
  });

  const {
    filters: { service_type, country, university, name },
    setFilters,
    fetchAllCounselors,
    setLoading,
  } = useCounselors();

  // Keep using the ref approach
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Create a memoized filters object
  const currentFilters = useMemo(
    () => ({
      service_type,
      country,
      university,
      name,
    }),
    [service_type, country, university, name]
  );

  // Debounce filter changes
  const debouncedFilters = useDebounce(currentFilters, 1000);

  // Set initial search value in input field
  useEffect(() => {
    if (name && searchInputRef.current) {
      searchInputRef.current.value = name;
    }
  }, []);

  // Update filters when debounced values change
  useEffect(() => {
    setFilters({
      ...debouncedFilters,
    });
    fetchAllCounselors();
  }, [debouncedFilters, setFilters, fetchAllCounselors]);

  // Helper function to toggle an item in an array
  const toggleArrayItem = (
    array: string[] | undefined,
    item: string
  ): string[] => {
    if (!array) return [item];
    return array.includes(item)
      ? array.filter((i) => i !== item)
      : [...array, item];
  };

  // Convert country display name to code
  const getCountryCode = (countryName: string): string => {
    return (
      COUNTRY_MAPPING[countryName as keyof typeof COUNTRY_MAPPING] ||
      countryName
    );
  };

  // Update local state immediately without triggering API calls
  const updateFilters = (
    newFilter: Partial<{
      service_type: string[] | undefined;
      country: string[] | undefined;
      university: string[] | undefined;
      min_hourly_rate: number;
      max_hourly_rate: number;
      name: string;
    }>
  ) => {
    setLoading(true);
    const updatedFilters = {
      service_type,
      country,
      university,
      name,
      ...newFilter,
    };

    // Update global filters state
    setFilters(updatedFilters);
  };

  // Toggle a service type in the filters
  const toggleServiceType = (serviceItem: string) => {
    updateFilters({
      service_type: toggleArrayItem(service_type as string[], serviceItem),
    });
  };

  // Toggle a country in the filters, converting display name to code
  const toggleCountry = (countryItem: string) => {
    const countryCode = getCountryCode(countryItem);
    updateFilters({
      country: toggleArrayItem(country as string[], countryCode),
    });
  };

  // Toggle a university in the filters
  const toggleUniversity = (universityItem: string) => {
    updateFilters({
      university: toggleArrayItem(university as string[], universityItem),
    });
  };

  // Toggle sections
  const toggleSection = (section: string) => {
    setIsOpen((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  // Handle search submit
  const handleSearch = () => {
    setLoading(true);
    const searchValue = searchInputRef.current?.value || "";

    // Important: Don't update the filter if it's the same as current name
    // This prevents unnecessary re-renders
    if (searchValue !== name) {
      updateFilters({
        name: searchValue,
      });
    } else {
      // Just fetch counselors if the search value is unchanged
      fetchAllCounselors();
    }
  };

  // Clear all filters
  const clearFilters = () => {
    // Clear the input field directly
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }

    const emptyFilters = {
      service_type: undefined,
      country: undefined,
      university: undefined,
      name: "",
    };
    setFilters(emptyFilters);
    fetchAllCounselors();
  };

  // Count active filters
  const activeFilterCount =
    (service_type?.length || 0) +
    (country?.length || 0) +
    (university?.length || 0) +
    (name ? 1 : 0);

  const FilterContent = () => (
    <div className="space-y-4">
      {/* Name Search Input with button on right side */}
      <div className="relative flex items-center">
        <Input
          ref={searchInputRef}
          type="text"
          placeholder="Search counselors..."
          className="pr-12 w-full"
          defaultValue={name || ""}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSearch();
            }
          }}
        />
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="absolute right-0 top-0 h-full px-3"
          onClick={handleSearch}
        >
          <Search className="h-4 w-4" />
        </Button>
      </div>

      {/* Services Section */}
      <div className="border-b border-gray-100">
        <button
          onClick={() => toggleSection("services")}
          className="flex w-full items-center justify-between py-4 text-left"
        >
          <div className="flex items-center gap-2">
            <span className="text-base">Services</span>
            {service_type && service_type.length > 0 && (
              <Badge variant="secondary" className="h-5 px-2">
                {service_type.length}
              </Badge>
            )}
          </div>
          <ChevronDown
            className={`h-4 w-4 transition-transform ${
              isOpen.services ? "rotate-180" : ""
            }`}
          />
        </button>

        {isOpen.services && (
          <div className="pb-4">
            <div className="space-y-2">
              {SERVICES.map((serviceItem) => (
                <label
                  key={serviceItem}
                  className="flex items-center gap-2 rounded-lg p-2 hover:bg-gray-50 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    name="service_type"
                    checked={service_type?.includes(serviceItem)}
                    onChange={() => toggleServiceType(serviceItem)}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <span className="text-sm">{serviceItem}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Countries Section */}
      <div className="border-b border-gray-100">
        <button
          onClick={() => toggleSection("countries")}
          className="flex w-full items-center justify-between py-4 text-left"
        >
          <div className="flex items-center gap-2">
            <span className="text-base">Countries</span>
            {country && country.length > 0 && (
              <Badge variant="secondary" className="h-5 px-2">
                {country.length}
              </Badge>
            )}
          </div>
          <ChevronDown
            className={`h-4 w-4 transition-transform ${
              isOpen.countries ? "rotate-180" : ""
            }`}
          />
        </button>

        {isOpen.countries && (
          <div className="pb-4">
            <div className="space-y-2">
              {COUNTRIES.map((countryItem) => (
                <label
                  key={countryItem}
                  className="flex items-center gap-2 rounded-lg p-2 hover:bg-gray-50 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={country?.includes(getCountryCode(countryItem))}
                    onChange={() => toggleCountry(countryItem)}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <span className="text-sm">{countryItem}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Universities Section */}
      <div className="border-b border-gray-100">
        <button
          onClick={() => toggleSection("universities")}
          className="flex w-full items-center justify-between py-4 text-left"
        >
          <div className="flex items-center gap-2">
            <span className="text-base">Universities</span>
            {university && university.length > 0 && (
              <Badge variant="secondary" className="h-5 px-2">
                {university.length}
              </Badge>
            )}
          </div>
          <ChevronDown
            className={`h-4 w-4 transition-transform ${
              isOpen.universities ? "rotate-180" : ""
            }`}
          />
        </button>

        {isOpen.universities && (
          <div className="pb-4">
            <div className="space-y-2">
              {UNIVERSITIES.map((universityItem) => (
                <label
                  key={universityItem}
                  className="flex items-center gap-2 rounded-lg p-2 hover:bg-gray-50 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={university?.includes(universityItem)}
                    onChange={() => toggleUniversity(universityItem)}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <span className="text-sm">{universityItem}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>

      {activeFilterCount > 0 && (
        <div className="flex items-center justify-between pt-4">
          <button
            onClick={clearFilters}
            className="flex items-center gap-2 text-sm text-gray-500"
          >
            <X className="h-4 w-4" />
            Clear filters
          </button>
          <span className="text-sm text-gray-500">
            {activeFilterCount} selected
          </span>
        </div>
      )}
    </div>
  );

  return (
    <>
      {/* Desktop sidebar */}
      <div className="hidden border-r border-gray-100 lg:block">
        <div className="sticky top-8 w-64 px-4">
          <FilterContent />
        </div>
      </div>

      {/* Mobile sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="sm" className="lg:hidden">
            <FilterIcon className="mr-2 h-4 w-4" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 px-2">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="overflow-y-auto">
          <SheetHeader>
            <SheetTitle>Filters</SheetTitle>
          </SheetHeader>
          <div className="mt-4">
            <FilterContent />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
