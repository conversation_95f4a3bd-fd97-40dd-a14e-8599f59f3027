"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";
import apiClient from "@lib/apiClient";
import { toast } from "react-toastify";
import {
  handleGoogleLogin,
  handleLinkedInLogin,
  initiateLinkedInLogin,
} from "@/app/utils/auth";

interface User {
  avatar?: string;
  firstName?: string;
  email?: string;
}

interface AuthState {
  token: string | null;
  user: User | null;
  userType: string | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  signup: (data: SignupData) => Promise<void>;
  verifyEmail: (email: string, code: string) => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (
    email: string,
    code: string,
    newPassword: string
  ) => Promise<void>;
  logout: () => void;
  signupData: Partial<SignupData> | null;
  initiateSignup: (
    data: { first_name: string; last_name: string; email: string },
    userType: "student" | "counselor"
  ) => Promise<void>;
  resetSignupData: () => void;
  completeSignup: (password: string) => Promise<string>;
  resendVerificationCode: (email: string) => Promise<void>;
  verifySignupCode: (email: string, code: string) => Promise<void>;
  googleAuth: (
    token: string,
    userType: "student" | "counselor" | undefined
  ) => Promise<void>;
  linkedinAuth: (
    code: string,
    userType: "student" | "counselor" | undefined
  ) => Promise<void>;
  initiateLinkedInAuth: (userType: "student" | "counselor" | undefined) => void;
  isAuthenticated: boolean;
  auth: () => boolean;
}

interface SignupData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  userType: "student" | "counselor";
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      token: null as string | null,
      user: null as User | null,
      userType: null as string | null,
      loading: false,
      signupData: null,
      isAuthenticated: false,

      auth: () => {
        const token = localStorage.getItem("access_token");
        const isAuthenticated = !!token;
        set({ isAuthenticated });
        return isAuthenticated;
      },

      login: async (username, password) => {
        try {
          set({ loading: true });
          const formData = new URLSearchParams();
          formData.append("username", username);
          formData.append("password", password);

          const response = await apiClient.post("/auth/signin", formData, {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          });

          const { access_token, token_type, user_type } = response.data;
          localStorage.setItem("access_token", access_token);
          set({
            token: access_token,
            userType: user_type,
            isAuthenticated: true,
          });

          toast.success("Logged in successfully!");
        } catch (error: any) {
          toast.error(error.response?.data?.detail || "Login failed");
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      signup: async (data) => {
        try {
          set({ loading: true });
          const response = await apiClient.post(
            `/auth/signup/${data.userType}`,
            {
              email: data.email,
              password: data.password,
              first_name: data.first_name,
              last_name: data.last_name,
            }
          );

          if (response.data.id) {
            localStorage.setItem("email", data.email);
            toast.success("Verification code sent to your email!");
          }
        } catch (error: any) {
          toast.error(
            error.response?.data?.detail?.[0]?.msg || "Signup failed"
          );
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      verifyEmail: async (email, code) => {
        try {
          set({ loading: true });
          await apiClient.post("/auth/verify-code", { email, code });
          toast.success("Code verified successfully!");
        } catch (error: any) {
          toast.error(
            error.response?.data?.detail?.[0]?.msg || "Verification failed"
          );
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      forgotPassword: async (email) => {
        try {
          set({ loading: true });
          await apiClient.post("/auth/forgot-password", { email });
          toast.success("Reset code sent to your email!");
        } catch (error: any) {
          toast.error(
            error.response?.data?.detail?.[0]?.msg ||
              "Failed to send reset code"
          );
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      resetPassword: async (email, code, newPassword) => {
        try {
          set({ loading: true });
          await apiClient.post("/auth/reset-password", {
            email,
            code,
            new_password: newPassword,
          });
          toast.success("Password reset successfully!");
        } catch (error: any) {
          toast.error(
            error.response?.data?.detail?.[0]?.msg || "Password reset failed"
          );
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      logout: () => {
        localStorage.removeItem("access_token");
        set({ token: null, userType: null, isAuthenticated: false });
      },

      initiateSignup: async (data, userType = "student") => {
        try {
          set({ loading: true });
          const response = await apiClient.post("/auth/signup/initiate", {
            ...data,
            user_type: userType,
          });
          set({ signupData: { ...data, userType } });
          toast.success(response.data.message);
        } catch (error: any) {
          toast.error(
            error.response?.data?.detail || "Failed to initiate signup"
          );
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      resetSignupData: () => {
        set({ signupData: null });
      },

      completeSignup: async (password: string) => {
        const { signupData } = get();
        if (!signupData?.email) {
          toast.error("Missing signup data");
          return;
        }

        try {
          set({ loading: true });
          const response = await apiClient.post("/auth/signup/complete", {
            email: signupData.email,
            password,
          });

          const { access_token, token_type, user_type } = response.data;

          // Store token and update auth state
          localStorage.setItem("access_token", access_token);
          set({
            token: access_token,
            userType: user_type,
            isAuthenticated: true,
            signupData: null,
          });

          toast.success("Account created successfully!");

          // Return user type so pages can redirect appropriately
          return user_type;
        } catch (error: any) {
          toast.error(
            error.response?.data?.detail || "Failed to complete signup"
          );
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      resendVerificationCode: async (email: string) => {
        try {
          set({ loading: true });
          const response = await apiClient.post("/auth/signup/resend", {
            email,
          });
          toast.success(response.data.message);
        } catch (error: any) {
          toast.error(error.response?.data?.detail || "Failed to resend code");
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      verifySignupCode: async (email: string, code: string) => {
        try {
          set({ loading: true });
          const response = await apiClient.post("/auth/signup/verify", {
            email,
            code,
          });
          toast.success(response.data.message);
        } catch (error: any) {
          toast.error(
            error.response?.data?.detail || "Invalid verification code"
          );
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      googleAuth: async (
        token: string,
        userType: "student" | "counselor" | undefined
      ) => {
        try {
          set({ loading: true });
          const response = await handleGoogleLogin(token, userType);

          if (response.access_token) {
            localStorage.setItem("access_token", response.access_token);
            set({
              token: response.access_token,
              userType: response.user_type,
              isAuthenticated: true,
            });

            // Get user info to check profile completion status
            const userInfoResponse = await apiClient.get(
              `/user/me?timezone=${
                Intl.DateTimeFormat().resolvedOptions().timeZone
              }`
            );
            const userInfo = userInfoResponse.data;

            // Success message based on whether it's a new account or not
            if (response.is_new_user) {
              toast.success("Account created with Google successfully!");
            } else {
              toast.success("Logged in with Google successfully!");
            }

            // Redirect based on user type and profile completion
            if (userInfo.userType === "counselor") {
              if (userInfo.is_verified) {
                window.location.href = "/counselor/dashboard";
              } else if (userInfo.isProfileComplete) {
                window.location.href = "/counselor/profile-complete";
              } else {
                window.location.href = "/counselor/onboarding";
              }
            } else {
              if (userInfo.isProfileComplete) {
                window.location.href = "/student/dashboard";
              } else {
                window.location.href = "/student/onboarding";
              }
            }
          }
        } catch (error: any) {
          // Let the error propagate to the component
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      linkedinAuth: async (
        code: string,
        userType: "student" | "counselor" | undefined
      ) => {
        try {
          set({ loading: true });
          const response = await handleLinkedInLogin(code, userType);

          if (response.access_token) {
            localStorage.setItem("access_token", response.access_token);
            set({
              token: response.access_token,
              userType: response.user_type,
              isAuthenticated: true,
            });

            // Get user info to check profile completion status
            const userInfoResponse = await apiClient.get(
              `/user/me?timezone=${
                Intl.DateTimeFormat().resolvedOptions().timeZone
              }`
            );
            const userInfo = userInfoResponse.data;

            // Success message based on whether it's a new account or not
            if (response.is_new_user) {
              toast.success("Account created with LinkedIn successfully!");
            } else {
              toast.success("Logged in with LinkedIn successfully!");
            }

            // Redirect based on user type and profile completion
            if (userInfo.userType === "counselor") {
              if (userInfo.is_verified) {
                window.location.href = "/counselor/dashboard";
              } else if (userInfo.isProfileComplete) {
                window.location.href = "/counselor/profile-complete";
              } else {
                window.location.href = "/counselor/onboarding";
              }
            } else {
              if (userInfo.isProfileComplete) {
                window.location.href = "/student/dashboard";
              } else {
                window.location.href = "/student/onboarding";
              }
            }
          }
        } catch (error: any) {
          // Let the error propagate to the component
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      initiateLinkedInAuth: (userType: "student" | "counselor" | undefined) => {
        initiateLinkedInLogin(userType);
      },
    }),
    {
      name: "auth-storage",
    }
  )
);
