import { PersonalInfo } from "@/app/types/student/profile";
import { GoalsSkeleton } from "./skeleton";
import { Pencil } from "lucide-react";

export const GoalsBlock = ({
  personalInfo,
  onEditClick,
}: {
  personalInfo: PersonalInfo | null;
  onEditClick: () => void;
}) => {
  return !personalInfo ? (
    <GoalsSkeleton />
  ) : (
    <div className="space-y-3 sm:space-y-4 rounded-xl border p-2.5 sm:p-4 lg:p-6 ">
      <div className="flex justify-between items-center">
        <h2 className="text-base sm:text-xl font-semibold">Goals</h2>
        <button
          onClick={onEditClick}
          className="p-3 text-gray-700 text-base sm:text-lg font-semibold hover:bg-gray-200 rounded-xl bg-gray-100 transition-all whitespace-nowrap"
        >
          <Pencil className="w-5 h-5 sm:w-6 sm:h-6" />
        </button>
      </div>
      <p className="text-gray-600 leading-relaxed text-sm sm:text-base">
        {personalInfo.goals || "No Goals provided"}
      </p>
    </div>
  );
};
