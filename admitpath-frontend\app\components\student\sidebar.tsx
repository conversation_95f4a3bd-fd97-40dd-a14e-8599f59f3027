"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Calendar,
  User,
  Package,
  CreditCard,
  BookOpen,
  MessageSquare,
  Menu,
  Link as LinkIcon,
} from "lucide-react";
import { useState, useEffect } from "react";
import Image from "next/image";
import { useWebSocketChat } from "@/app/hooks/useWebSocketChat";

interface NavItem {
  label: string;
  href: string;
  icon: React.ReactNode;
}

const navItems: NavItem[] = [
  {
    label: "Overview",
    href: "/student/dashboard",
    icon: <Home className="w-6 h-6" />,
  },
  {
    label: "Sessions",
    href: "/student/dashboard/sessions",
    icon: <Calendar className="w-6 h-6" />,
  },
  {
    label: "Profile",
    href: "/student/dashboard/profile",
    icon: <User className="w-6 h-6" />,
  },
  {
    label: "Packages",
    href: "/student/dashboard/packages",
    icon: <Package className="w-6 h-6" />,
  },
  {
    label: "Payments",
    href: "/student/dashboard/payments",
    icon: <CreditCard className="w-6 h-6" />,
  },
  {
    label: "Resources",
    href: "/student/dashboard/resources",
    icon: <BookOpen className="w-6 h-6" />,
  },
  {
    label: "Messages",
    href: "/student/dashboard/messages",
    icon: <MessageSquare className="w-6 h-6" />,
  },
];

interface SidebarProps {
  isOpen: boolean;
  onMenuClick: () => void;
}

const Sidebar = ({ isOpen, onMenuClick }: SidebarProps) => {
  const pathname = usePathname();
  const [isHovered, setIsHovered] = useState(false);
  const { getUnseenChannelsCount, unseenChannelsCount } = useWebSocketChat();

  useEffect(() => {
    const fetchUnseenCount = async () => {
      await getUnseenChannelsCount();
    };

    fetchUnseenCount();
  }, []);

  return (
    <aside
      className={`
        fixed top-0 left-0 h-full bg-white
        transition-all duration-300 ease-in-out
        ${isOpen ? "w-64 translate-x-0" : "w-20 -translate-x-full"}
        md:translate-x-0
        md:w-20 xl:w-64
        ${isHovered ? "md:w-64" : ""}
        md:shadow-lg
        shadow-2xl
        z-10
        flex flex-col

        md:overflow-hidden
        overflow-y-auto
      `}
      onMouseEnter={() => window.innerWidth >= 768 && setIsHovered(true)}
      onMouseLeave={() => window.innerWidth >= 768 && setIsHovered(false)}
    >
      <div className="sticky top-0 bg-white z-20 p-4">
        <div className="flex items-center justify-center transition-all duration-300">
          <Link href="/">
            <Image
              width={100}
              height={100}
              src="/icons/logo.png"
              alt="Logo"
              className="transition-all duration-300 w-[3.8rem] h-[3.8rem] md:w-[3.6rem] md:h-[3.6rem] xl:w-[4.8rem] xl:h-[4.8rem] flex-grow-0"
            />
          </Link>
        </div>
        {isOpen && (
          <button
            className="md:hidden absolute right-4 top-1/2 -translate-y-1/2 rounded-lg bg-gray-50 hover:bg-gray-100"
            onClick={onMenuClick}
          >
            <Menu className="w-6 h-6" />
          </button>
        )}
      </div>

      <div className="flex-1 flex flex-col min-h-0">
        <nav className="flex-1 gap-y-4 px-3 flex flex-col">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`
                flex items-center p-4 transition-colors rounded-xl
                ${
                  pathname === item.href
                    ? "bg-gray-900 text-white"
                    : "hover:bg-gray-100"
                }
              `}
              onClick={(e) => {
                onMenuClick();
                if (window.innerWidth >= 768) {
                  setIsHovered(false);
                }
              }}
            >
              <span className="flex items-center justify-center">
                {item.icon}
              </span>
              <span
                className={`
                  mx-4 whitespace-nowrap transition-opacity duration-300
                  ${isOpen ? "opacity-100" : "opacity-0"}
                  ${isHovered ? "md:opacity-100" : "md:opacity-0"}
                  xl:opacity-100
                `}
              >
                {item.label}
                {item.label === "Messages" && unseenChannelsCount > 0 && (
                  <span className="ml-2 px-2 py-1 text-xs bg-green-500 text-white rounded-full">
                    {unseenChannelsCount}
                  </span>
                )}
              </span>
            </Link>
          ))}
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
