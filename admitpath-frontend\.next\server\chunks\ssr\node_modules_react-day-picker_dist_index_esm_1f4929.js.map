{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.esm.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/node_modules/.pnpm/%40rollup%2Bplugin-typescript%4011.1.5_rollup%404.9.1_tslib%402.6.2_typescript%405.3.3/node_modules/tslib/tslib.es6.js", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/types/DayPickerMultiple.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/types/DayPickerRange.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/types/DayPickerSingle.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/defaultClassNames.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/formatters/formatCaption.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/formatters/formatDay.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/formatters/formatMonthCaption.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/formatters/formatWeekNumber.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/formatters/formatWeekdayName.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/formatters/formatYearCaption.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/labels/labelDay.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/labels/labelMonthDropdown.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/labels/labelNext.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/labels/labelPrevious.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/labels/labelWeekday.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/labels/labelWeekNumber.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/labels/labelYearDropdown.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/defaultContextValues.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/utils/parseFromToProps.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/DayPicker/DayPickerContext.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/CaptionLabel/CaptionLabel.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/IconDropdown/IconDropdown.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Dropdown/Dropdown.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/MonthsDropdown/MonthsDropdown.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/YearsDropdown/YearsDropdown.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useControlledValue/useControlledValue.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Navigation/utils/getInitialMonth.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Navigation/useNavigationState.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Navigation/utils/getDisplayMonths.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Navigation/utils/getNextMonth.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Navigation/utils/getPreviousMonth.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Navigation/NavigationContext.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/CaptionDropdowns/CaptionDropdowns.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/IconLeft/IconLeft.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/IconRight/IconRight.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Button/Button.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Navigation/Navigation.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/CaptionNavigation/CaptionNavigation.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Caption/Caption.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Footer/Footer.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/HeadRow/utils/getWeekdays.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/HeadRow/HeadRow.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Head/Head.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/DayContent/DayContent.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/SelectMultiple/SelectMultipleContext.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/SelectRange/utils/addToRange.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/SelectRange/SelectRangeContext.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Modifiers/utils/matcherToArray.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Modifiers/utils/getCustomModifiers.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/types/Modifiers.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Modifiers/utils/getInternalModifiers.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Modifiers/ModifiersContext.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/types/Matchers.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Modifiers/utils/isDateInRange.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Modifiers/utils/isMatch.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Modifiers/utils/getActiveModifiers.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Focus/utils/getInitialFocusTarget.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Focus/utils/getNextFocus.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/Focus/FocusContext.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useActiveModifiers/useActiveModifiers.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/SelectSingle/SelectSingleContext.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useDayEventHandlers/useDayEventHandlers.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useSelectedDays/useSelectedDays.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useDayRender/utils/getDayClassNames.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useDayRender/utils/getDayStyle.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useDayRender/useDayRender.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Day/Day.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/WeekNumber/WeekNumber.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Row/Row.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Table/utils/daysToMonthWeeks.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Table/utils/getMonthWeeks.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Table/Table.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useId/useId.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Month/Month.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Months/Months.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/components/Root/Root.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/contexts/RootProvider.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/DayPicker.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useInput/utils/isValidDate.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/hooks/useInput/useInput.ts", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/react-day-picker/src/types/DayPickerDefault.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\nimport { SelectMultipleEventHandler } from './EventHandlers';\n\n/** The props for the {@link DayPicker} component when using `mode=\"multiple\"`. */\nexport interface DayPickerMultipleProps extends DayPickerBase {\n  mode: 'multiple';\n  /** The selected days. */\n  selected?: Date[] | undefined;\n  /** Event fired when a days added or removed to the selection. */\n  onSelect?: SelectMultipleEventHandler;\n  /** The minimum amount of days that can be selected. */\n  min?: number;\n  /** The maximum amount of days that can be selected. */\n  max?: number;\n}\n\n/** Returns true when the props are of type {@link DayPickerMultipleProps}. */\nexport function isDayPickerMultiple(\n  props: DayPickerProps | DayPickerContextValue\n): props is DayPickerMultipleProps {\n  return props.mode === 'multiple';\n}\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\nimport { SelectRangeEventHandler } from './EventHandlers';\nimport { DateRange } from './Matchers';\n\n/** The props for the {@link DayPicker} component when using `mode=\"range\"`. */\nexport interface DayPickerRangeProps extends DayPickerBase {\n  mode: 'range';\n  /** The selected range of days. */\n  selected?: DateRange | undefined;\n  /** Event fired when a range (or a part of the range) is selected. */\n  onSelect?: SelectRangeEventHandler;\n  /** The minimum amount of days that can be selected. */\n  min?: number;\n  /** The maximum amount of days that can be selected. */\n  max?: number;\n}\n\n/** Returns true when the props are of type {@link DayPickerRangeProps}. */\nexport function isDayPickerRange(\n  props: DayPickerProps | DayPickerContextValue\n): props is DayPickerRangeProps {\n  return props.mode === 'range';\n}\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\nimport { SelectSingleEventHandler } from './EventHandlers';\n\n/** The props for the {@link DayPicker} component when using `mode=\"single\"`. */\nexport interface DayPickerSingleProps extends DayPickerBase {\n  mode: 'single';\n  /** The selected day. */\n  selected?: Date | undefined;\n  /** Event fired when a day is selected. */\n  onSelect?: SelectSingleEventHandler;\n  /** Make the selection required. */\n  required?: boolean;\n}\n\n/** Returns true when the props are of type {@link DayPickerSingleProps}. */\nexport function isDayPickerSingle(\n  props: DayPickerProps | DayPickerContextValue\n): props is DayPickerSingleProps {\n  return props.mode === 'single';\n}\n", "import { ClassNames } from 'types/Styles';\n\n/**\n * The name of the default CSS classes.\n */\nexport const defaultClassNames: Required<ClassNames> = {\n  root: 'rdp',\n  multiple_months: 'rdp-multiple_months',\n  with_weeknumber: 'rdp-with_weeknumber',\n  vhidden: 'rdp-vhidden',\n  button_reset: 'rdp-button_reset',\n  button: 'rdp-button',\n\n  caption: 'rdp-caption',\n\n  caption_start: 'rdp-caption_start',\n  caption_end: 'rdp-caption_end',\n  caption_between: 'rdp-caption_between',\n  caption_label: 'rdp-caption_label',\n\n  caption_dropdowns: 'rdp-caption_dropdowns',\n\n  dropdown: 'rdp-dropdown',\n  dropdown_month: 'rdp-dropdown_month',\n  dropdown_year: 'rdp-dropdown_year',\n  dropdown_icon: 'rdp-dropdown_icon',\n\n  months: 'rdp-months',\n  month: 'rdp-month',\n  table: 'rdp-table',\n  tbody: 'rdp-tbody',\n  tfoot: 'rdp-tfoot',\n\n  head: 'rdp-head',\n  head_row: 'rdp-head_row',\n  head_cell: 'rdp-head_cell',\n\n  nav: 'rdp-nav',\n  nav_button: 'rdp-nav_button',\n  nav_button_previous: 'rdp-nav_button_previous',\n  nav_button_next: 'rdp-nav_button_next',\n\n  nav_icon: 'rdp-nav_icon',\n\n  row: 'rdp-row',\n  weeknumber: 'rdp-weeknumber',\n  cell: 'rdp-cell',\n\n  day: 'rdp-day',\n  day_today: 'rdp-day_today',\n  day_outside: 'rdp-day_outside',\n  day_selected: 'rdp-day_selected',\n  day_disabled: 'rdp-day_disabled',\n  day_hidden: 'rdp-day_hidden',\n  day_range_start: 'rdp-day_range_start',\n  day_range_end: 'rdp-day_range_end',\n  day_range_middle: 'rdp-day_range_middle'\n};\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the caption.\n */\nexport function formatCaption(\n  month: Date,\n  options?: { locale?: Locale }\n): string {\n  return format(month, 'LLLL y', options);\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the Day button.\n */\nexport function formatDay(day: Date, options?: { locale?: Locale }): string {\n  return format(day, 'd', options);\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the Month caption.\n */\nexport function formatMonthCaption(\n  month: Date,\n  options?: { locale?: Locale }\n): string {\n  return format(month, 'LLLL', options);\n}\n", "/**\n * The default formatter for the week number.\n */\nexport function formatWeekNumber(weekNumber: number): string {\n  return `${weekNumber}`;\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the name of the weekday.\n */\nexport function formatWeekdayName(\n  weekday: Date,\n  options?: { locale?: Locale }\n): string {\n  return format(weekday, 'cccccc', options);\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the Year caption.\n */\nexport function formatYearCaption(\n  year: Date,\n  options?: {\n    locale?: Locale;\n  }\n): string {\n  return format(year, 'yyyy', options);\n}\n", "import { format } from 'date-fns';\n\nimport { DayLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for the day button.\n */\nexport const labelDay: DayLabel = (day, activeModifiers, options): string => {\n  return format(day, 'do MMMM (EEEE)', options);\n};\n", "/**\n * The default ARIA label for the WeekNumber element.\n */\nexport const labelMonthDropdown = (): string => {\n  return 'Month: ';\n};\n", "import { NavButtonLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for next month button in navigation\n */\nexport const labelNext: NavButtonLabel = (): string => {\n  return 'Go to next month';\n};\n", "import { NavButtonLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for previous month button in navigation\n */\nexport const labelPrevious: NavButtonLabel = (): string => {\n  return 'Go to previous month';\n};\n", "import { format } from 'date-fns';\n\nimport { WeekdayLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for the Weekday element.\n */\nexport const labelWeekday: WeekdayLabel = (day, options): string => {\n  return format(day, 'cccc', options);\n};\n", "import { WeekNumberLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nexport const labelWeekNumber: WeekNumberLabel = (n): string => {\n  return `Week n. ${n}`;\n};\n", "/**\n * The default ARIA label for the WeekNumber element.\n */\nexport const labelYearDropdown = (): string => {\n  return 'Year: ';\n};\n", "import { enUS } from 'date-fns/locale';\n\nimport { CaptionLayout } from 'components/Caption';\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { defaultClassNames } from './defaultClassNames';\nimport * as formatters from './formatters';\nimport * as labels from './labels';\n\nexport type DefaultContextProps =\n  | 'captionLayout'\n  | 'classNames'\n  | 'formatters'\n  | 'locale'\n  | 'labels'\n  | 'modifiersClassNames'\n  | 'modifiers'\n  | 'numberOfMonths'\n  | 'styles'\n  | 'today'\n  | 'mode';\n\nexport type DefaultContextValues = Pick<\n  DayPickerContextValue,\n  DefaultContextProps\n>;\n/**\n * Returns the default values to use in the DayPickerContext, in case they are\n * not passed down with the DayPicker initial props.\n */\nexport function getDefaultContextValues(): DefaultContextValues {\n  const captionLayout: CaptionLayout = 'buttons';\n  const classNames = defaultClassNames;\n  const locale = enUS;\n  const modifiersClassNames = {};\n  const modifiers = {};\n  const numberOfMonths = 1;\n  const styles = {};\n  const today = new Date();\n\n  return {\n    captionLayout,\n    classNames,\n    formatters,\n    labels,\n    locale,\n    modifiersClassNames,\n    modifiers,\n    numberOfMonths,\n    styles,\n    today,\n    mode: 'default'\n  };\n}\n", "import { endOfMonth, startOfDay, startOfMonth } from 'date-fns';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\n\n/** Return the `fromDate` and `toDate` prop values values parsing the DayPicker props. */\nexport function parseFromToProps(\n  props: Pick<\n    DayPickerBase,\n    'fromYear' | 'toYear' | 'fromDate' | 'toDate' | 'fromMonth' | 'toMonth'\n  >\n): { fromDate: Date | undefined; toDate: Date | undefined } {\n  const { fromYear, toYear, fromMonth, toMonth } = props;\n  let { fromDate, toDate } = props;\n\n  if (fromMonth) {\n    fromDate = startOfMonth(fromMonth);\n  } else if (fromYear) {\n    fromDate = new Date(fromYear, 0, 1);\n  }\n  if (toMonth) {\n    toDate = endOfMonth(toMonth);\n  } else if (toYear) {\n    toDate = new Date(toYear, 11, 31);\n  }\n\n  return {\n    fromDate: fromDate ? startOfDay(fromDate) : undefined,\n    toDate: toDate ? startOfDay(toDate) : undefined\n  };\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { Locale } from 'date-fns';\nimport { DayPickerProps } from 'DayPicker';\n\nimport { CaptionLayout } from 'components/Caption';\nimport { DayPickerBase, DaySelectionMode } from 'types/DayPickerBase';\nimport {\n  DayPickerMultipleProps,\n  isDayPickerMultiple\n} from 'types/DayPickerMultiple';\nimport { DayPickerRangeProps, isDayPickerRange } from 'types/DayPickerRange';\nimport { DayPickerSingleProps, isDayPickerSingle } from 'types/DayPickerSingle';\nimport { Formatters } from 'types/Formatters';\nimport { Labels } from 'types/Labels';\nimport { Matcher } from 'types/Matchers';\nimport { DayModifiers, ModifiersClassNames } from 'types/Modifiers';\nimport { ClassNames, Styles } from 'types/Styles';\n\nimport { getDefaultContextValues } from './defaultContextValues';\nimport { parseFromToProps } from './utils';\n\n/**\n * The value of the {@link DayPickerContext} extends the props from DayPicker\n * with default and cleaned up values.\n */\nexport interface DayPickerContextValue extends DayPickerBase {\n  mode: DaySelectionMode;\n  onSelect?:\n    | DayPickerSingleProps['onSelect']\n    | DayPickerMultipleProps['onSelect']\n    | DayPickerRangeProps['onSelect'];\n  required?: boolean;\n  min?: number;\n  max?: number;\n  selected?: Matcher | Matcher[];\n\n  captionLayout: CaptionLayout;\n  classNames: Required<ClassNames>;\n  formatters: Formatters;\n  labels: Labels;\n  locale: Locale;\n  modifiersClassNames: ModifiersClassNames;\n  modifiers: DayModifiers;\n  numberOfMonths: number;\n  styles: Styles;\n  today: Date;\n}\n\n/**\n * The DayPicker context shares the props passed to DayPicker within internal\n * and custom components. It is used to set the default values and perform\n * one-time calculations required to render the days.\n *\n * Access to this context from the {@link useDayPicker} hook.\n */\nexport const DayPickerContext = createContext<\n  DayPickerContextValue | undefined\n>(undefined);\n\n/** The props for the {@link DayPickerProvider}. */\nexport interface DayPickerProviderProps {\n  /** The initial props from the DayPicker component. */\n  initialProps: DayPickerProps;\n  children?: ReactNode;\n}\n/**\n * The provider for the {@link DayPickerContext}, assigning the defaults from the\n * initial DayPicker props.\n */\nexport function DayPickerProvider(props: DayPickerProviderProps): JSX.Element {\n  const { initialProps } = props;\n\n  const defaultContextValues = getDefaultContextValues();\n\n  const { fromDate, toDate } = parseFromToProps(initialProps);\n\n  let captionLayout =\n    initialProps.captionLayout ?? defaultContextValues.captionLayout;\n  if (captionLayout !== 'buttons' && (!fromDate || !toDate)) {\n    // When no from/to dates are set, the caption is always buttons\n    captionLayout = 'buttons';\n  }\n\n  let onSelect;\n  if (\n    isDayPickerSingle(initialProps) ||\n    isDayPickerMultiple(initialProps) ||\n    isDayPickerRange(initialProps)\n  ) {\n    onSelect = initialProps.onSelect;\n  }\n\n  const value: DayPickerContextValue = {\n    ...defaultContextValues,\n    ...initialProps,\n    captionLayout,\n    classNames: {\n      ...defaultContextValues.classNames,\n      ...initialProps.classNames\n    },\n    components: {\n      ...initialProps.components\n    },\n    formatters: {\n      ...defaultContextValues.formatters,\n      ...initialProps.formatters\n    },\n    fromDate,\n    labels: {\n      ...defaultContextValues.labels,\n      ...initialProps.labels\n    },\n    mode: initialProps.mode || defaultContextValues.mode,\n    modifiers: {\n      ...defaultContextValues.modifiers,\n      ...initialProps.modifiers\n    },\n    modifiersClassNames: {\n      ...defaultContextValues.modifiersClassNames,\n      ...initialProps.modifiersClassNames\n    },\n    onSelect,\n    styles: {\n      ...defaultContextValues.styles,\n      ...initialProps.styles\n    },\n    toDate\n  };\n\n  return (\n    <DayPickerContext.Provider value={value}>\n      {props.children}\n    </DayPickerContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link DayPickerContextValue}.\n *\n * Use the DayPicker context to access to the props passed to DayPicker inside\n * internal or custom components.\n */\nexport function useDayPicker(): DayPickerContextValue {\n  const context = useContext(DayPickerContext);\n  if (!context) {\n    throw new Error(`useDayPicker must be used within a DayPickerProvider.`);\n  }\n  return context;\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link CaptionLabel} component. */\nexport interface CaptionLabelProps {\n  /** The ID for the heading element. Must be the same as the labelled-by in Table. */\n  id?: string;\n  /** The month where the caption is displayed. */\n  displayMonth: Date;\n  /** The index of the month where the caption is displayed. Older custom components may miss this prop. */\n  displayIndex?: number | undefined;\n}\n\n/** Render the caption for the displayed month. This component is used when `captionLayout=\"buttons\"`. */\nexport function CaptionLabel(props: CaptionLabelProps): JSX.Element {\n  const {\n    locale,\n    classNames,\n    styles,\n    formatters: { formatCaption }\n  } = useDayPicker();\n  return (\n    <div\n      className={classNames.caption_label}\n      style={styles.caption_label}\n      aria-live=\"polite\"\n      role=\"presentation\"\n      id={props.id}\n    >\n      {formatCaption(props.displayMonth, { locale })}\n    </div>\n  );\n}\n", "import { StyledComponent } from 'types/Styles';\n\n/**\n * Render the icon in the styled drop-down.\n */\nexport function IconDropdown(props: StyledComponent): JSX.Element {\n  return (\n    <svg\n      width=\"8px\"\n      height=\"8px\"\n      viewBox=\"0 0 120 120\"\n      data-testid=\"iconDropdown\"\n      {...props}\n    >\n      <path\n        d=\"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.0739418023,59.4824074 -0.0739418023,52.5175926 4.22182541,48.2218254 Z\"\n        fill=\"currentColor\"\n        fillRule=\"nonzero\"\n      ></path>\n    </svg>\n  );\n}\n", "import {\n  ChangeEventHandler,\n  CSSProperties,\n  ReactNode,\n  SelectHTMLAttributes\n} from 'react';\n\nimport { IconDropdown } from 'components/IconDropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link Dropdown} component. */\nexport interface DropdownProps {\n  /** The name attribute of the element. */\n  name?: string;\n  /** The caption displayed to replace the hidden select. */\n  caption?: ReactNode;\n  children?: SelectHTMLAttributes<HTMLSelectElement>['children'];\n  className?: string;\n  ['aria-label']?: string;\n  style?: CSSProperties;\n  /** The selected value. */\n  value?: string | number;\n  onChange?: ChangeEventHandler<HTMLSelectElement>;\n}\n\n/**\n * Render a styled select component – displaying a caption and a custom\n * drop-down icon.\n */\nexport function Dropdown(props: DropdownProps): JSX.Element {\n  const { onChange, value, children, caption, className, style } = props;\n  const dayPicker = useDayPicker();\n\n  const IconDropdownComponent =\n    dayPicker.components?.IconDropdown ?? IconDropdown;\n  return (\n    <div className={className} style={style}>\n      <span className={dayPicker.classNames.vhidden}>\n        {props['aria-label']}\n      </span>\n      <select\n        name={props.name}\n        aria-label={props['aria-label']}\n        className={dayPicker.classNames.dropdown}\n        style={dayPicker.styles.dropdown}\n        value={value}\n        onChange={onChange}\n      >\n        {children}\n      </select>\n      <div\n        className={dayPicker.classNames.caption_label}\n        style={dayPicker.styles.caption_label}\n        aria-hidden=\"true\"\n      >\n        {caption}\n        {\n          <IconDropdownComponent\n            className={dayPicker.classNames.dropdown_icon}\n            style={dayPicker.styles.dropdown_icon}\n          />\n        }\n      </div>\n    </div>\n  );\n}\n", "import { ChangeEventHandler } from 'react';\n\nimport { isSameYear, setMonth, startOfMonth } from 'date-fns';\n\nimport { Dropdown } from 'components/Dropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { MonthChangeEventHandler } from 'types/EventHandlers';\n\n/** The props for the {@link MonthsDropdown} component. */\nexport interface MonthsDropdownProps {\n  /** The month where the dropdown is displayed. */\n  displayMonth: Date;\n  onChange: MonthChangeEventHandler;\n}\n\n/** Render the dropdown to navigate between months. */\nexport function MonthsDropdown(props: MonthsDropdownProps): JSX.Element {\n  const {\n    fromDate,\n    toDate,\n    styles,\n    locale,\n    formatters: { formatMonthCaption },\n    classNames,\n    components,\n    labels: { labelMonthDropdown }\n  } = useDayPicker();\n\n  // Dropdown should appear only when both from/toDate is set\n  if (!fromDate) return <></>;\n  if (!toDate) return <></>;\n\n  const dropdownMonths: Date[] = [];\n\n  if (isSameYear(fromDate, toDate)) {\n    // only display the months included in the range\n    const date = startOfMonth(fromDate);\n    for (let month = fromDate.getMonth(); month <= toDate.getMonth(); month++) {\n      dropdownMonths.push(setMonth(date, month));\n    }\n  } else {\n    // display all the 12 months\n    const date = startOfMonth(new Date()); // Any date should be OK, as we just need the year\n    for (let month = 0; month <= 11; month++) {\n      dropdownMonths.push(setMonth(date, month));\n    }\n  }\n\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (e) => {\n    const selectedMonth = Number(e.target.value);\n    const newMonth = setMonth(startOfMonth(props.displayMonth), selectedMonth);\n    props.onChange(newMonth);\n  };\n\n  const DropdownComponent = components?.Dropdown ?? Dropdown;\n\n  return (\n    <DropdownComponent\n      name=\"months\"\n      aria-label={labelMonthDropdown()}\n      className={classNames.dropdown_month}\n      style={styles.dropdown_month}\n      onChange={handleChange}\n      value={props.displayMonth.getMonth()}\n      caption={formatMonthCaption(props.displayMonth, { locale })}\n    >\n      {dropdownMonths.map((m) => (\n        <option key={m.getMonth()} value={m.getMonth()}>\n          {formatMonthCaption(m, { locale })}\n        </option>\n      ))}\n    </DropdownComponent>\n  );\n}\n", "import { ChangeEventHandler } from 'react';\n\nimport { setYear, startOfMonth, startOfYear } from 'date-fns';\n\nimport { Dropdown } from 'components/Dropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { MonthChangeEventHandler } from 'types/EventHandlers';\n\n/**\n * The props for the {@link YearsDropdown} component.\n */\nexport interface YearsDropdownProps {\n  /** The month where the drop-down is displayed. */\n  displayMonth: Date;\n  /** Callback to handle the `change` event. */\n  onChange: MonthChangeEventHandler;\n}\n\n/**\n * Render a dropdown to change the year. Take in account the `nav.fromDate` and\n * `toDate` from context.\n */\nexport function YearsDropdown(props: YearsDropdownProps): JSX.Element {\n  const { displayMonth } = props;\n  const {\n    fromDate,\n    toDate,\n    locale,\n    styles,\n    classNames,\n    components,\n    formatters: { formatYearCaption },\n    labels: { labelYearDropdown }\n  } = useDayPicker();\n\n  const years: Date[] = [];\n\n  // Dropdown should appear only when both from/toDate is set\n  if (!fromDate) return <></>;\n  if (!toDate) return <></>;\n\n  const fromYear = fromDate.getFullYear();\n  const toYear = toDate.getFullYear();\n  for (let year = fromYear; year <= toYear; year++) {\n    years.push(setYear(startOfYear(new Date()), year));\n  }\n\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (e) => {\n    const newMonth = setYear(\n      startOfMonth(displayMonth),\n      Number(e.target.value)\n    );\n    props.onChange(newMonth);\n  };\n\n  const DropdownComponent = components?.Dropdown ?? Dropdown;\n\n  return (\n    <DropdownComponent\n      name=\"years\"\n      aria-label={labelYearDropdown()}\n      className={classNames.dropdown_year}\n      style={styles.dropdown_year}\n      onChange={handleChange}\n      value={displayMonth.getFullYear()}\n      caption={formatYearCaption(displayMonth, { locale })}\n    >\n      {years.map((year) => (\n        <option key={year.getFullYear()} value={year.getFullYear()}>\n          {formatYearCaption(year, { locale })}\n        </option>\n      ))}\n    </DropdownComponent>\n  );\n}\n", "import { Dispatch, SetStateAction, useState } from 'react';\n\nexport type DispatchStateAction<T> = Dispatch<SetStateAction<T>>;\n\n/**\n * Helper hook for using controlled/uncontrolled values from a component props.\n *\n * When the value is not controlled, pass `undefined` as `controlledValue` and\n * use the returned setter to update it.\n *\n * When the value is controlled, pass the controlled value as second\n * argument, which will be always returned as `value`.\n */\nexport function useControlledValue<T>(\n  defaultValue: T,\n  controlledValue: T | undefined\n): [T, DispatchStateAction<T>] {\n  const [uncontrolledValue, setValue] = useState(defaultValue);\n\n  const value =\n    controlledValue === undefined ? uncontrolledValue : controlledValue;\n\n  return [value, setValue] as [T, DispatchStateAction<T>];\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\n/** Return the initial month according to the given options. */\nexport function getInitialMonth(context: Partial<DayPickerContextValue>): Date {\n  const { month, defaultMonth, today } = context;\n  let initialMonth = month || defaultMonth || today || new Date();\n\n  const { toDate, fromDate, numberOfMonths = 1 } = context;\n\n  // Fix the initialMonth if is after the to-date\n  if (toDate && differenceInCalendarMonths(toDate, initialMonth) < 0) {\n    const offset = -1 * (numberOfMonths - 1);\n    initialMonth = addMonths(toDate, offset);\n  }\n  // Fix the initialMonth if is before the from-date\n  if (fromDate && differenceInCalendarMonths(initialMonth, fromDate) < 0) {\n    initialMonth = fromDate;\n  }\n  return startOfMonth(initialMonth);\n}\n", "import { startOfMonth } from 'date-fns';\n\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useControlledValue } from 'hooks/useControlledValue';\n\nimport { getInitialMonth } from './utils/getInitialMonth';\n\nexport type NavigationState = [\n  /** The month DayPicker is navigating at */\n  month: Date,\n  /** Go to the specified month. */\n  goToMonth: (month: Date) => void\n];\n\n/** Controls the navigation state. */\nexport function useNavigationState(): NavigationState {\n  const context = useDayPicker();\n  const initialMonth = getInitialMonth(context);\n  const [month, setMonth] = useControlledValue(initialMonth, context.month);\n\n  const goToMonth = (date: Date) => {\n    if (context.disableNavigation) return;\n    const month = startOfMonth(date);\n    setMonth(month);\n    context.onMonthChange?.(month);\n  };\n\n  return [month, goToMonth];\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\n/**\n * Return the months to display in the component according to the number of\n * months and the from/to date.\n */\nexport function getDisplayMonths(\n  month: Date,\n  {\n    reverseMonths,\n    numberOfMonths\n  }: {\n    reverseMonths?: boolean;\n    numberOfMonths: number;\n  }\n): Date[] {\n  const start = startOfMonth(month);\n  const end = startOfMonth(addMonths(start, numberOfMonths));\n  const monthsDiff = differenceInCalendarMonths(end, start);\n  let months = [];\n\n  for (let i = 0; i < monthsDiff; i++) {\n    const nextMonth = addMonths(start, i);\n    months.push(nextMonth);\n  }\n\n  if (reverseMonths) months = months.reverse();\n  return months;\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\n/**\n * Returns the next month the user can navigate to according to the given\n * options.\n *\n * Please note that the next month is not always the next calendar month:\n *\n * - if after the `toDate` range, is undefined;\n * - if the navigation is paged, is the number of months displayed ahead.\n *\n */\nexport function getNextMonth(\n  startingMonth: Date,\n  options: {\n    numberOfMonths?: number;\n    fromDate?: Date;\n    toDate?: Date;\n    pagedNavigation?: boolean;\n    today?: Date;\n    disableNavigation?: boolean;\n  }\n): Date | undefined {\n  if (options.disableNavigation) {\n    return undefined;\n  }\n  const { toDate, pagedNavigation, numberOfMonths = 1 } = options;\n  const offset = pagedNavigation ? numberOfMonths : 1;\n  const month = startOfMonth(startingMonth);\n\n  if (!toDate) {\n    return addMonths(month, offset);\n  }\n\n  const monthsDiff = differenceInCalendarMonths(toDate, startingMonth);\n\n  if (monthsDiff < numberOfMonths) {\n    return undefined;\n  }\n\n  // Jump forward as the number of months when paged navigation\n  return addMonths(month, offset);\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\n/**\n * Returns the next previous the user can navigate to, according to the given\n * options.\n *\n * Please note that the previous month is not always the previous calendar\n * month:\n *\n * - if before the `fromDate` date, is `undefined`;\n * - if the navigation is paged, is the number of months displayed before.\n *\n */\nexport function getPreviousMonth(\n  startingMonth: Date,\n  options: {\n    numberOfMonths?: number;\n    fromDate?: Date;\n    toDate?: Date;\n    pagedNavigation?: boolean;\n    today?: Date;\n    disableNavigation?: boolean;\n  }\n): Date | undefined {\n  if (options.disableNavigation) {\n    return undefined;\n  }\n  const { fromDate, pagedNavigation, numberOfMonths = 1 } = options;\n  const offset = pagedNavigation ? numberOfMonths : 1;\n  const month = startOfMonth(startingMonth);\n  if (!fromDate) {\n    return addMonths(month, -offset);\n  }\n  const monthsDiff = differenceInCalendarMonths(month, fromDate);\n\n  if (monthsDiff <= 0) {\n    return undefined;\n  }\n\n  // Jump back as the number of months when paged navigation\n  return addMonths(month, -offset);\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { addMonths, isBefore, isSameMonth } from 'date-fns';\n\nimport { useDayPicker } from '../DayPicker';\nimport { useNavigationState } from './useNavigationState';\nimport { getDisplayMonths } from './utils/getDisplayMonths';\nimport { getNextMonth } from './utils/getNextMonth';\nimport { getPreviousMonth } from './utils/getPreviousMonth';\n\n/** Represents the value of the {@link NavigationContext}. */\nexport interface NavigationContextValue {\n  /** The month to display in the calendar. When `numberOfMonths` is greater than one, is the first of the displayed months. */\n  currentMonth: Date;\n  /** The months rendered by DayPicker. DayPicker can render multiple months via `numberOfMonths`. */\n  displayMonths: Date[];\n  /** Navigate to the specified month. */\n  goToMonth: (month: Date) => void;\n  /** Navigate to the specified date. */\n  goToDate: (date: Date, refDate?: Date) => void;\n  /** The next month to display. */\n  nextMonth?: Date;\n  /** The previous month to display. */\n  previousMonth?: Date;\n  /** Whether the given day is included in the displayed months. */\n  isDateDisplayed: (day: Date) => boolean;\n}\n\n/**\n * The Navigation context shares details and methods to navigate the months in DayPicker.\n * Access this context from the {@link useNavigation} hook.\n */\nexport const NavigationContext = createContext<\n  NavigationContextValue | undefined\n>(undefined);\n\n/** Provides the values for the {@link NavigationContext}. */\nexport function NavigationProvider(props: {\n  children?: ReactNode;\n}): JSX.Element {\n  const dayPicker = useDayPicker();\n  const [currentMonth, goToMonth] = useNavigationState();\n\n  const displayMonths = getDisplayMonths(currentMonth, dayPicker);\n  const nextMonth = getNextMonth(currentMonth, dayPicker);\n  const previousMonth = getPreviousMonth(currentMonth, dayPicker);\n\n  const isDateDisplayed = (date: Date) => {\n    return displayMonths.some((displayMonth) =>\n      isSameMonth(date, displayMonth)\n    );\n  };\n\n  const goToDate = (date: Date, refDate?: Date) => {\n    if (isDateDisplayed(date)) {\n      return;\n    }\n\n    if (refDate && isBefore(date, refDate)) {\n      goToMonth(addMonths(date, 1 + dayPicker.numberOfMonths * -1));\n    } else {\n      goToMonth(date);\n    }\n  };\n\n  const value: NavigationContextValue = {\n    currentMonth,\n    displayMonths,\n    goToMonth,\n    goToDate,\n    previousMonth,\n    nextMonth,\n    isDateDisplayed\n  };\n\n  return (\n    <NavigationContext.Provider value={value}>\n      {props.children}\n    </NavigationContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link NavigationContextValue}. Use this hook to navigate\n * between months or years in DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useNavigation(): NavigationContextValue {\n  const context = useContext(NavigationContext);\n  if (!context) {\n    throw new Error('useNavigation must be used within a NavigationProvider');\n  }\n  return context;\n}\n", "import { addMonths } from 'date-fns';\n\nimport { CaptionProps } from 'components/Caption/Caption';\nimport { CaptionLabel } from 'components/CaptionLabel';\nimport { MonthsDropdown } from 'components/MonthsDropdown';\nimport { YearsDropdown } from 'components/YearsDropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useNavigation } from 'contexts/Navigation';\nimport { MonthChangeEventHandler } from 'types/EventHandlers';\n\n/**\n * Render a caption with the dropdowns to navigate between months and years.\n */\nexport function CaptionDropdowns(props: CaptionProps): JSX.Element {\n  const { classNames, styles, components } = useDayPicker();\n  const { goToMonth } = useNavigation();\n\n  const handleMonthChange: MonthChangeEventHandler = (newMonth) => {\n    goToMonth(\n      addMonths(newMonth, props.displayIndex ? -props.displayIndex : 0)\n    );\n  };\n  const CaptionLabelComponent = components?.CaptionLabel ?? CaptionLabel;\n  const captionLabel = (\n    <CaptionLabelComponent id={props.id} displayMonth={props.displayMonth} />\n  );\n  return (\n    <div\n      className={classNames.caption_dropdowns}\n      style={styles.caption_dropdowns}\n    >\n      {/* Caption label is visually hidden but for a11y. */}\n      <div className={classNames.vhidden}>{captionLabel}</div>\n      <MonthsDropdown\n        onChange={handleMonthChange}\n        displayMonth={props.displayMonth}\n      />\n      <YearsDropdown\n        onChange={handleMonthChange}\n        displayMonth={props.displayMonth}\n      />\n    </div>\n  );\n}\n", "import { StyledComponent } from 'types/Styles';\n\n/**\n * Ren<PERSON> the \"previous month\" button in the navigation.\n */\nexport function IconLeft(props: StyledComponent): JSX.Element {\n  return (\n    <svg width=\"16px\" height=\"16px\" viewBox=\"0 0 120 120\" {...props}>\n      <path\n        d=\"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z\"\n        fill=\"currentColor\"\n        fillRule=\"nonzero\"\n      ></path>\n    </svg>\n  );\n}\n", "import { StyledComponent } from 'types/Styles';\n\n/**\n * Ren<PERSON> the \"next month\" button in the navigation.\n */\nexport function IconRight(props: StyledComponent): JSX.Element {\n  return (\n    <svg width=\"16px\" height=\"16px\" viewBox=\"0 0 120 120\" {...props}>\n      <path\n        d=\"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z\"\n        fill=\"currentColor\"\n      ></path>\n    </svg>\n  );\n}\n", "import { forwardRef } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link Button} component. */\nexport type ButtonProps = JSX.IntrinsicElements['button'];\n\n/** Render a button HTML element applying the reset class name. */\nexport const Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  (props, ref) => {\n    const { classNames, styles } = useDayPicker();\n\n    const classNamesArr = [classNames.button_reset, classNames.button];\n    if (props.className) {\n      classNamesArr.push(props.className);\n    }\n    const className = classNamesArr.join(' ');\n\n    const style = { ...styles.button_reset, ...styles.button };\n    if (props.style) {\n      Object.assign(style, props.style);\n    }\n\n    return (\n      <button\n        {...props}\n        ref={ref}\n        type=\"button\"\n        className={className}\n        style={style}\n      />\n    );\n  }\n);\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';\n\nimport { IconLeft } from 'components/IconLeft';\nimport { IconRight } from 'components/IconRight';\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { Button } from '../Button';\n\n/** The props for the {@link Navigation} component. */\nexport interface NavigationProps {\n  /** The month where the caption is displayed. */\n  displayMonth: Date;\n  /** The previous month. */\n  previousMonth?: Date;\n  /** The next month. */\n  nextMonth?: Date;\n  /** Hide the previous button. */\n  hidePrevious: boolean;\n  /** Hide the next button. */\n  hideNext: boolean;\n  /** Event handler when the next button is clicked. */\n  onNextClick: MouseEventHandler<HTMLButtonElement>;\n  /** Event handler when the previous button is clicked. */\n  onPreviousClick: MouseEventHandler<HTMLButtonElement>;\n}\n\n/** A component rendering the navigation buttons or the drop-downs. */\nexport function Navigation(props: NavigationProps): JSX.Element {\n  const {\n    dir,\n    locale,\n    classNames,\n    styles,\n    labels: { labelPrevious, labelNext },\n    components\n  } = useDayPicker();\n\n  if (!props.nextMonth && !props.previousMonth) {\n    return <></>;\n  }\n\n  const previousLabel = labelPrevious(props.previousMonth, { locale });\n  const previousClassName = [\n    classNames.nav_button,\n    classNames.nav_button_previous\n  ].join(' ');\n\n  const nextLabel = labelNext(props.nextMonth, { locale });\n  const nextClassName = [\n    classNames.nav_button,\n    classNames.nav_button_next\n  ].join(' ');\n\n  const IconRightComponent = components?.IconRight ?? IconRight;\n  const IconLeftComponent = components?.IconLeft ?? IconLeft;\n  return (\n    <div className={classNames.nav} style={styles.nav}>\n      {!props.hidePrevious && (\n        <Button\n          name=\"previous-month\"\n          aria-label={previousLabel}\n          className={previousClassName}\n          style={styles.nav_button_previous}\n          disabled={!props.previousMonth}\n          onClick={props.onPreviousClick}\n        >\n          {dir === 'rtl' ? (\n            <IconRightComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          ) : (\n            <IconLeftComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          )}\n        </Button>\n      )}\n      {!props.hideNext && (\n        <Button\n          name=\"next-month\"\n          aria-label={nextLabel}\n          className={nextClassName}\n          style={styles.nav_button_next}\n          disabled={!props.nextMonth}\n          onClick={props.onNextClick}\n        >\n          {dir === 'rtl' ? (\n            <IconLeftComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          ) : (\n            <IconRightComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          )}\n        </Button>\n      )}\n    </div>\n  );\n}\n", "import { MouseEventHandler } from 'react';\n\nimport { isSameMonth } from 'date-fns';\n\nimport { CaptionProps } from 'components/Caption/Caption';\nimport { Navigation } from 'components/Navigation';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useNavigation } from 'contexts/Navigation';\n\n/**\n * Render a caption with a button-based navigation.\n */\nexport function CaptionNavigation(props: CaptionProps): JSX.Element {\n  const { numberOfMonths } = useDayPicker();\n  const { previousMonth, nextMonth, goToMonth, displayMonths } =\n    useNavigation();\n\n  const displayIndex = displayMonths.findIndex((month) =>\n    isSameMonth(props.displayMonth, month)\n  );\n\n  const isFirst = displayIndex === 0;\n  const isLast = displayIndex === displayMonths.length - 1;\n\n  const hideNext = numberOfMonths > 1 && (isFirst || !isLast);\n  const hidePrevious = numberOfMonths > 1 && (isLast || !isFirst);\n\n  const handlePreviousClick: MouseEventHandler = () => {\n    if (!previousMonth) return;\n    goToMonth(previousMonth);\n  };\n\n  const handleNextClick: MouseEventHandler = () => {\n    if (!nextMonth) return;\n    goToMonth(nextMonth);\n  };\n\n  return (\n    <Navigation\n      displayMonth={props.displayMonth}\n      hideNext={hideNext}\n      hidePrevious={hidePrevious}\n      nextMonth={nextMonth}\n      previousMonth={previousMonth}\n      onPreviousClick={handlePreviousClick}\n      onNextClick={handleNextClick}\n    />\n  );\n}\n", "import { CaptionDropdowns } from 'components/CaptionDropdowns';\nimport { CaptionLabel } from 'components/CaptionLabel';\nimport { CaptionNavigation } from 'components/CaptionNavigation';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** Represent the props of the {@link Caption} component. */\nexport interface CaptionProps {\n  /** The ID for the heading element. Must be the same as the labelled-by in Table. */\n  id?: string;\n  /** The month where the caption is displayed. */\n  displayMonth: Date;\n  /** The index of the month where the caption is displayed. Older custom components may miss this prop. */\n  displayIndex?: number | undefined;\n}\n\n/**\n * The layout of the caption:\n *\n * - `dropdown`: display dropdowns for choosing the month and the year.\n * - `buttons`: display previous month / next month buttons.\n * - `dropdown-buttons`: display both month / year dropdowns and previous month / next month buttons.\n */\nexport type CaptionLayout = 'dropdown' | 'buttons' | 'dropdown-buttons';\n\n/**\n * Render the caption of a month. The caption has a different layout when\n * setting the {@link DayPickerBase.captionLayout} prop.\n */\nexport function Caption(props: CaptionProps): JSX.Element {\n  const { classNames, disableNavigation, styles, captionLayout, components } =\n    useDayPicker();\n\n  const CaptionLabelComponent = components?.CaptionLabel ?? CaptionLabel;\n\n  let caption: JSX.Element;\n  if (disableNavigation) {\n    caption = (\n      <CaptionLabelComponent id={props.id} displayMonth={props.displayMonth} />\n    );\n  } else if (captionLayout === 'dropdown') {\n    caption = (\n      <CaptionDropdowns displayMonth={props.displayMonth} id={props.id} />\n    );\n  } else if (captionLayout === 'dropdown-buttons') {\n    caption = (\n      <>\n        <CaptionDropdowns\n          displayMonth={props.displayMonth}\n          displayIndex={props.displayIndex}\n          id={props.id}\n        />\n        <CaptionNavigation\n          displayMonth={props.displayMonth}\n          displayIndex={props.displayIndex}\n          id={props.id}\n        />\n      </>\n    );\n  } else {\n    caption = (\n      <>\n        <CaptionLabelComponent\n          id={props.id}\n          displayMonth={props.displayMonth}\n          displayIndex={props.displayIndex}\n        />\n        <CaptionNavigation displayMonth={props.displayMonth} id={props.id} />\n      </>\n    );\n  }\n\n  return (\n    <div className={classNames.caption} style={styles.caption}>\n      {caption}\n    </div>\n  );\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\n\nexport interface FooterProps {\n  /** The month where the footer is displayed. */\n  displayMonth?: Date;\n}\n/** Render the Footer component (empty as default).*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function Footer(props: FooterProps): JSX.Element {\n  const {\n    footer,\n    styles,\n    classNames: { tfoot }\n  } = useDayPicker();\n  if (!footer) return <></>;\n  return (\n    <tfoot className={tfoot} style={styles.tfoot}>\n      <tr>\n        <td colSpan={8}>{footer}</td>\n      </tr>\n    </tfoot>\n  );\n}\n", "import { addDays, Locale, startOfISOWeek, startOfWeek } from 'date-fns';\n\n/**\n * Generate a series of 7 days, starting from the week, to use for formatting\n * the weekday names (Monday, Tuesday, etc.).\n */\nexport function getWeekdays(\n  locale?: Locale,\n  /** The index of the first day of the week (0 - Sunday). */\n  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,\n  /** Use ISOWeek instead of locale/ */\n  ISOWeek?: boolean\n): Date[] {\n  const start = ISOWeek\n    ? startOfISOWeek(new Date())\n    : startOfWeek(new Date(), { locale, weekStartsOn });\n\n  const days = [];\n  for (let i = 0; i < 7; i++) {\n    const day = addDays(start, i);\n    days.push(day);\n  }\n  return days;\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\n\nimport { getWeekdays } from './utils';\n\n/**\n * Render the HeadRow component - i.e. the table head row with the weekday names.\n */\nexport function HeadRow(): JSX.Element {\n  const {\n    classNames,\n    styles,\n    showWeekNumber,\n    locale,\n    weekStartsOn,\n    ISOWeek,\n    formatters: { formatWeekdayName },\n    labels: { labelWeekday }\n  } = useDayPicker();\n\n  const weekdays = getWeekdays(locale, weekStartsOn, ISOWeek);\n\n  return (\n    <tr style={styles.head_row} className={classNames.head_row}>\n      {showWeekNumber && (\n        <td style={styles.head_cell} className={classNames.head_cell}></td>\n      )}\n      {weekdays.map((weekday, i) => (\n        <th\n          key={i}\n          scope=\"col\"\n          className={classNames.head_cell}\n          style={styles.head_cell}\n          aria-label={labelWeekday(weekday, { locale })}\n        >\n          {formatWeekdayName(weekday, { locale })}\n        </th>\n      ))}\n    </tr>\n  );\n}\n", "import { HeadRow } from 'components/HeadRow';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** Render the table head. */\nexport function Head(): JSX.Element {\n  const { classNames, styles, components } = useDayPicker();\n  const HeadRowComponent = components?.HeadRow ?? HeadRow;\n  return (\n    <thead style={styles.head} className={classNames.head}>\n      <HeadRowComponent />\n    </thead>\n  );\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\nimport { ActiveModifiers } from 'types/Modifiers';\n\n/** Represent the props for the {@link DayContent} component. */\nexport interface DayContentProps {\n  /** The date representing the day. */\n  date: Date;\n  /** The month where the day is displayed. */\n  displayMonth: Date;\n  /** The active modifiers for the given date. */\n  activeModifiers: ActiveModifiers;\n}\n\n/** Render the content of the day cell. */\nexport function DayContent(props: DayContentProps): JSX.Element {\n  const {\n    locale,\n    formatters: { formatDay }\n  } = useDayPicker();\n\n  return <>{formatDay(props.date, { locale })}</>;\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { isSameDay } from 'date-fns';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport {\n  DayPickerMultipleProps,\n  isDayPickerMultiple\n} from 'types/DayPickerMultiple';\nimport { DayClickEventHandler } from 'types/EventHandlers';\nimport { InternalModifier, Modifiers } from 'types/Modifiers';\n\n/** Represent the modifiers that are changed by the multiple selection. */\nexport type SelectMultipleModifiers = Pick<\n  Modifiers,\n  InternalModifier.Disabled\n>;\n\n/** Represents the value of a {@link SelectMultipleContext}. */\nexport interface SelectMultipleContextValue {\n  /** The days that have been selected. */\n  selected: Date[] | undefined;\n  /** The modifiers for the corresponding selection. */\n  modifiers: SelectMultipleModifiers;\n  /** Event handler to attach to the day button to enable the multiple select. */\n  onDayClick?: DayClickEventHandler;\n}\n\n/**\n * The SelectMultiple context shares details about the selected days when in\n * multiple selection mode.\n *\n * Access this context from the {@link useSelectMultiple} hook.\n */\nexport const SelectMultipleContext = createContext<\n  SelectMultipleContextValue | undefined\n>(undefined);\n\nexport type SelectMultipleProviderProps = {\n  initialProps: DayPickerBase;\n  children?: ReactNode;\n};\n\n/** Provides the values for the {@link SelectMultipleContext}. */\nexport function SelectMultipleProvider(\n  props: SelectMultipleProviderProps\n): JSX.Element {\n  if (!isDayPickerMultiple(props.initialProps)) {\n    const emptyContextValue: SelectMultipleContextValue = {\n      selected: undefined,\n      modifiers: {\n        disabled: []\n      }\n    };\n    return (\n      <SelectMultipleContext.Provider value={emptyContextValue}>\n        {props.children}\n      </SelectMultipleContext.Provider>\n    );\n  }\n  return (\n    <SelectMultipleProviderInternal\n      initialProps={props.initialProps}\n      children={props.children}\n    />\n  );\n}\n\n/** @private */\nexport interface SelectMultipleProviderInternalProps {\n  initialProps: DayPickerMultipleProps;\n  children?: ReactNode;\n}\n\nexport function SelectMultipleProviderInternal({\n  initialProps,\n  children\n}: SelectMultipleProviderInternalProps): JSX.Element {\n  const { selected, min, max } = initialProps;\n\n  const onDayClick: DayClickEventHandler = (day, activeModifiers, e) => {\n    initialProps.onDayClick?.(day, activeModifiers, e);\n\n    const isMinSelected = Boolean(\n      activeModifiers.selected && min && selected?.length === min\n    );\n    if (isMinSelected) {\n      return;\n    }\n\n    const isMaxSelected = Boolean(\n      !activeModifiers.selected && max && selected?.length === max\n    );\n    if (isMaxSelected) {\n      return;\n    }\n\n    const selectedDays = selected ? [...selected] : [];\n\n    if (activeModifiers.selected) {\n      const index = selectedDays.findIndex((selectedDay) =>\n        isSameDay(day, selectedDay)\n      );\n      selectedDays.splice(index, 1);\n    } else {\n      selectedDays.push(day);\n    }\n    initialProps.onSelect?.(selectedDays, day, activeModifiers, e);\n  };\n\n  const modifiers: SelectMultipleModifiers = {\n    disabled: []\n  };\n\n  if (selected) {\n    modifiers.disabled.push((day: Date) => {\n      const isMaxSelected = max && selected.length > max - 1;\n      const isSelected = selected.some((selectedDay) =>\n        isSameDay(selectedDay, day)\n      );\n      return Boolean(isMaxSelected && !isSelected);\n    });\n  }\n\n  const contextValue = {\n    selected,\n    onDayClick,\n    modifiers\n  };\n\n  return (\n    <SelectMultipleContext.Provider value={contextValue}>\n      {children}\n    </SelectMultipleContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link SelectMultipleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useSelectMultiple(): SelectMultipleContextValue {\n  const context = useContext(SelectMultipleContext);\n  if (!context) {\n    throw new Error(\n      'useSelectMultiple must be used within a SelectMultipleProvider'\n    );\n  }\n  return context;\n}\n", "import { isAfter, isBefore, isSameDay } from 'date-fns';\n\nimport { DateRange } from 'types/Matchers';\n\n/**\n * Add a day to an existing range.\n *\n * The returned range takes in account the `undefined` values and if the added\n * day is already present in the range.\n */\nexport function addToRange(\n  day: Date,\n  range?: DateRange\n): DateRange | undefined {\n  const { from, to } = range || {};\n  if (from && to) {\n    if (isSameDay(to, day) && isSameDay(from, day)) {\n      return undefined;\n    }\n    if (isSameDay(to, day)) {\n      return { from: to, to: undefined };\n    }\n    if (isSameDay(from, day)) {\n      return undefined;\n    }\n    if (isAfter(from, day)) {\n      return { from: day, to };\n    }\n    return { from, to: day };\n  }\n  if (to) {\n    if (isAfter(day, to)) {\n      return { from: to, to: day };\n    }\n    return { from: day, to };\n  }\n  if (from) {\n    if (isBefore(day, from)) {\n      return { from: day, to: from };\n    }\n    return { from, to: day };\n  }\n  return { from: day, to: undefined };\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport {\n  addDays,\n  differenceInCalendarDays,\n  isSameDay,\n  subDays\n} from 'date-fns';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport { DayPickerRangeProps, isDayPickerRange } from 'types/DayPickerRange';\nimport { DayClickEventHandler } from 'types/EventHandlers';\nimport { DateRange } from 'types/Matchers';\nimport { InternalModifier, Modifiers } from 'types/Modifiers';\n\nimport { addToRange } from './utils/addToRange';\n\n/** Represent the modifiers that are changed by the range selection. */\nexport type SelectRangeModifiers = Pick<\n  Modifiers,\n  | InternalModifier.Disabled\n  | InternalModifier.RangeEnd\n  | InternalModifier.RangeMiddle\n  | InternalModifier.RangeStart\n>;\n\n/** Represents the value of a {@link SelectRangeContext}. */\nexport interface SelectRangeContextValue {\n  /** The range of days that has been selected. */\n  selected: DateRange | undefined;\n  /** The modifiers for the corresponding selection. */\n  modifiers: SelectRangeModifiers;\n  /** Event handler to attach to the day button to enable the range select. */\n  onDayClick?: DayClickEventHandler;\n}\n\n/**\n * The SelectRange context shares details about the selected days when in\n * range selection mode.\n *\n * Access this context from the {@link useSelectRange} hook.\n */\nexport const SelectRangeContext = createContext<\n  SelectRangeContextValue | undefined\n>(undefined);\n\nexport interface SelectRangeProviderProps {\n  initialProps: DayPickerBase;\n  children?: ReactNode;\n}\n\n/** Provides the values for the {@link SelectRangeProvider}. */\nexport function SelectRangeProvider(\n  props: SelectRangeProviderProps\n): JSX.Element {\n  if (!isDayPickerRange(props.initialProps)) {\n    const emptyContextValue: SelectRangeContextValue = {\n      selected: undefined,\n      modifiers: {\n        range_start: [],\n        range_end: [],\n        range_middle: [],\n        disabled: []\n      }\n    };\n    return (\n      <SelectRangeContext.Provider value={emptyContextValue}>\n        {props.children}\n      </SelectRangeContext.Provider>\n    );\n  }\n  return (\n    <SelectRangeProviderInternal\n      initialProps={props.initialProps}\n      children={props.children}\n    />\n  );\n}\n\n/** @private */\nexport interface SelectRangeProviderInternalProps {\n  initialProps: DayPickerRangeProps;\n  children?: ReactNode;\n}\n\nexport function SelectRangeProviderInternal({\n  initialProps,\n  children\n}: SelectRangeProviderInternalProps): JSX.Element {\n  const { selected } = initialProps;\n  const { from: selectedFrom, to: selectedTo } = selected || {};\n  const min = initialProps.min;\n  const max = initialProps.max;\n\n  const onDayClick: DayClickEventHandler = (day, activeModifiers, e) => {\n    initialProps.onDayClick?.(day, activeModifiers, e);\n    const newRange = addToRange(day, selected);\n    initialProps.onSelect?.(newRange, day, activeModifiers, e);\n  };\n\n  const modifiers: SelectRangeModifiers = {\n    range_start: [],\n    range_end: [],\n    range_middle: [],\n    disabled: []\n  };\n\n  if (selectedFrom) {\n    modifiers.range_start = [selectedFrom];\n    if (!selectedTo) {\n      modifiers.range_end = [selectedFrom];\n    } else {\n      modifiers.range_end = [selectedTo];\n      if (!isSameDay(selectedFrom, selectedTo)) {\n        modifiers.range_middle = [\n          {\n            after: selectedFrom,\n            before: selectedTo\n          }\n        ];\n      }\n    }\n  } else if (selectedTo) {\n    modifiers.range_start = [selectedTo];\n    modifiers.range_end = [selectedTo];\n  }\n\n  if (min) {\n    if (selectedFrom && !selectedTo) {\n      modifiers.disabled.push({\n        after: subDays(selectedFrom, min - 1),\n        before: addDays(selectedFrom, min - 1)\n      });\n    }\n    if (selectedFrom && selectedTo) {\n      modifiers.disabled.push({\n        after: selectedFrom,\n        before: addDays(selectedFrom, min - 1)\n      });\n    }\n    if (!selectedFrom && selectedTo) {\n      modifiers.disabled.push({\n        after: subDays(selectedTo, min - 1),\n        before: addDays(selectedTo, min - 1)\n      });\n    }\n  }\n  if (max) {\n    if (selectedFrom && !selectedTo) {\n      modifiers.disabled.push({\n        before: addDays(selectedFrom, -max + 1)\n      });\n      modifiers.disabled.push({\n        after: addDays(selectedFrom, max - 1)\n      });\n    }\n    if (selectedFrom && selectedTo) {\n      const selectedCount =\n        differenceInCalendarDays(selectedTo, selectedFrom) + 1;\n      const offset = max - selectedCount;\n      modifiers.disabled.push({\n        before: subDays(selectedFrom, offset)\n      });\n      modifiers.disabled.push({\n        after: addDays(selectedTo, offset)\n      });\n    }\n    if (!selectedFrom && selectedTo) {\n      modifiers.disabled.push({\n        before: addDays(selectedTo, -max + 1)\n      });\n      modifiers.disabled.push({\n        after: addDays(selectedTo, max - 1)\n      });\n    }\n  }\n\n  return (\n    <SelectRangeContext.Provider value={{ selected, onDayClick, modifiers }}>\n      {children}\n    </SelectRangeContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link SelectRangeContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useSelectRange(): SelectRangeContextValue {\n  const context = useContext(SelectRangeContext);\n  if (!context) {\n    throw new Error('useSelectRange must be used within a SelectRangeProvider');\n  }\n  return context;\n}\n", "import { Matcher } from 'types/Matchers';\n\n/** Normalize to array a matcher input. */\nexport function matcherToArray(\n  matcher: Matcher | Matcher[] | undefined\n): Matcher[] {\n  if (Array.isArray(matcher)) {\n    return [...matcher];\n  } else if (matcher !== undefined) {\n    return [matcher];\n  } else {\n    return [];\n  }\n}\n", "import { CustomModifiers, DayModifiers } from 'types/Modifiers';\n\nimport { matcherToArray } from './matcherToArray';\n\n/** Create CustomModifiers from dayModifiers */\nexport function getCustomModifiers(\n  dayModifiers: DayModifiers\n): CustomModifiers {\n  const customModifiers: CustomModifiers = {};\n  Object.entries(dayModifiers).forEach(([modifier, matcher]) => {\n    customModifiers[modifier] = matcherToArray(matcher);\n  });\n  return customModifiers;\n}\n", "import { CSSProperties } from 'react';\n\nimport { Matcher } from './Matchers';\n\n/** A _modifier_ represents different styles or states of a day displayed in the calendar. */\nexport type Modifier = string;\n\n/** The modifiers used by DayPicker. */\nexport type Modifiers = CustomModifiers & InternalModifiers;\n\n/** The name of the modifiers that are used internally by DayPicker. */\nexport enum InternalModifier {\n  Outside = 'outside',\n  /** Name of the modifier applied to the disabled days, using the `disabled` prop. */\n  Disabled = 'disabled',\n  /** Name of the modifier applied to the selected days using the `selected` prop). */\n  Selected = 'selected',\n  /** Name of the modifier applied to the hidden days using the `hidden` prop). */\n  Hidden = 'hidden',\n  /** Name of the modifier applied to the day specified using the `today` prop). */\n  Today = 'today',\n  /** The modifier applied to the day starting a selected range, when in range selection mode.  */\n  RangeStart = 'range_start',\n  /** The modifier applied to the day ending a selected range, when in range selection mode.  */\n  RangeEnd = 'range_end',\n  /** The modifier applied to the days between the start and the end of a selected range, when in range selection mode.  */\n  RangeMiddle = 'range_middle'\n}\n\n/** Map of matchers used for the internal modifiers. */\nexport type InternalModifiers = Record<InternalModifier, Matcher[]>;\n\n/**\n * The modifiers that are matching a day in the calendar. Use the {@link useActiveModifiers} hook to get the modifiers for a day.\n *\n * ```\n * const activeModifiers: ActiveModifiers = {\n *  selected: true,\n *  customModifier: true\n * }\n * ```\n *\n * */\nexport type ActiveModifiers = Record<Modifier, true> &\n  Partial<Record<InternalModifier, true>>;\n\n/** The style to apply to each day element matching a modifier. */\nexport type ModifiersStyles = Record<Modifier, CSSProperties> &\n  Partial<Record<InternalModifier, CSSProperties>>;\n\n/** The classnames to assign to each day element matching a modifier. */\nexport type ModifiersClassNames = Record<Modifier, string> &\n  Partial<Record<InternalModifier, string>>;\n\n/** The custom modifiers passed to the {@link DayPickerBase.modifiers}. */\nexport type DayModifiers = Record<Modifier, Matcher | Matcher[]>;\n\n/**\n * A map of matchers used as custom modifiers by DayPicker component. This is\n * the same as {@link DayModifiers]], but it accepts only array of [[Matcher}s.\n */\nexport type CustomModifiers = Record<Modifier, Matcher[]>;\n", "import { DayPickerContextValue } from 'contexts/DayPicker';\nimport { SelectMultipleContextValue } from 'contexts/SelectMultiple';\nimport { SelectRangeContextValue } from 'contexts/SelectRange';\nimport { isDayPickerMultiple } from 'types/DayPickerMultiple';\nimport { isDayPickerRange } from 'types/DayPickerRange';\nimport { InternalModifier, InternalModifiers } from 'types/Modifiers';\n\nimport { matcherToArray } from './matcherToArray';\n\nconst {\n  Selected,\n  Disabled,\n  Hidden,\n  Today,\n  RangeEnd,\n  RangeMiddle,\n  RangeStart,\n  Outside\n} = InternalModifier;\n\n/** Return the {@link InternalModifiers} from the DayPicker and select contexts. */\nexport function getInternalModifiers(\n  dayPicker: DayPickerContextValue,\n  selectMultiple: SelectMultipleContextValue,\n  selectRange: SelectRangeContextValue\n) {\n  const internalModifiers: InternalModifiers = {\n    [Selected]: matcherToArray(dayPicker.selected),\n    [Disabled]: matcherToArray(dayPicker.disabled),\n    [Hidden]: matcherToArray(dayPicker.hidden),\n    [Today]: [dayPicker.today],\n    [RangeEnd]: [],\n    [RangeMiddle]: [],\n    [RangeStart]: [],\n    [Outside]: []\n  };\n\n  if (dayPicker.fromDate) {\n    internalModifiers[Disabled].push({ before: dayPicker.fromDate });\n  }\n  if (dayPicker.toDate) {\n    internalModifiers[Disabled].push({ after: dayPicker.toDate });\n  }\n\n  if (isDayPickerMultiple(dayPicker)) {\n    internalModifiers[Disabled] = internalModifiers[Disabled].concat(\n      selectMultiple.modifiers[Disabled]\n    );\n  } else if (isDayPickerRange(dayPicker)) {\n    internalModifiers[Disabled] = internalModifiers[Disabled].concat(\n      selectRange.modifiers[Disabled]\n    );\n    internalModifiers[RangeStart] = selectRange.modifiers[RangeStart];\n    internalModifiers[RangeMiddle] = selectRange.modifiers[RangeMiddle];\n    internalModifiers[RangeEnd] = selectRange.modifiers[RangeEnd];\n  }\n  return internalModifiers;\n}\n", "import { createContext, useContext, ReactNode } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useSelectMultiple } from 'contexts/SelectMultiple';\nimport { useSelectRange } from 'contexts/SelectRange';\nimport { CustomModifiers, InternalModifiers, Modifiers } from 'types/Modifiers';\n\nimport { getCustomModifiers } from './utils/getCustomModifiers';\nimport { getInternalModifiers } from './utils/getInternalModifiers';\n\n/** The Modifiers context store the modifiers used in DayPicker. To access the value of this context, use {@link useModifiers}. */\nexport const ModifiersContext = createContext<Modifiers | undefined>(undefined);\n\nexport type ModifiersProviderProps = { children: ReactNode };\n\n/** Provide the value for the {@link ModifiersContext}. */\nexport function ModifiersProvider(props: ModifiersProviderProps): JSX.Element {\n  const dayPicker = useDayPicker();\n  const selectMultiple = useSelectMultiple();\n  const selectRange = useSelectRange();\n\n  const internalModifiers: InternalModifiers = getInternalModifiers(\n    dayPicker,\n    selectMultiple,\n    selectRange\n  );\n\n  const customModifiers: CustomModifiers = getCustomModifiers(\n    dayPicker.modifiers\n  );\n\n  const modifiers: Modifiers = {\n    ...internalModifiers,\n    ...customModifiers\n  };\n\n  return (\n    <ModifiersContext.Provider value={modifiers}>\n      {props.children}\n    </ModifiersContext.Provider>\n  );\n}\n\n/**\n * Return the modifiers used by DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n * Requires to be wrapped into {@link ModifiersProvider}.\n *\n */\nexport function useModifiers(): Modifiers {\n  const context = useContext(ModifiersContext);\n  if (!context) {\n    throw new Error('useModifiers must be used within a ModifiersProvider');\n  }\n  return context;\n}\n", "/**\n * A value or a function that matches a specific day.\n *\n *\n * Matchers are passed to <PERSON>P<PERSON> via {@link DayPickerBase.disabled},\n * {@link DayPickerBase.hidden]] or [[DayPickerProps.selected} and are used to\n * determine if a day should get a {@link Modifier}.\n *\n * Matchers can be of different types:\n *\n * ```\n * // will always match the day\n * const booleanMatcher: Matcher = true;\n *\n *  // will match the today's date\n * const dateMatcher: Matcher = new Date();\n *\n * // will match the days in the array\n * const arrayMatcher: Matcher = [new Date(2019, 1, 2), new Date(2019, 1, 4)];\n *\n * // will match days after the 2nd of February 2019\n * const afterMatcher: DateAfter = { after: new Date(2019, 1, 2) };\n *  // will match days before the 2nd of February 2019 }\n * const beforeMatcher: DateBefore = { before: new Date(2019, 1, 2) };\n *\n * // will match Sundays\n * const dayOfWeekMatcher: DayOfWeek = {\n *  dayOfWeek: 0\n * };\n *\n * // will match the included days, except the two dates\n * const intervalMatcher: DateInterval = {\n *    after: new Date(2019, 1, 2),\n *    before: new Date(2019, 1, 5)\n * };\n *\n * // will match the included days, including the two dates\n * const rangeMatcher: DateRange = {\n *    from: new Date(2019, 1, 2),\n *    to: new Date(2019, 1, 5)\n * };\n *\n * // will match when the function return true\n * const functionMatcher: Matcher = (day: Date) => {\n *  return day.getMonth() === 2 // match when month is March\n * };\n * ```\n *\n * @see {@link isMatch}\n *\n * */\nexport type Matcher =\n  | boolean\n  | ((date: Date) => boolean)\n  | Date\n  | Date[]\n  | DateRange\n  | DateBefore\n  | DateAfter\n  | DateInterval\n  | DayOfWeek;\n\n/** A matcher to match a day falling after the specified date, with the date not included. */\nexport type DateAfter = { after: Date };\n\n/** A matcher to match a day falling before the specified date, with the date not included. */\nexport type DateBefore = { before: Date };\n\n/** A matcher to match a day falling before and/or after two dates, where the dates are not included. */\nexport type DateInterval = { before: Date; after: Date };\n\n/** A matcher to match a range of dates. The range can be open. Differently from {@link DateInterval}, the dates here are included. */\nexport type DateRange = { from: Date | undefined; to?: Date | undefined };\n\n/** A matcher to match a date being one of the specified days of the week (`0-6`, where `0` is Sunday). */\nexport type DayOfWeek = { dayOfWeek: number[] };\n\n/** Returns true if `matcher` is of type {@link DateInterval}. */\nexport function isDateInterval(matcher: unknown): matcher is DateInterval {\n  return Boolean(\n    matcher &&\n      typeof matcher === 'object' &&\n      'before' in matcher &&\n      'after' in matcher\n  );\n}\n\n/** Returns true if `value` is a {@link DateRange} type. */\nexport function isDateRange(value: unknown): value is DateRange {\n  return Boolean(value && typeof value === 'object' && 'from' in value);\n}\n\n/** Returns true if `value` is of type {@link DateAfter}. */\nexport function isDateAfterType(value: unknown): value is DateAfter {\n  return Boolean(value && typeof value === 'object' && 'after' in value);\n}\n\n/** Returns true if `value` is of type {@link DateBefore}. */\nexport function isDateBeforeType(value: unknown): value is DateBefore {\n  return Boolean(value && typeof value === 'object' && 'before' in value);\n}\n\n/** Returns true if `value` is a {@link DayOfWeek} type. */\nexport function isDayOfWeekType(value: unknown): value is DayOfWeek {\n  return Boolean(value && typeof value === 'object' && 'dayOfWeek' in value);\n}\n", "import { differenceInCalendarDays, isSameDay } from 'date-fns';\n\nimport { DateRange } from 'types/Matchers';\n\n/** Return `true` whether `date` is inside `range`. */\nexport function isDateInRange(date: Date, range: DateRange): boolean {\n  let { from, to } = range;\n  if (from && to) {\n    const isRangeInverted = differenceInCalendarDays(to, from) < 0;\n    if (isRangeInverted) {\n      [from, to] = [to, from];\n    }\n    const isInRange =\n      differenceInCalendarDays(date, from) >= 0 &&\n      differenceInCalendarDays(to, date) >= 0;\n    return isInRange;\n  }\n  if (to) {\n    return isSameDay(to, date);\n  }\n  if (from) {\n    return isSameDay(from, date);\n  }\n  return false;\n}\n", "import { differenceInCalendarDays, isAfter, isDate, isSameDay } from 'date-fns';\n\nimport {\n  isDateAfterType,\n  isDateBeforeType,\n  isDateInterval,\n  isDateRange,\n  isDayOfWeekType,\n  Matcher\n} from 'types/Matchers';\n\nimport { isDateInRange } from './isDateInRange';\n\n/** Returns true if `value` is a Date type. */\nfunction isDateType(value: unknown): value is Date {\n  return isDate(value);\n}\n\n/** Returns true if `value` is an array of valid dates. */\nfunction isArrayOfDates(value: unknown): value is Date[] {\n  return Array.isArray(value) && value.every(isDate);\n}\n\n/**\n * Returns whether a day matches against at least one of the given Matchers.\n *\n * ```\n * const day = new Date(2022, 5, 19);\n * const matcher1: DateRange = {\n *    from: new Date(2021, 12, 21),\n *    to: new Date(2021, 12, 30)\n * }\n * const matcher2: DateRange = {\n *    from: new Date(2022, 5, 1),\n *    to: new Date(2022, 5, 23)\n * }\n *\n * const isMatch(day, [matcher1, matcher2]); // true, since day is in the matcher1 range.\n * ```\n * */\nexport function isMatch(day: Date, matchers: Matcher[]): boolean {\n  return matchers.some((matcher: Matcher) => {\n    if (typeof matcher === 'boolean') {\n      return matcher;\n    }\n    if (isDateType(matcher)) {\n      return isSameDay(day, matcher);\n    }\n    if (isArrayOfDates(matcher)) {\n      return matcher.includes(day);\n    }\n    if (isDateRange(matcher)) {\n      return isDateInRange(day, matcher);\n    }\n    if (isDayOfWeekType(matcher)) {\n      return matcher.dayOfWeek.includes(day.getDay());\n    }\n    if (isDateInterval(matcher)) {\n      const diffBefore = differenceInCalendarDays(matcher.before, day);\n      const diffAfter = differenceInCalendarDays(matcher.after, day);\n      const isDayBefore = diffBefore > 0;\n      const isDayAfter = diffAfter < 0;\n      const isClosedInterval = isAfter(matcher.before, matcher.after);\n      if (isClosedInterval) {\n        return isDayAfter && isDayBefore;\n      } else {\n        return isDayBefore || isDayAfter;\n      }\n    }\n    if (isDateAfterType(matcher)) {\n      return differenceInCalendarDays(day, matcher.after) > 0;\n    }\n    if (isDateBeforeType(matcher)) {\n      return differenceInCalendarDays(matcher.before, day) > 0;\n    }\n    if (typeof matcher === 'function') {\n      return matcher(day);\n    }\n    return false;\n  });\n}\n", "import { isSameMonth } from 'date-fns';\n\nimport { ActiveModifiers, Modifiers } from 'types/Modifiers';\n\nimport { isMatch } from './isMatch';\n\n/** Return the active modifiers for the given day. */\nexport function getActiveModifiers(\n  day: Date,\n  /** The modifiers to match for the given date. */\n  modifiers: Modifiers,\n  /** The month where the day is displayed, to add the \"outside\" modifiers.  */\n  displayMonth?: Date\n): ActiveModifiers {\n  const matchedModifiers = Object.keys(modifiers).reduce(\n    (result: string[], key: string): string[] => {\n      const modifier = modifiers[key];\n      if (isMatch(day, modifier)) {\n        result.push(key);\n      }\n      return result;\n    },\n    []\n  );\n  const activeModifiers: ActiveModifiers = {};\n  matchedModifiers.forEach((modifier) => (activeModifiers[modifier] = true));\n\n  if (displayMonth && !isSameMonth(day, displayMonth)) {\n    activeModifiers.outside = true;\n  }\n\n  return activeModifiers;\n}\n", "import { addDays, endOfMonth, startOfMonth } from 'date-fns';\n\nimport { getActiveModifiers } from 'contexts/Modifiers';\nimport { Modifiers } from 'types/Modifiers';\n\n/**\n * Returns the day that should be the target of the focus when DayPicker is\n * rendered the first time.\n *\n * TODO: this function doesn't consider if the day is outside the month. We\n * implemented this check in `useDayRender` but it should probably go here. See\n * https://github.com/gpbl/react-day-picker/pull/1576\n */\nexport function getInitialFocusTarget(\n  displayMonths: Date[],\n  modifiers: Modifiers\n) {\n  const firstDayInMonth = startOfMonth(displayMonths[0]);\n  const lastDayInMonth = endOfMonth(displayMonths[displayMonths.length - 1]);\n\n  // TODO: cleanup code\n  let firstFocusableDay;\n  let today;\n  let date = firstDayInMonth;\n  while (date <= lastDayInMonth) {\n    const activeModifiers = getActiveModifiers(date, modifiers);\n    const isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n    if (!isFocusable) {\n      date = addDays(date, 1);\n      continue;\n    }\n    if (activeModifiers.selected) {\n      return date;\n    }\n    if (activeModifiers.today && !today) {\n      today = date;\n    }\n    if (!firstFocusableDay) {\n      firstFocusableDay = date;\n    }\n    date = addDays(date, 1);\n  }\n  if (today) {\n    return today;\n  } else {\n    return firstFocusableDay;\n  }\n}\n", "import {\n  addDays,\n  addMonths,\n  addWeeks,\n  addYears,\n  endOfISOWeek,\n  endOfWeek,\n  max,\n  min,\n  startOfISOWeek,\n  startOfWeek\n} from 'date-fns';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\nimport { getActiveModifiers } from 'contexts/Modifiers';\nimport { Modifiers } from 'types/Modifiers';\n\nexport type MoveFocusBy =\n  | 'day'\n  | 'week'\n  | 'startOfWeek'\n  | 'endOfWeek'\n  | 'month'\n  | 'year';\n\nexport type MoveFocusDirection = 'after' | 'before';\n\nexport type FocusDayPickerContext = Partial<\n  Pick<\n    DayPickerContextValue,\n    'ISOWeek' | 'weekStartsOn' | 'fromDate' | 'toDate' | 'locale'\n  >\n>;\n\nexport type FocusDayOptions = {\n  moveBy: MoveFocusBy;\n  direction: MoveFocusDirection;\n  context: FocusDayPickerContext;\n  modifiers?: Modifiers;\n  retry?: { count: number; lastFocused: Date };\n};\n\nconst MAX_RETRY = 365;\n\n/** Return the next date to be focused. */\nexport function getNextFocus(focusedDay: Date, options: FocusDayOptions): Date {\n  const {\n    moveBy,\n    direction,\n    context,\n    modifiers,\n    retry = { count: 0, lastFocused: focusedDay }\n  } = options;\n  const { weekStartsOn, fromDate, toDate, locale } = context;\n\n  const moveFns = {\n    day: addDays,\n    week: addWeeks,\n    month: addMonths,\n    year: addYears,\n    startOfWeek: (date: Date) =>\n      context.ISOWeek\n        ? startOfISOWeek(date)\n        : startOfWeek(date, { locale, weekStartsOn }),\n    endOfWeek: (date: Date) =>\n      context.ISOWeek\n        ? endOfISOWeek(date)\n        : endOfWeek(date, { locale, weekStartsOn })\n  };\n\n  let newFocusedDay = moveFns[moveBy](\n    focusedDay,\n    direction === 'after' ? 1 : -1\n  );\n\n  if (direction === 'before' && fromDate) {\n    newFocusedDay = max([fromDate, newFocusedDay]);\n  } else if (direction === 'after' && toDate) {\n    newFocusedDay = min([toDate, newFocusedDay]);\n  }\n  let isFocusable = true;\n\n  if (modifiers) {\n    const activeModifiers = getActiveModifiers(newFocusedDay, modifiers);\n    isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n  }\n  if (isFocusable) {\n    return newFocusedDay;\n  } else {\n    if (retry.count > MAX_RETRY) {\n      return retry.lastFocused;\n    }\n    return getNextFocus(newFocusedDay, {\n      moveBy,\n      direction,\n      context,\n      modifiers,\n      retry: {\n        ...retry,\n        count: retry.count + 1\n      }\n    });\n  }\n}\n", "import { createContext, ReactNode, useContext, useState } from 'react';\n\nimport { isSameDay } from 'date-fns';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { useModifiers } from '../Modifiers';\nimport { useNavigation } from '../Navigation';\nimport { getInitialFocusTarget } from './utils/getInitialFocusTarget';\nimport {\n  getNextFocus,\n  MoveFocusBy,\n  MoveFocusDirection\n} from './utils/getNextFocus';\n\n/** Represents the value of the {@link FocusContext}. */\nexport type FocusContextValue = {\n  /** The day currently focused. */\n  focusedDay: Date | undefined;\n  /** Day that will be focused.  */\n  focusTarget: Date | undefined;\n  /** Focus a day. */\n  focus: (day: Date) => void;\n  /** Blur the focused day. */\n  blur: () => void;\n  /** Focus the day after the focused day. */\n  focusDayAfter: () => void;\n  /** Focus the day before the focused day. */\n  focusDayBefore: () => void;\n  /** Focus the day in the week before the focused day. */\n  focusWeekBefore: () => void;\n  /** Focus the day in the week after the focused day. */\n  focusWeekAfter: () => void;\n  /* Focus the day in the month before the focused day. */\n  focusMonthBefore: () => void;\n  /* Focus the day in the month after the focused day. */\n  focusMonthAfter: () => void;\n  /* Focus the day in the year before the focused day. */\n  focusYearBefore: () => void;\n  /* Focus the day in the year after the focused day. */\n  focusYearAfter: () => void;\n  /* Focus the day at the start of the week of the focused day. */\n  focusStartOfWeek: () => void;\n  /* Focus the day at the end of the week of focused day. */\n  focusEndOfWeek: () => void;\n};\n\n/**\n * The Focus context shares details about the focused day for the keyboard\n *\n * Access this context from the {@link useFocusContext} hook.\n */\nexport const FocusContext = createContext<FocusContextValue | undefined>(\n  undefined\n);\n\nexport type FocusProviderProps = { children: ReactNode };\n\n/** The provider for the {@link FocusContext}. */\nexport function FocusProvider(props: FocusProviderProps): JSX.Element {\n  const navigation = useNavigation();\n  const modifiers = useModifiers();\n\n  const [focusedDay, setFocusedDay] = useState<Date | undefined>();\n  const [lastFocused, setLastFocused] = useState<Date | undefined>();\n\n  const initialFocusTarget = getInitialFocusTarget(\n    navigation.displayMonths,\n    modifiers\n  );\n\n  // TODO: cleanup and test obscure code below\n  const focusTarget =\n    focusedDay ?? (lastFocused && navigation.isDateDisplayed(lastFocused))\n      ? lastFocused\n      : initialFocusTarget;\n\n  const blur = () => {\n    setLastFocused(focusedDay);\n    setFocusedDay(undefined);\n  };\n  const focus = (date: Date) => {\n    setFocusedDay(date);\n  };\n\n  const context = useDayPicker();\n\n  const moveFocus = (moveBy: MoveFocusBy, direction: MoveFocusDirection) => {\n    if (!focusedDay) return;\n    const nextFocused = getNextFocus(focusedDay, {\n      moveBy,\n      direction,\n      context,\n      modifiers\n    });\n    if (isSameDay(focusedDay, nextFocused)) return undefined;\n    navigation.goToDate(nextFocused, focusedDay);\n    focus(nextFocused);\n  };\n\n  const value: FocusContextValue = {\n    focusedDay,\n    focusTarget,\n    blur,\n    focus,\n    focusDayAfter: () => moveFocus('day', 'after'),\n    focusDayBefore: () => moveFocus('day', 'before'),\n    focusWeekAfter: () => moveFocus('week', 'after'),\n    focusWeekBefore: () => moveFocus('week', 'before'),\n    focusMonthBefore: () => moveFocus('month', 'before'),\n    focusMonthAfter: () => moveFocus('month', 'after'),\n    focusYearBefore: () => moveFocus('year', 'before'),\n    focusYearAfter: () => moveFocus('year', 'after'),\n    focusStartOfWeek: () => moveFocus('startOfWeek', 'before'),\n    focusEndOfWeek: () => moveFocus('endOfWeek', 'after')\n  };\n\n  return (\n    <FocusContext.Provider value={value}>\n      {props.children}\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link FocusContextValue}. Use this hook to handle the\n * focus state of the elements.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useFocusContext(): FocusContextValue {\n  const context = useContext(FocusContext);\n  if (!context) {\n    throw new Error('useFocusContext must be used within a FocusProvider');\n  }\n  return context;\n}\n", "import { getActiveModifiers, useModifiers } from 'contexts/Modifiers';\nimport { ActiveModifiers } from 'types/Modifiers';\n\n/**\n * Return the active modifiers for the specified day.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n * @param day\n * @param displayMonth\n */\nexport function useActiveModifiers(\n  day: Date,\n  /**\n   * The month where the date is displayed. If not the same as `date`, the day\n   * is an \"outside day\".\n   */\n  displayMonth?: Date\n): ActiveModifiers {\n  const modifiers = useModifiers();\n  const activeModifiers = getActiveModifiers(day, modifiers, displayMonth);\n  return activeModifiers;\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport { DayPickerSingleProps, isDayPickerSingle } from 'types/DayPickerSingle';\nimport { DayClickEventHandler } from 'types/EventHandlers';\n\n/** Represents the value of a {@link SelectSingleContext}. */\nexport interface SelectSingleContextValue {\n  /** The day that has been selected. */\n  selected: Date | undefined;\n  /** Event handler to attach to the day button to enable the single select. */\n  onDayClick?: DayClickEventHandler;\n}\n\n/**\n * The SelectSingle context shares details about the selected days when in\n * single selection mode.\n *\n * Access this context from the {@link useSelectSingle} hook.\n */\nexport const SelectSingleContext = createContext<\n  SelectSingleContextValue | undefined\n>(undefined);\n\nexport interface SelectSingleProviderProps {\n  initialProps: DayPickerBase;\n  children?: ReactNode;\n}\n\n/** Provides the values for the {@link SelectSingleProvider}. */\nexport function SelectSingleProvider(\n  props: SelectSingleProviderProps\n): JSX.Element {\n  if (!isDayPickerSingle(props.initialProps)) {\n    const emptyContextValue: SelectSingleContextValue = {\n      selected: undefined\n    };\n    return (\n      <SelectSingleContext.Provider value={emptyContextValue}>\n        {props.children}\n      </SelectSingleContext.Provider>\n    );\n  }\n  return (\n    <SelectSingleProviderInternal\n      initialProps={props.initialProps}\n      children={props.children}\n    />\n  );\n}\n\n/** @private */\nexport interface SelectSingleProviderInternal {\n  initialProps: DayPickerSingleProps;\n  children?: ReactNode;\n}\n\nexport function SelectSingleProviderInternal({\n  initialProps,\n  children\n}: SelectSingleProviderInternal): JSX.Element {\n  const onDayClick: DayClickEventHandler = (day, activeModifiers, e) => {\n    initialProps.onDayClick?.(day, activeModifiers, e);\n\n    if (activeModifiers.selected && !initialProps.required) {\n      initialProps.onSelect?.(undefined, day, activeModifiers, e);\n      return;\n    }\n    initialProps.onSelect?.(day, day, activeModifiers, e);\n  };\n\n  const contextValue: SelectSingleContextValue = {\n    selected: initialProps.selected,\n    onDayClick\n  };\n  return (\n    <SelectSingleContext.Provider value={contextValue}>\n      {children}\n    </SelectSingleContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link SelectSingleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useSelectSingle(): SelectSingleContextValue {\n  const context = useContext(SelectSingleContext);\n  if (!context) {\n    throw new Error(\n      'useSelectSingle must be used within a SelectSingleProvider'\n    );\n  }\n  return context;\n}\n", "import {\n  FocusEventHandler,\n  HTMLProps,\n  KeyboardEventHandler,\n  MouseEventHandler,\n  PointerEventHandler,\n  TouchEventHandler\n} from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useFocusContext } from 'contexts/Focus';\nimport { useSelectMultiple } from 'contexts/SelectMultiple';\nimport { useSelectRange } from 'contexts/SelectRange';\nimport { useSelectSingle } from 'contexts/SelectSingle';\nimport { isDayPickerMultiple } from 'types/DayPickerMultiple';\nimport { isDayPickerRange } from 'types/DayPickerRange';\nimport { isDayPickerSingle } from 'types/DayPickerSingle';\nimport { ActiveModifiers } from 'types/Modifiers';\n\nexport type EventName =\n  | 'onClick'\n  | 'onFocus'\n  | 'onBlur'\n  | 'onKeyDown'\n  | 'onKeyUp'\n  | 'onMouseEnter'\n  | 'onMouseLeave'\n  | 'onPointerEnter'\n  | 'onPointerLeave'\n  | 'onTouchCancel'\n  | 'onTouchEnd'\n  | 'onTouchMove'\n  | 'onTouchStart';\n\nexport type DayEventName =\n  | 'onDayClick'\n  | 'onDayFocus'\n  | 'onDayBlur'\n  | 'onDayKeyDown'\n  | 'onDayKeyUp'\n  | 'onDayMouseEnter'\n  | 'onDayMouseLeave'\n  | 'onDayPointerEnter'\n  | 'onDayPointerLeave'\n  | 'onDayTouchCancel'\n  | 'onDayTouchEnd'\n  | 'onDayTouchMove'\n  | 'onDayTouchStart';\n\nexport type DayEventHandlers = Pick<HTMLProps<HTMLButtonElement>, EventName>;\n\n/**\n * This hook returns details about the content to render in the day cell.\n *\n *\n * When a day cell is rendered in the table, DayPicker can either:\n *\n * - render nothing: when the day is outside the month or has matched the\n *   \"hidden\" modifier.\n * - render a button when `onDayClick` or a selection mode is set.\n * - render a non-interactive element: when no selection mode is set, the day\n *   cell shouldn’t respond to any interaction. DayPicker should render a `div`\n *   or a `span`.\n *\n * ### Usage\n *\n * Use this hook to customize the behavior of the {@link Day} component. Create a\n * new `Day` component using this hook and pass it to the `components` prop.\n * The source of {@link Day} can be a good starting point.\n *\n */\nexport function useDayEventHandlers(\n  date: Date,\n  activeModifiers: ActiveModifiers\n): DayEventHandlers {\n  const dayPicker = useDayPicker();\n  const single = useSelectSingle();\n  const multiple = useSelectMultiple();\n  const range = useSelectRange();\n  const {\n    focusDayAfter,\n    focusDayBefore,\n    focusWeekAfter,\n    focusWeekBefore,\n    blur,\n    focus,\n    focusMonthBefore,\n    focusMonthAfter,\n    focusYearBefore,\n    focusYearAfter,\n    focusStartOfWeek,\n    focusEndOfWeek\n  } = useFocusContext();\n\n  const onClick: MouseEventHandler = (e) => {\n    if (isDayPickerSingle(dayPicker)) {\n      single.onDayClick?.(date, activeModifiers, e);\n    } else if (isDayPickerMultiple(dayPicker)) {\n      multiple.onDayClick?.(date, activeModifiers, e);\n    } else if (isDayPickerRange(dayPicker)) {\n      range.onDayClick?.(date, activeModifiers, e);\n    } else {\n      dayPicker.onDayClick?.(date, activeModifiers, e);\n    }\n  };\n\n  const onFocus: FocusEventHandler = (e) => {\n    focus(date);\n    dayPicker.onDayFocus?.(date, activeModifiers, e);\n  };\n\n  const onBlur: FocusEventHandler = (e) => {\n    blur();\n    dayPicker.onDayBlur?.(date, activeModifiers, e);\n  };\n\n  const onMouseEnter: MouseEventHandler = (e) => {\n    dayPicker.onDayMouseEnter?.(date, activeModifiers, e);\n  };\n  const onMouseLeave: MouseEventHandler = (e) => {\n    dayPicker.onDayMouseLeave?.(date, activeModifiers, e);\n  };\n  const onPointerEnter: PointerEventHandler = (e) => {\n    dayPicker.onDayPointerEnter?.(date, activeModifiers, e);\n  };\n  const onPointerLeave: PointerEventHandler = (e) => {\n    dayPicker.onDayPointerLeave?.(date, activeModifiers, e);\n  };\n  const onTouchCancel: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchCancel?.(date, activeModifiers, e);\n  };\n  const onTouchEnd: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchEnd?.(date, activeModifiers, e);\n  };\n  const onTouchMove: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchMove?.(date, activeModifiers, e);\n  };\n  const onTouchStart: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchStart?.(date, activeModifiers, e);\n  };\n\n  const onKeyUp: KeyboardEventHandler = (e) => {\n    dayPicker.onDayKeyUp?.(date, activeModifiers, e);\n  };\n\n  const onKeyDown: KeyboardEventHandler = (e) => {\n    switch (e.key) {\n      case 'ArrowLeft':\n        e.preventDefault();\n        e.stopPropagation();\n        dayPicker.dir === 'rtl' ? focusDayAfter() : focusDayBefore();\n        break;\n      case 'ArrowRight':\n        e.preventDefault();\n        e.stopPropagation();\n        dayPicker.dir === 'rtl' ? focusDayBefore() : focusDayAfter();\n        break;\n      case 'ArrowDown':\n        e.preventDefault();\n        e.stopPropagation();\n        focusWeekAfter();\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        e.stopPropagation();\n        focusWeekBefore();\n        break;\n      case 'PageUp':\n        e.preventDefault();\n        e.stopPropagation();\n        e.shiftKey ? focusYearBefore() : focusMonthBefore();\n        break;\n      case 'PageDown':\n        e.preventDefault();\n        e.stopPropagation();\n        e.shiftKey ? focusYearAfter() : focusMonthAfter();\n        break;\n      case 'Home':\n        e.preventDefault();\n        e.stopPropagation();\n        focusStartOfWeek();\n        break;\n      case 'End':\n        e.preventDefault();\n        e.stopPropagation();\n        focusEndOfWeek();\n        break;\n    }\n    dayPicker.onDayKeyDown?.(date, activeModifiers, e);\n  };\n\n  const eventHandlers: DayEventHandlers = {\n    onClick,\n    onFocus,\n    onBlur,\n    onKeyDown,\n    onKeyUp,\n    onMouseEnter,\n    onMouseLeave,\n    onPointerEnter,\n    onPointerLeave,\n    onTouchCancel,\n    onTouchEnd,\n    onTouchMove,\n    onTouchStart\n  };\n\n  return eventHandlers;\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\nimport { useSelectMultiple } from 'contexts/SelectMultiple';\nimport { useSelectRange } from 'contexts/SelectRange';\nimport { useSelectSingle } from 'contexts/SelectSingle';\nimport { isDayPickerMultiple } from 'types/DayPickerMultiple';\nimport { isDayPickerRange } from 'types/DayPickerRange';\nimport { isDayPickerSingle } from 'types/DayPickerSingle';\nimport { DateRange } from 'types/Matchers';\n\nexport type SelectedDays = Date | Date[] | DateRange | undefined;\n\n/**\n * Return the current selected days when DayPicker is in selection mode. Days\n * selected by the custom selection mode are not returned.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n */\nexport function useSelectedDays(): SelectedDays {\n  const dayPicker = useDayPicker();\n  const single = useSelectSingle();\n  const multiple = useSelectMultiple();\n  const range = useSelectRange();\n\n  const selectedDays = isDayPickerSingle(dayPicker)\n    ? single.selected\n    : isDayPickerMultiple(dayPicker)\n      ? multiple.selected\n      : isDayPickerRange(dayPicker)\n        ? range.selected\n        : undefined;\n\n  return selectedDays;\n}\n", "import { DayPickerContextValue } from 'contexts/DayPicker';\nimport { ActiveModifiers, InternalModifier } from 'types/Modifiers';\n\nfunction isInternalModifier(modifier: string): modifier is InternalModifier {\n  return Object.values(InternalModifier).includes(modifier as InternalModifier);\n}\n\n/**\n * Return the class names for the Day element, according to the given active\n * modifiers.\n *\n * Custom class names are set via `modifiersClassNames` or `classNames`,\n * where the first have the precedence.\n */\nexport function getDayClassNames(\n  dayPicker: Pick<DayPickerContextValue, 'modifiersClassNames' | 'classNames'>,\n  activeModifiers: ActiveModifiers\n) {\n  const classNames: string[] = [dayPicker.classNames.day];\n  Object.keys(activeModifiers).forEach((modifier) => {\n    const customClassName = dayPicker.modifiersClassNames[modifier];\n    if (customClassName) {\n      classNames.push(customClassName);\n    } else if (isInternalModifier(modifier)) {\n      const internalClassName = dayPicker.classNames[`day_${modifier}`];\n      if (internalClassName) {\n        classNames.push(internalClassName);\n      }\n    }\n  });\n  return classNames;\n}\n", "import { CSSProperties } from 'react';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\nimport { ActiveModifiers } from 'types/Modifiers';\n\n/** Return the style for the Day element, according to the given active modifiers. */\nexport function getDayStyle(\n  dayPicker: Pick<DayPickerContextValue, 'modifiersStyles' | 'styles'>,\n  activeModifiers: ActiveModifiers\n): CSSProperties {\n  let style: CSSProperties = {\n    ...dayPicker.styles.day\n  };\n  Object.keys(activeModifiers).forEach((modifier) => {\n    style = {\n      ...style,\n      ...dayPicker.modifiersStyles?.[modifier]\n    };\n  });\n  return style;\n}\n", "import { RefObject, useEffect } from 'react';\n\nimport { isSameDay } from 'date-fns';\n\nimport { ButtonProps } from 'components/Button';\nimport { DayContent } from 'components/DayContent';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useFocusContext } from 'contexts/Focus';\nimport { useActiveModifiers } from 'hooks/useActiveModifiers';\nimport {\n  DayEventHandlers,\n  useDayEventHandlers\n} from 'hooks/useDayEventHandlers';\nimport { SelectedDays, useSelectedDays } from 'hooks/useSelectedDays';\nimport { ActiveModifiers } from 'types/Modifiers';\nimport { StyledComponent } from 'types/Styles';\n\nimport { getDayClassNames } from './utils/getDayClassNames';\nimport { getDayStyle } from './utils/getDayStyle';\n\nexport type DayRender = {\n  /** Whether the day should be rendered a `button` instead of a `div` */\n  isButton: boolean;\n  /** Whether the day should be hidden. */\n  isHidden: boolean;\n  /** The modifiers active for the given day. */\n  activeModifiers: ActiveModifiers;\n  /** The props to apply to the button element (when `isButton` is true). */\n  buttonProps: StyledComponent &\n    Pick<ButtonProps, 'disabled' | 'aria-selected' | 'tabIndex'> &\n    DayEventHandlers;\n  /** The props to apply to the div element (when `isButton` is false). */\n  divProps: StyledComponent;\n  selectedDays: SelectedDays;\n};\n\n/**\n * Return props and data used to render the {@link Day} component.\n *\n * Use this hook when creating a component to replace the built-in `Day`\n * component.\n */\nexport function useDayRender(\n  /** The date to render. */\n  day: Date,\n  /** The month where the date is displayed (if not the same as `date`, it means it is an \"outside\" day). */\n  displayMonth: Date,\n  /** A ref to the button element that will be target of focus when rendered (if required). */\n  buttonRef: RefObject<HTMLButtonElement>\n): DayRender {\n  const dayPicker = useDayPicker();\n  const focusContext = useFocusContext();\n  const activeModifiers = useActiveModifiers(day, displayMonth);\n  const eventHandlers = useDayEventHandlers(day, activeModifiers);\n  const selectedDays = useSelectedDays();\n  const isButton = Boolean(\n    dayPicker.onDayClick || dayPicker.mode !== 'default'\n  );\n\n  // Focus the button if the day is focused according to the focus context\n  useEffect(() => {\n    if (activeModifiers.outside) return;\n    if (!focusContext.focusedDay) return;\n    if (!isButton) return;\n    if (isSameDay(focusContext.focusedDay, day)) {\n      buttonRef.current?.focus();\n    }\n  }, [\n    focusContext.focusedDay,\n    day,\n    buttonRef,\n    isButton,\n    activeModifiers.outside\n  ]);\n\n  const className = getDayClassNames(dayPicker, activeModifiers).join(' ');\n  const style = getDayStyle(dayPicker, activeModifiers);\n  const isHidden = Boolean(\n    (activeModifiers.outside && !dayPicker.showOutsideDays) ||\n      activeModifiers.hidden\n  );\n\n  const DayContentComponent = dayPicker.components?.DayContent ?? DayContent;\n  const children = (\n    <DayContentComponent\n      date={day}\n      displayMonth={displayMonth}\n      activeModifiers={activeModifiers}\n    />\n  );\n\n  const divProps = {\n    style,\n    className,\n    children,\n    role: 'gridcell'\n  };\n\n  const isFocusTarget =\n    focusContext.focusTarget &&\n    isSameDay(focusContext.focusTarget, day) &&\n    !activeModifiers.outside;\n\n  const isFocused =\n    focusContext.focusedDay && isSameDay(focusContext.focusedDay, day);\n\n  const buttonProps = {\n    ...divProps,\n    disabled: activeModifiers.disabled,\n    role: 'gridcell',\n    ['aria-selected']: activeModifiers.selected,\n    tabIndex: isFocused || isFocusTarget ? 0 : -1,\n    ...eventHandlers\n  };\n\n  const dayRender: DayRender = {\n    isButton,\n    isHidden,\n    activeModifiers: activeModifiers,\n    selectedDays,\n    buttonProps,\n    divProps\n  };\n\n  return dayRender;\n}\n", "import { useRef } from 'react';\n\nimport { useDayRender } from 'hooks/useDayRender';\n\nimport { Button } from '../Button';\n\n/** Represent the props used by the {@link Day} component. */\nexport interface DayProps {\n  /** The month where the date is displayed. */\n  displayMonth: Date;\n  /** The date to render. */\n  date: Date;\n}\n\n/**\n * The content of a day cell – as a button or span element according to its\n * modifiers.\n */\nexport function Day(props: DayProps): JSX.Element {\n  const buttonRef = useRef<HTMLButtonElement>(null);\n  const dayRender = useDayRender(props.date, props.displayMonth, buttonRef);\n\n  if (dayRender.isHidden) {\n    return <div role=\"gridcell\"></div>;\n  }\n  if (!dayRender.isButton) {\n    return <div {...dayRender.divProps} />;\n  }\n  return <Button name=\"day\" ref={buttonRef} {...dayRender.buttonProps} />;\n}\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { Button } from '../Button';\n\n/**\n * The props for the {@link WeekNumber} component.\n */\nexport interface WeekNumberProps {\n  /** The number of the week. */\n  number: number;\n  /** The dates in the week. */\n  dates: Date[];\n}\n\n/**\n * Render the week number element. If `onWeekNumberClick` is passed to DayPicker, it\n * renders a button, otherwise a span element.\n */\nexport function WeekNumber(props: WeekNumberProps): JSX.Element {\n  const { number: weekNumber, dates } = props;\n  const {\n    onWeekNumberClick,\n    styles,\n    classNames,\n    locale,\n    labels: { labelWeekNumber },\n    formatters: { formatWeekNumber }\n  } = useDayPicker();\n\n  const content = formatWeekNumber(Number(weekNumber), { locale });\n\n  if (!onWeekNumberClick) {\n    return (\n      <span className={classNames.weeknumber} style={styles.weeknumber}>\n        {content}\n      </span>\n    );\n  }\n\n  const label = labelWeekNumber(Number(weekNumber), { locale });\n\n  const handleClick: MouseEventHandler = function (e) {\n    onWeekNumberClick(weekNumber, dates, e);\n  };\n\n  return (\n    <Button\n      name=\"week-number\"\n      aria-label={label}\n      className={classNames.weeknumber}\n      style={styles.weeknumber}\n      onClick={handleClick}\n    >\n      {content}\n    </Button>\n  );\n}\n", "import { getUnixTime } from 'date-fns';\n\nimport { Day } from 'components/Day';\nimport { WeekNumber } from 'components/WeekNumber';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/**\n * The props for the {@link Row} component.\n */\nexport interface RowProps {\n  /** The month where the row is displayed. */\n  displayMonth: Date;\n  /** The number of the week to render. */\n  weekNumber: number;\n  /** The days contained in the week. */\n  dates: Date[];\n}\n\n/** Render a row in the calendar, with the days and the week number. */\nexport function Row(props: RowProps): JSX.Element {\n  const { styles, classNames, showWeekNumber, components } = useDayPicker();\n\n  const DayComponent = components?.Day ?? Day;\n  const WeeknumberComponent = components?.WeekNumber ?? WeekNumber;\n\n  let weekNumberCell;\n  if (showWeekNumber) {\n    weekNumberCell = (\n      <td className={classNames.cell} style={styles.cell}>\n        <WeeknumberComponent number={props.weekNumber} dates={props.dates} />\n      </td>\n    );\n  }\n\n  return (\n    <tr className={classNames.row} style={styles.row}>\n      {weekNumberCell}\n      {props.dates.map((date) => (\n        <td\n          className={classNames.cell}\n          style={styles.cell}\n          key={getUnixTime(date)}\n          role=\"presentation\"\n        >\n          <DayComponent displayMonth={props.displayMonth} date={date} />\n        </td>\n      ))}\n    </tr>\n  );\n}\n", "import {\n  addDays,\n  differenceInCalendarDays,\n  endOfISOWeek,\n  endOfWeek,\n  getISOWeek,\n  getWeek,\n  Locale,\n  startOfISOWeek,\n  startOfWeek\n} from 'date-fns';\n\nimport { MonthWeek } from './getMonthWeeks';\n\n/** Return the weeks between two dates.  */\nexport function daysToMonthWeeks(\n  fromDate: Date,\n  toDate: Date,\n  options?: {\n    ISOWeek?: boolean;\n    locale?: Locale;\n    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;\n    firstWeekContainsDate?: 1 | 4;\n  }\n): MonthWeek[] {\n  const toWeek = options?.ISOWeek\n    ? endOfISOWeek(toDate)\n    : endOfWeek(toDate, options);\n  const fromWeek = options?.ISOWeek\n    ? startOfISOWeek(fromDate)\n    : startOfWeek(fromDate, options);\n\n  const nOfDays = differenceInCalendarDays(toWeek, fromWeek);\n  const days: Date[] = [];\n\n  for (let i = 0; i <= nOfDays; i++) {\n    days.push(addDays(fromWeek, i));\n  }\n\n  const weeksInMonth = days.reduce((result: MonthWeek[], date) => {\n    const weekNumber = options?.ISOWeek\n      ? getISOWeek(date)\n      : getWeek(date, options);\n\n    const existingWeek = result.find(\n      (value) => value.weekNumber === weekNumber\n    );\n    if (existingWeek) {\n      existingWeek.dates.push(date);\n      return result;\n    }\n    result.push({\n      weekNumber,\n      dates: [date]\n    });\n    return result;\n  }, []);\n\n  return weeksInMonth;\n}\n", "import {\n  addWeeks,\n  endOfMonth,\n  getWeeksInMonth,\n  Locale,\n  startOfMonth\n} from 'date-fns';\n\nimport { daysToMonthWeeks } from './daysToMonthWeeks';\n\n/** Represents a week in the month.*/\nexport type MonthWeek = {\n  /** The week number from the start of the year. */\n  weekNumber: number;\n  /** The dates in the week. */\n  dates: Date[];\n};\n\n/**\n * Return the weeks belonging to the given month, adding the \"outside days\" to\n * the first and last week.\n */\nexport function getMonthWeeks(\n  month: Date,\n  options: {\n    locale: Locale;\n    useFixedWeeks?: boolean;\n    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;\n    firstWeekContainsDate?: 1 | 4;\n    ISOWeek?: boolean;\n  }\n): MonthWeek[] {\n  const weeksInMonth: MonthWeek[] = daysToMonthWeeks(\n    startOfMonth(month),\n    endOfMonth(month),\n    options\n  );\n\n  if (options?.useFixedWeeks) {\n    // Add extra weeks to the month, up to 6 weeks\n    const nrOfMonthWeeks = getWeeksInMonth(month, options);\n    if (nrOfMonthWeeks < 6) {\n      const lastWeek = weeksInMonth[weeksInMonth.length - 1];\n      const lastDate = lastWeek.dates[lastWeek.dates.length - 1];\n      const toDate = addWeeks(lastDate, 6 - nrOfMonthWeeks);\n      const extraWeeks = daysToMonthWeeks(\n        addWeeks(lastDate, 1),\n        toDate,\n        options\n      );\n      weeksInMonth.push(...extraWeeks);\n    }\n  }\n  return weeksInMonth;\n}\n", "import { Footer } from 'components/Footer';\nimport { Head } from 'components/Head';\nimport { Row } from 'components/Row';\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { getMonthWeeks } from './utils/getMonthWeeks';\n\n/** The props for the {@link Table} component. */\nexport interface TableProps {\n  /** ID of table element */\n  id?: string;\n  /** The ID of the label of the table (the same given to the Caption). */\n  ['aria-labelledby']?: string;\n  /** The month where the table is displayed. */\n  displayMonth: Date;\n}\n\n/** Render the table with the calendar. */\nexport function Table(props: TableProps): JSX.Element {\n  const {\n    locale,\n    classNames,\n    styles,\n    hideHead,\n    fixedWeeks,\n    components,\n    weekStartsOn,\n    firstWeekContainsDate,\n    ISOWeek\n  } = useDayPicker();\n\n  const weeks = getMonthWeeks(props.displayMonth, {\n    useFixedWeeks: Boolean(fixedWeeks),\n    ISOWeek,\n    locale,\n    weekStartsOn,\n    firstWeekContainsDate\n  });\n\n  const HeadComponent = components?.Head ?? Head;\n  const RowComponent = components?.Row ?? Row;\n  const FooterComponent = components?.Footer ?? Footer;\n  return (\n    <table\n      id={props.id}\n      className={classNames.table}\n      style={styles.table}\n      role=\"grid\"\n      aria-labelledby={props['aria-labelledby']}\n    >\n      {!hideHead && <HeadComponent />}\n      <tbody className={classNames.tbody} style={styles.tbody}>\n        {weeks.map((week) => (\n          <RowComponent\n            displayMonth={props.displayMonth}\n            key={week.weekNumber}\n            dates={week.dates}\n            weekNumber={week.weekNumber}\n          />\n        ))}\n      </tbody>\n      <FooterComponent displayMonth={props.displayMonth} />\n    </table>\n  );\n}\n", "/*\nThe MIT License (MIT)\n\nCopyright (c) 2018-present, React Training LLC\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n/* eslint-disable prefer-const */\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n/*\n * Welcome to @reach/auto-id!\n * Let's see if we can make sense of why this hook exists and its\n * implementation.\n *\n * Some background:\n *   1. Accessibility APIs rely heavily on element IDs\n *   2. Requiring developers to put IDs on every element in Reach UI is both\n *      cumbersome and error-prone\n *   3. With a component model, we can generate IDs for them!\n *\n * Solution 1: Generate random IDs.\n *\n * This works great as long as you don't server render your app. When React (in\n * the client) tries to reuse the markup from the server, the IDs won't match\n * and React will then recreate the entire DOM tree.\n *\n * Solution 2: Increment an integer\n *\n * This sounds great. Since we're rendering the exact same tree on the server\n * and client, we can increment a counter and get a deterministic result between\n * client and server. Also, JS integers can go up to nine-quadrillion. I'm\n * pretty sure the tab will be closed before an app never needs\n * 10 quadrillion IDs!\n *\n * Problem solved, right?\n *\n * Ah, but there's a catch! React's concurrent rendering makes this approach\n * non-deterministic. While the client and server will end up with the same\n * elements in the end, depending on suspense boundaries (and possibly some user\n * input during the initial render) the incrementing integers won't always match\n * up.\n *\n * Solution 3: Don't use IDs at all on the server; patch after first render.\n *\n * What we've done here is solution 2 with some tricks. With this approach, the\n * ID returned is an empty string on the first render. This way the server and\n * client have the same markup no matter how wild the concurrent rendering may\n * have gotten.\n *\n * After the render, we patch up the components with an incremented ID. This\n * causes a double render on any components with `useId`. Shouldn't be a problem\n * since the components using this hook should be small, and we're only updating\n * the ID attribute on the DOM, nothing big is happening.\n *\n * It doesn't have to be an incremented number, though--we could do generate\n * random strings instead, but incrementing a number is probably the cheapest\n * thing we can do.\n *\n * Additionally, we only do this patchup on the very first client render ever.\n * Any calls to `useId` that happen dynamically in the client will be\n * populated immediately with a value. So, we only get the double render after\n * server hydration and never again, SO BACK OFF ALRIGHT?\n */\n\nimport { useEffect, useLayoutEffect, useState } from 'react';\n\nfunction canUseDOM() {\n  return !!(\n    typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement\n  );\n}\n/**\n * React currently throws a warning when using useLayoutEffect on the server. To\n * get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect in the browser. We occasionally need useLayoutEffect to\n * ensure we don't get a render flash for certain operations, but we may also\n * need affected components to render on the server. One example is when setting\n * a component's descendants to retrieve their index values.\n *\n * Important to note that using this hook as an escape hatch will break the\n * eslint dependency warnings unless you rename the import to `useLayoutEffect`.\n * Use sparingly only when the effect won't effect the rendered HTML to avoid\n * any server/client mismatch.\n *\n * If a useLayoutEffect is needed and the result would create a mismatch, it's\n * likely that the component in question shouldn't be rendered on the server at\n * all, so a better approach would be to lazily render those in a parent\n * component after client-side hydration.\n *\n * https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * https://github.com/reduxjs/react-redux/blob/master/src/utils/useIsomorphicLayoutEffect.js\n *\n * @param effect\n * @param deps\n */\nconst useIsomorphicLayoutEffect = canUseDOM() ? useLayoutEffect : useEffect;\n\nlet serverHandoffComplete = false;\nlet id = 0;\nfunction genId() {\n  return `react-day-picker-${++id}`;\n}\n\n/* eslint-disable react-hooks/rules-of-hooks */\n\n/**\n * useId\n *\n * Autogenerate IDs to facilitate WAI-ARIA and server rendering.\n *\n * Note: The returned ID will initially be `null` and will update after a\n * component mounts. Users may need to supply their own ID if they need\n * consistent values for SSR.\n *\n * @see Docs https://reach.tech/auto-id\n */\nfunction useId(idFromProps: string): string;\nfunction useId(idFromProps: number): number;\nfunction useId(idFromProps: string | number): string | number;\nfunction useId(idFromProps: string | undefined | null): string | undefined;\nfunction useId(idFromProps: number | undefined | null): number | undefined;\nfunction useId(\n  idFromProps: string | number | undefined | null\n): string | number | undefined;\nfunction useId(): string | undefined;\n\nfunction useId(providedId?: number | string | undefined | null) {\n  // TODO: Remove error flag when updating internal deps to React 18. None of\n  // our tricks will play well with concurrent rendering anyway.\n\n  // If this instance isn't part of the initial render, we don't have to do the\n  // double render/patch-up dance. We can just generate the ID and return it.\n  let initialId = providedId ?? (serverHandoffComplete ? genId() : null);\n  let [id, setId] = useState(initialId);\n\n  useIsomorphicLayoutEffect(() => {\n    if (id === null) {\n      // Patch the ID after render. We do this in `useLayoutEffect` to avoid any\n      // rendering flicker, though it'll make the first render slower (unlikely\n      // to matter, but you're welcome to measure your app and let us know if\n      // it's a problem).\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  useEffect(() => {\n    if (serverHandoffComplete === false) {\n      // Flag all future uses of `useId` to skip the update dance. This is in\n      // `useEffect` because it goes after `useLayoutEffect`, ensuring we don't\n      // accidentally bail out of the patch-up dance prematurely.\n      serverHandoffComplete = true;\n    }\n  }, []);\n\n  return providedId ?? id ?? undefined;\n}\n\nexport { useId, canUseDOM };\n", "import { Caption } from 'components/Caption';\nimport { Table } from 'components/Table';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useNavigation } from 'contexts/Navigation';\nimport { useId } from 'hooks/useId';\n\n/** The props for the {@link Month} component. */\nexport interface MonthProps {\n  displayIndex: number;\n  displayMonth: Date;\n}\n\n/** Render a month. */\nexport function Month(props: MonthProps) {\n  const dayPicker = useDayPicker();\n  const { dir, classNames, styles, components } = dayPicker;\n  const { displayMonths } = useNavigation();\n\n  const captionId = useId(\n    dayPicker.id ? `${dayPicker.id}-${props.displayIndex}` : undefined\n  );\n\n  const tableId = dayPicker.id\n    ? `${dayPicker.id}-grid-${props.displayIndex}`\n    : undefined;\n\n  const className = [classNames.month];\n  let style = styles.month;\n\n  let isStart = props.displayIndex === 0;\n  let isEnd = props.displayIndex === displayMonths.length - 1;\n  const isCenter = !isStart && !isEnd;\n  if (dir === 'rtl') {\n    [isEnd, isStart] = [isStart, isEnd];\n  }\n\n  if (isStart) {\n    className.push(classNames.caption_start);\n    style = { ...style, ...styles.caption_start };\n  }\n  if (isEnd) {\n    className.push(classNames.caption_end);\n    style = { ...style, ...styles.caption_end };\n  }\n  if (isCenter) {\n    className.push(classNames.caption_between);\n    style = { ...style, ...styles.caption_between };\n  }\n\n  const CaptionComponent = components?.Caption ?? Caption;\n\n  return (\n    <div key={props.displayIndex} className={className.join(' ')} style={style}>\n      <CaptionComponent\n        id={captionId}\n        displayMonth={props.displayMonth}\n        displayIndex={props.displayIndex}\n      />\n      <Table\n        id={tableId}\n        aria-labelledby={captionId}\n        displayMonth={props.displayMonth}\n      />\n    </div>\n  );\n}\n", "import { ReactNode } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link Months} component. */\nexport type MonthsProps = { children: ReactNode };\n\n/**\n * Render the wrapper for the month grids.\n */\nexport function Months(props: MonthsProps): JSX.Element {\n  const { classNames, styles } = useDayPicker();\n\n  return (\n    <div className={classNames.months} style={styles.months}>\n      {props.children}\n    </div>\n  );\n}\n", "import { useEffect, useState } from 'react';\n\nimport { DayPickerProps } from 'DayPicker';\n\nimport { Month } from 'components/Month';\nimport { Months } from 'components/Months';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useFocusContext } from 'contexts/Focus';\nimport { useNavigation } from 'contexts/Navigation';\n\nfunction isDataAttributes(attrs: DayPickerProps): attrs is {\n  [key: string]: string | boolean | number | undefined;\n} {\n  return true;\n}\n\nexport interface RootProps {\n  initialProps: DayPickerProps;\n}\n\n/** Render the container with the months according to the number of months to display. */\nexport function Root({ initialProps }: RootProps): JSX.Element {\n  const dayPicker = useDayPicker();\n  const focusContext = useFocusContext();\n  const navigation = useNavigation();\n\n  const [hasInitialFocus, setHasInitialFocus] = useState(false);\n\n  // Focus the focus target when initialFocus is passed in\n  useEffect(() => {\n    if (!dayPicker.initialFocus) return;\n    if (!focusContext.focusTarget) return;\n    if (hasInitialFocus) return;\n\n    focusContext.focus(focusContext.focusTarget);\n    setHasInitialFocus(true);\n  }, [\n    dayPicker.initialFocus,\n    hasInitialFocus,\n    focusContext.focus,\n    focusContext.focusTarget,\n    focusContext\n  ]);\n\n  // Apply classnames according to props\n  const classNames = [dayPicker.classNames.root, dayPicker.className];\n  if (dayPicker.numberOfMonths > 1) {\n    classNames.push(dayPicker.classNames.multiple_months);\n  }\n  if (dayPicker.showWeekNumber) {\n    classNames.push(dayPicker.classNames.with_weeknumber);\n  }\n\n  const style = {\n    ...dayPicker.styles.root,\n    ...dayPicker.style\n  };\n\n  const dataAttributes = Object.keys(initialProps)\n    .filter((key) => key.startsWith('data-'))\n    .reduce((attrs, key) => {\n      if (!isDataAttributes(initialProps)) return attrs;\n      return {\n        ...attrs,\n        [key]: initialProps[key]\n      };\n    }, {});\n\n  const MonthsComponent = initialProps.components?.Months ?? Months;\n\n  return (\n    <div\n      className={classNames.join(' ')}\n      style={style}\n      dir={dayPicker.dir}\n      id={dayPicker.id}\n      nonce={initialProps.nonce}\n      title={initialProps.title}\n      lang={initialProps.lang}\n      {...dataAttributes}\n    >\n      <MonthsComponent>\n        {navigation.displayMonths.map((month, i) => (\n          <Month key={i} displayIndex={i} displayMonth={month} />\n        ))}\n      </MonthsComponent>\n    </div>\n  );\n}\n", "import { ReactNode } from 'react';\n\nimport { ModifiersProvider } from 'contexts/Modifiers/ModifiersContext';\n\nimport { DayPickerProvider } from './DayPicker';\nimport { FocusProvider } from './Focus';\nimport { NavigationProvider } from './Navigation';\nimport { SelectMultipleProvider } from './SelectMultiple';\nimport { SelectRangeProvider } from './SelectRange';\nimport { SelectSingleProvider } from './SelectSingle';\nimport { DayPickerDefaultProps } from 'types/DayPickerDefault';\nimport { DayPickerSingleProps } from 'types/DayPickerSingle';\nimport { DayPickerMultipleProps } from 'types/DayPickerMultiple';\nimport { DayPickerRangeProps } from 'types/DayPickerRange';\n\ntype RootContextProps =\n  | Partial<DayPickerDefaultProps>\n  | Partial<DayPickerSingleProps>\n  | Partial<DayPickerMultipleProps>\n  | Partial<DayPickerRangeProps>;\n\n/** The props of {@link RootProvider}. */\nexport type RootContext = RootContextProps & {\n  children?: ReactNode;\n};\n\n/** Provide the value for all the context providers. */\nexport function RootProvider(props: RootContext): JSX.Element {\n  const { children, ...initialProps } = props;\n\n  return (\n    <DayPickerProvider initialProps={initialProps}>\n      <NavigationProvider>\n        <SelectSingleProvider initialProps={initialProps}>\n          <SelectMultipleProvider initialProps={initialProps}>\n            <SelectRangeProvider initialProps={initialProps}>\n              <ModifiersProvider>\n                <FocusProvider>{children}</FocusProvider>\n              </ModifiersProvider>\n            </SelectRangeProvider>\n          </SelectMultipleProvider>\n        </SelectSingleProvider>\n      </NavigationProvider>\n    </DayPickerProvider>\n  );\n}\n", "import { DayPickerDefaultProps } from 'types/DayPickerDefault';\nimport { DayPickerMultipleProps } from 'types/DayPickerMultiple';\nimport { DayPickerRangeProps } from 'types/DayPickerRange';\nimport { DayPickerSingleProps } from 'types/DayPickerSingle';\n\nimport { Root } from './components/Root';\nimport { RootProvider } from './contexts/RootProvider';\n\nexport type DayPickerProps =\n  | DayPickerDefaultProps\n  | DayPickerSingleProps\n  | DayPickerMultipleProps\n  | DayPickerRangeProps;\n\n/**\n * DayPicker render a date picker component to let users pick dates from a\n * calendar. See http://react-day-picker.js.org for updated documentation and\n * examples.\n *\n * ### Customization\n *\n * DayPicker offers different customization props. For example,\n *\n * - show multiple months using `numberOfMonths`\n * - display a dropdown to navigate the months via `captionLayout`\n * - display the week numbers with `showWeekNumbers`\n * - disable or hide days with `disabled` or `hidden`\n *\n * ### Controlling the months\n *\n * Change the initially displayed month using the `defaultMonth` prop. The\n * displayed months are controlled by DayPicker and stored in its internal\n * state. To control the months yourself, use `month` instead of `defaultMonth`\n * and use the `onMonthChange` event to set it.\n *\n * To limit the months the user can navigate to, use\n * `fromDate`/`fromMonth`/`fromYear` or `toDate`/`toMonth`/`toYear`.\n *\n * ### Selection modes\n *\n * DayPicker supports different selection mode that can be toggled using the\n * `mode` prop:\n *\n * - `mode=\"single\"`: only one day can be selected. Use `required` to make the\n *   selection required. Use the `onSelect` event handler to get the selected\n *   days.\n * - `mode=\"multiple\"`: users can select one or more days. Limit the amount of\n *   days that can be selected with the `min` or the `max` props.\n * - `mode=\"range\"`: users can select a range of days. Limit the amount of days\n *   in the range with the `min` or the `max` props.\n * - `mode=\"default\"` (default): the built-in selections are disabled. Implement\n *   your own selection mode with `onDayClick`.\n *\n * The selection modes should cover the most common use cases. In case you\n * need a more refined way of selecting days, use `mode=\"default\"`. Use the\n * `selected` props and add the day event handlers to add/remove days from the\n * selection.\n *\n * ### Modifiers\n *\n * A _modifier_ represents different styles or states for the days displayed in\n * the calendar (like \"selected\" or \"disabled\"). Define custom modifiers using\n * the `modifiers` prop.\n *\n * ### Formatters and custom component\n *\n * You can customize how the content is displayed in the date picker by using\n * either the formatters or replacing the internal components.\n *\n * For the most common cases you want to use the `formatters` prop to change how\n * the content is formatted in the calendar. Use the `components` prop to\n * replace the internal components, like the navigation icons.\n *\n * ### Styling\n *\n * DayPicker comes with a default, basic style in `react-day-picker/style` – use\n * it as template for your own style.\n *\n * If you are using CSS modules, pass the imported styles object the\n * `classNames` props.\n *\n * You can also style the elements via inline styles using the `styles` prop.\n *\n * ### Form fields\n *\n * If you need to bind the date picker to a form field, you can use the\n * `useInput` hooks for a basic behavior. See the `useInput` source as an\n * example to bind the date picker with form fields.\n *\n * ### Localization\n *\n * To localize DayPicker, import the locale from `date-fns` package and use the\n * `locale` prop.\n *\n * For example, to use Spanish locale:\n *\n * ```\n * import { es } from 'date-fns/locale';\n * <DayPicker locale={es} />\n * ```\n */\nexport function DayPicker(\n  props:\n    | DayPickerDefaultProps\n    | DayPickerSingleProps\n    | DayPickerMultipleProps\n    | DayPickerRangeProps\n): JSX.Element {\n  return (\n    <RootProvider {...props}>\n      <Root initialProps={props} />\n    </RootProvider>\n  );\n}\n", "/** @private */\nexport function isValidDate(day: Date): boolean {\n  return !isNaN(day.getTime());\n}\n", "import {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  FocusEventHandler,\n  InputHTMLAttributes,\n  useState\n} from 'react';\n\nimport { differenceInCalendarDays, format as _format, parse } from 'date-fns';\nimport { enUS } from 'date-fns/locale';\n\nimport { parseFromToProps } from 'contexts/DayPicker/utils';\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport { DayPickerSingleProps } from 'types/DayPickerSingle';\nimport {\n  DayClickEventHandler,\n  MonthChangeEventHandler\n} from 'types/EventHandlers';\n\nimport { isValidDate } from './utils/isValidDate';\n\n/** The props to attach to the input field when using {@link useInput}. */\nexport type InputProps = Pick<\n  InputHTMLAttributes<HTMLInputElement>,\n  'onBlur' | 'onChange' | 'onFocus' | 'value' | 'placeholder'\n>;\n\n/** The props to attach to the DayPicker component when using {@link useInput}. */\nexport type InputDayPickerProps = Pick<\n  DayPickerSingleProps,\n  | 'fromDate'\n  | 'toDate'\n  | 'locale'\n  | 'month'\n  | 'onDayClick'\n  | 'onMonthChange'\n  | 'selected'\n  | 'today'\n>;\n\nexport interface UseInputOptions\n  extends Pick<\n    DayPickerBase,\n    | 'locale'\n    | 'fromDate'\n    | 'toDate'\n    | 'fromMonth'\n    | 'toMonth'\n    | 'fromYear'\n    | 'toYear'\n    | 'today'\n  > {\n  /** The initially selected date */\n  defaultSelected?: Date;\n  /**\n   * The format string for formatting the input field. See\n   * https://date-fns.org/docs/format for a list of format strings.\n   *\n   * @defaultValue PP\n   */\n  format?: string;\n  /** Make the selection required. */\n  required?: boolean;\n}\n\n/** Represent the value returned by {@link useInput}. */\nexport interface UseInputValue {\n  /** The props to pass to a DayPicker component. */\n  dayPickerProps: InputDayPickerProps;\n  /** The props to pass to an input field. */\n  inputProps: InputProps;\n  /** A function to reset to the initial state. */\n  reset: () => void;\n  /** A function to set the selected day. */\n  setSelected: (day: Date | undefined) => void;\n}\n\n/** Return props and setters for binding an input field to DayPicker. */\nexport function useInput(options: UseInputOptions = {}): UseInputValue {\n  const {\n    locale = enUS,\n    required,\n    format = 'PP',\n    defaultSelected,\n    today = new Date()\n  } = options;\n  const { fromDate, toDate } = parseFromToProps(options);\n\n  // Shortcut to the DateFns functions\n  const parseValue = (value: string) => parse(value, format, today, { locale });\n\n  // Initialize states\n  const [month, setMonth] = useState(defaultSelected ?? today);\n  const [selectedDay, setSelectedDay] = useState(defaultSelected);\n  const defaultInputValue = defaultSelected\n    ? _format(defaultSelected, format, { locale })\n    : '';\n  const [inputValue, setInputValue] = useState(defaultInputValue);\n\n  const reset = () => {\n    setSelectedDay(defaultSelected);\n    setMonth(defaultSelected ?? today);\n    setInputValue(defaultInputValue ?? '');\n  };\n\n  const setSelected = (date: Date | undefined) => {\n    setSelectedDay(date);\n    setMonth(date ?? today);\n    setInputValue(date ? _format(date, format, { locale }) : '');\n  };\n\n  const handleDayClick: DayClickEventHandler = (day, { selected }) => {\n    if (!required && selected) {\n      setSelectedDay(undefined);\n      setInputValue('');\n      return;\n    }\n    setSelectedDay(day);\n    setInputValue(day ? _format(day, format, { locale }) : '');\n  };\n\n  const handleMonthChange: MonthChangeEventHandler = (month) => {\n    setMonth(month);\n  };\n\n  // When changing the input field, save its value in state and check if the\n  // string is a valid date. If it is a valid day, set it as selected and update\n  // the calendar’s month.\n  const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {\n    setInputValue(e.target.value);\n    const day = parseValue(e.target.value);\n    const isBefore = fromDate && differenceInCalendarDays(fromDate, day) > 0;\n    const isAfter = toDate && differenceInCalendarDays(day, toDate) > 0;\n    if (!isValidDate(day) || isBefore || isAfter) {\n      setSelectedDay(undefined);\n      return;\n    }\n    setSelectedDay(day);\n    setMonth(day);\n  };\n\n  // Special case for _required_ fields: on blur, if the value of the input is not\n  // a valid date, reset the calendar and the input value.\n  const handleBlur: FocusEventHandler<HTMLInputElement> = (e) => {\n    const day = parseValue(e.target.value);\n    if (!isValidDate(day)) {\n      reset();\n    }\n  };\n\n  // When focusing, make sure DayPicker visualizes the month of the date in the\n  // input field.\n  const handleFocus: FocusEventHandler<HTMLInputElement> = (e) => {\n    if (!e.target.value) {\n      reset();\n      return;\n    }\n    const day = parseValue(e.target.value);\n    if (isValidDate(day)) {\n      setMonth(day);\n    }\n  };\n\n  const dayPickerProps: InputDayPickerProps = {\n    month: month,\n    onDayClick: handleDayClick,\n    onMonthChange: handleMonthChange,\n    selected: selectedDay,\n    locale,\n    fromDate,\n    toDate,\n    today\n  };\n\n  const inputProps: InputProps = {\n    onBlur: handleBlur,\n    onChange: handleChange,\n    onFocus: handleFocus,\n    value: inputValue,\n    placeholder: _format(new Date(), format, { locale })\n  };\n\n  return { dayPickerProps, inputProps, reset, setSelected };\n}\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\n\n/** The props for the {@link DayPicker} component when using `mode=\"default\"` or `undefined`. */\nexport interface DayPickerDefaultProps extends DayPickerBase {\n  mode?: undefined | 'default';\n}\n\n/** Returns true when the props are of type {@link DayPickerDefaultProps}. */\nexport function isDayPickerDefault(\n  props: DayPickerProps\n): props is DayPickerDefaultProps {\n  return props.mode === undefined || props.mode === 'default';\n}\n"], "names": ["_jsx", "_jsxs", "_Fragment", "format", "_format"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;8EAaA,GACA,oDAAA,GAiBO,IAAI,QAAQ,GAAG,WAAW;IAC7B,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;QAC7C,IAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YACjD,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACjB,IAAK,IAAI,CAAC,IAAI,CAAC,CAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzF,CAAS;QACD,OAAO,CAAC,CAAC;IACjB,EAAK;IACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC3C,EAAC;AAEM,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IACzB,IAAI,CAAC,GAAG,CAAA,CAAE,CAAC;IACX,IAAK,IAAI,CAAC,IAAI,CAAC,CAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAC/E,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU,EAC/D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACpE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAS;IACL,OAAO,CAAC,CAAC;AACb,CAAC;AAiKM,SAAS,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;IAC1C,IAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;QACjF,IAAI,EAAE,IAAI,CAAA,CAAE,CAAC,IAAI,IAAI,CAAC,EAAE;YACpB,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAS;IACT,CAAK;IACD,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7D,CAAC;AA8FsB,OAAO,eAAe,KAAK,UAAU,GAAG,eAAe,GAAG,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE;IACnH,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAC,CAAC,IAAI,GAAG,iBAAiB,EAAE,CAAC,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,UAAU,GAAG,UAAU,EAAE,CAAC,CAAC;AACrF;AC1SA,4EAAA,GACM,SAAU,mBAAmB,CACjC,KAA6C,EAAA;IAE7C,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACnC;ACJA,yEAAA,GACM,SAAU,gBAAgB,CAC9B,KAA6C,EAAA;IAE7C,OAAO,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC;AAChC;ACRA,0EAAA,GACM,SAAU,iBAAiB,CAC/B,KAA6C,EAAA;IAE7C,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC;AACjC;ACrBA;;CAEG,GACI,IAAM,iBAAiB,GAAyB;IACrD,IAAI,EAAE,KAAK;IACX,eAAe,EAAE,qBAAqB;IACtC,eAAe,EAAE,qBAAqB;IACtC,OAAO,EAAE,aAAa;IACtB,YAAY,EAAE,kBAAkB;IAChC,MAAM,EAAE,YAAY;IAEpB,OAAO,EAAE,aAAa;IAEtB,aAAa,EAAE,mBAAmB;IAClC,WAAW,EAAE,iBAAiB;IAC9B,eAAe,EAAE,qBAAqB;IACtC,aAAa,EAAE,mBAAmB;IAElC,iBAAiB,EAAE,uBAAuB;IAE1C,QAAQ,EAAE,cAAc;IACxB,cAAc,EAAE,oBAAoB;IACpC,aAAa,EAAE,mBAAmB;IAClC,aAAa,EAAE,mBAAmB;IAElC,MAAM,EAAE,YAAY;IACpB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,WAAW;IAElB,IAAI,EAAE,UAAU;IAChB,QAAQ,EAAE,cAAc;IACxB,SAAS,EAAE,eAAe;IAE1B,GAAG,EAAE,SAAS;IACd,UAAU,EAAE,gBAAgB;IAC5B,mBAAmB,EAAE,yBAAyB;IAC9C,eAAe,EAAE,qBAAqB;IAEtC,QAAQ,EAAE,cAAc;IAExB,GAAG,EAAE,SAAS;IACd,UAAU,EAAE,gBAAgB;IAC5B,IAAI,EAAE,UAAU;IAEhB,GAAG,EAAE,SAAS;IACd,SAAS,EAAE,eAAe;IAC1B,WAAW,EAAE,iBAAiB;IAC9B,YAAY,EAAE,kBAAkB;IAChC,YAAY,EAAE,kBAAkB;IAChC,UAAU,EAAE,gBAAgB;IAC5B,eAAe,EAAE,qBAAqB;IACtC,aAAa,EAAE,mBAAmB;IAClC,gBAAgB,EAAE,sBAAsB;CACzC;ACvDD;;CAEG,GACa,SAAA,aAAa,CAC3B,KAAW,EACX,OAA6B,EAAA;IAE7B,kKAAO,SAAM,AAAN,EAAO,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1C;ACRA;;CAEG,GACa,SAAA,SAAS,CAAC,GAAS,EAAE,OAA6B,EAAA;IAChE,kKAAO,SAAA,AAAM,EAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AACnC;ACLA;;CAEG,GACa,SAAA,kBAAkB,CAChC,KAAW,EACX,OAA6B,EAAA;IAE7B,kKAAO,SAAA,AAAM,EAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACxC;ACVA;;CAEG,GACG,SAAU,gBAAgB,CAAC,UAAkB,EAAA;IACjD,OAAO,EAAA,CAAA,MAAA,CAAG,UAAU,CAAE,CAAC;AACzB;ACHA;;CAEG,GACa,SAAA,iBAAiB,CAC/B,OAAa,EACb,OAA6B,EAAA;IAE7B,kKAAO,SAAA,AAAM,EAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC5C;ACRA;;CAEG,GACa,SAAA,iBAAiB,CAC/B,IAAU,EACV,OAEC,EAAA;IAED,kKAAO,SAAA,AAAM,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACvC;;;;;;;;;;ACRA;;CAEG,GACI,IAAM,QAAQ,GAAa,SAAC,GAAG,EAAE,eAAe,EAAE,OAAO,EAAA;IAC9D,kKAAO,SAAA,AAAM,EAAC,GAAG,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;ACTD;;CAEG,GACI,IAAM,kBAAkB,GAAG,YAAA;IAChC,OAAO,SAAS,CAAC;AACnB,CAAC;ACHD;;CAEG,GACI,IAAM,SAAS,GAAmB,YAAA;IACvC,OAAO,kBAAkB,CAAC;AAC5B,CAAC;ACLD;;CAEG,GACI,IAAM,aAAa,GAAmB,YAAA;IAC3C,OAAO,sBAAsB,CAAC;AAChC,CAAC;ACHD;;CAEG,GACI,IAAM,YAAY,GAAiB,SAAC,GAAG,EAAE,OAAO,EAAA;IACrD,kKAAO,SAAA,AAAM,EAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACtC,CAAC;ACPD;;CAEG,GACI,IAAM,eAAe,GAAoB,SAAC,CAAC,EAAA;IAChD,OAAO,UAAA,CAAA,MAAA,CAAW,CAAC,CAAE,CAAC;AACxB,CAAC;ACPD;;CAEG,GACI,IAAM,iBAAiB,GAAG,YAAA;IAC/B,OAAO,QAAQ,CAAC;AAClB,CAAC;;;;;;;;;;;ACqBD;;;CAGG,YACa,uBAAuB,GAAA;IACrC,IAAM,aAAa,GAAkB,SAAS,CAAC;IAC/C,IAAM,UAAU,GAAG,iBAAiB,CAAC;IACrC,IAAM,MAAM,qJAAG,QAAI,CAAC;IACpB,IAAM,mBAAmB,GAAG,CAAA,CAAE,CAAC;IAC/B,IAAM,SAAS,GAAG,CAAA,CAAE,CAAC;IACrB,IAAM,cAAc,GAAG,CAAC,CAAC;IACzB,IAAM,MAAM,GAAG,CAAA,CAAE,CAAC;IAClB,IAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;IAEzB,OAAO;QACL,aAAa,EAAA,aAAA;QACb,UAAU,EAAA,UAAA;QACV,UAAU,EAAA,UAAA;QACV,MAAM,EAAA,MAAA;QACN,MAAM,EAAA,MAAA;QACN,mBAAmB,EAAA,mBAAA;QACnB,SAAS,EAAA,SAAA;QACT,cAAc,EAAA,cAAA;QACd,MAAM,EAAA,MAAA;QACN,KAAK,EAAA,KAAA;QACL,IAAI,EAAE,SAAS;KAChB,CAAC;AACJ;ACjDA,uFAAA,GACM,SAAU,gBAAgB,CAC9B,KAGC,EAAA;IAEO,IAAA,QAAQ,GAAiC,KAAK,CAAA,QAAtC,EAAE,MAAM,GAAyB,KAAK,CAAA,MAA9B,EAAE,SAAS,GAAc,KAAK,CAAnB,SAAA,EAAE,OAAO,GAAK,KAAK,CAAA,OAAV,CAAW;IACjD,IAAA,QAAQ,GAAa,KAAK,CAAA,QAAlB,EAAE,MAAM,GAAK,KAAK,CAAA,MAAV,CAAW;IAEjC,IAAI,SAAS,EAAE;QACb,QAAQ,oJAAG,eAAA,AAAY,EAAC,SAAS,CAAC,CAAC;KACpC,MAAM,IAAI,QAAQ,EAAE;QACnB,QAAQ,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACrC;IACD,IAAI,OAAO,EAAE;QACX,MAAM,IAAG,2JAAA,AAAU,EAAC,OAAO,CAAC,CAAC;KAC9B,MAAM,IAAI,MAAM,EAAE;QACjB,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KACnC;IAED,OAAO;QACL,QAAQ,EAAE,QAAQ,iJAAG,cAAA,AAAU,EAAC,QAAQ,CAAC,GAAG,SAAS;QACrD,MAAM,EAAE,MAAM,kJAAG,aAAU,AAAV,EAAW,MAAM,CAAC,GAAG,SAAS;KAChD,CAAC;AACJ;ACoBA;;;;;;CAMG,OACU,gBAAgB,4MAAG,iBAAA,AAAa,EAE3C,SAAS,EAAE;AAQb;;;CAGG,GACG,SAAU,iBAAiB,CAAC,KAA6B,EAAA;;IACrD,IAAA,YAAY,GAAK,KAAK,CAAA,YAAV,CAAW;IAE/B,IAAM,oBAAoB,GAAG,uBAAuB,EAAE,CAAC;IAEjD,IAAA,EAAA,GAAuB,gBAAgB,CAAC,YAAY,CAAC,EAAnD,QAAQ,GAAA,EAAA,CAAA,QAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAmC,CAAC;IAE5D,IAAI,aAAa,GACf,CAAA,EAAA,GAAA,YAAY,CAAC,aAAa,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,oBAAoB,CAAC,aAAa,CAAC;IACnE,IAAI,aAAa,KAAK,SAAS,IAAA,CAAK,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE;;QAEzD,aAAa,GAAG,SAAS,CAAC;KAC3B;IAED,IAAI,QAAQ,CAAC;IACb,IACE,iBAAiB,CAAC,YAAY,CAAC,IAC/B,mBAAmB,CAAC,YAAY,CAAC,IACjC,gBAAgB,CAAC,YAAY,CAAC,EAC9B;QACA,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;KAClC;IAED,IAAM,KAAK,GAAA,SAAA,SAAA,SAAA,CAAA,GACN,oBAAoB,CAAA,EACpB,YAAY,CACf,EAAA;QAAA,aAAa,EAAA,aAAA;QACb,UAAU,EAAA,SAAA,SAAA,CAAA,GACL,oBAAoB,CAAC,UAAU,CAC/B,EAAA,YAAY,CAAC,UAAU,CAAA;QAE5B,UAAU,EACL,QAAA,CAAA,CAAA,CAAA,EAAA,YAAY,CAAC,UAAU,CAAA;QAE5B,UAAU,EACL,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,oBAAoB,CAAC,UAAU,CAAA,EAC/B,YAAY,CAAC,UAAU;QAE5B,QAAQ,EAAA,QAAA;QACR,MAAM,EAAA,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EACD,oBAAoB,CAAC,MAAM,CAC3B,EAAA,YAAY,CAAC,MAAM;QAExB,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,oBAAoB,CAAC,IAAI;QACpD,SAAS,EAAA,SAAA,SAAA,CAAA,GACJ,oBAAoB,CAAC,SAAS,CAC9B,EAAA,YAAY,CAAC,SAAS,CAAA;QAE3B,mBAAmB,EACd,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,oBAAoB,CAAC,mBAAmB,CAAA,EACxC,YAAY,CAAC,mBAAmB;QAErC,QAAQ,EAAA,QAAA;QACR,MAAM,EAAA,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EACD,oBAAoB,CAAC,MAAM,GAC3B,YAAY,CAAC,MAAM,CAExB;QAAA,MAAM,EAAA,MAAA;IAAA,CAAA,CACP,CAAC;IAEF,mOACEA,MAAAA,AAAA,EAAC,gBAAgB,CAAC,QAAQ,EAAC;QAAA,KAAK,EAAE,KAAK;QAAA,UACpC,KAAK,CAAC,QAAQ;IAAA,CAAA,CACW,EAC5B;AACJ,CAAC;AAED;;;;;CAKG,YACa,YAAY,GAAA;IAC1B,IAAM,OAAO,6MAAG,aAAA,AAAU,EAAC,gBAAgB,CAAC,CAAC;IAC7C,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;KAC1E;IACD,OAAO,OAAO,CAAC;AACjB;ACzIA,uGAAA,GACM,SAAU,YAAY,CAAC,KAAwB,EAAA;IAC7C,IAAA,EAKF,GAAA,YAAY,EAAE,EAJhB,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,MAAM,GAAA,EAAA,CAAA,MAAA,EACQ,aAAa,GAAA,GAAA,UAAA,CAAA,aACX,CAAC;IACnB,OACEA,kOAAAA,AACE,EAAA,KAAA,EAAA;QAAA,SAAS,EAAE,UAAU,CAAC,aAAa;QACnC,KAAK,EAAE,MAAM,CAAC,aAAa;QAAA,aACjB,QAAQ;QAClB,IAAI,EAAC,cAAc;QACnB,EAAE,EAAE,KAAK,CAAC,EAAE;QAEX,QAAA,EAAA,aAAa,CAAC,KAAK,CAAC,YAAY,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC;IAAA,CAAA,CAC1C,EACN;AACJ;AC7BA;;CAEG,GACG,SAAU,YAAY,CAAC,KAAsB,EAAA;IACjD,mOACEA,MAAAA,AAAA,EAAA,KAAA,EAAA,QAAA,CAAA;QACE,KAAK,EAAC,KAAK;QACX,MAAM,EAAC,KAAK;QACZ,OAAO,EAAC,aAAa;QAAA,eACT,cAAc;IAAA,CAAA,EACtB,KAAK,EAAA;QAAA,QAAA,8NAETA,MAAAA,AACE,EAAA,MAAA,EAAA;YAAA,CAAC,EAAC,yhBAAyhB;YAC3hB,IAAI,EAAC,cAAc;YACnB,QAAQ,EAAC,SAAS;QAAA,CACZ,CAAA;IAAA,CAAA,CAAA,CACJ,EACN;AACJ;ACIA;;;CAGG,GACG,SAAU,QAAQ,CAAC,KAAoB,EAAA;;IACnC,IAAA,QAAQ,GAAiD,KAAK,CAAA,QAAtD,EAAE,KAAK,GAA0C,KAAK,CAA/C,KAAA,EAAE,QAAQ,GAAgC,KAAK,CAAA,QAArC,EAAE,OAAO,GAAuB,KAAK,CAA5B,OAAA,EAAE,SAAS,GAAY,KAAK,CAAA,SAAjB,EAAE,KAAK,GAAK,KAAK,CAAA,KAAV,CAAW;IACvE,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IAEjC,IAAM,qBAAqB,GACzB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,SAAS,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,YAAY,CAAC;IACrD,mOACEC,OAAAA,EAAAA,OAAAA;QAAK,SAAS,EAAE,SAAS;QAAE,KAAK,EAAE,KAAK;QACrC,QAAA,EAAA;wOAAAD,MAAAA,AAAA,EAAA,MAAA,EAAA;gBAAM,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,OAAO;gBAAA,UAC1C,KAAK,CAAC,YAAY,CAAC;YAAA,CACf,CAAA;wOACPA,MAAAA,EAAAA,UAAAA;gBACE,IAAI,EAAE,KAAK,CAAC,IAAI;gBAAA,YAAA,EACJ,KAAK,CAAC,YAAY,CAAC;gBAC/B,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,QAAQ;gBACxC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ;gBAChC,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,QAAQ;gBAAA,UAEjB,QAAQ;YAAA,CAAA,CACF;wOACTC,OAAAA,AAAA,EAAA,KAAA,EAAA;gBACE,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,aAAa;gBAC7C,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,aAAa;gBAAA,aAAA,EACzB,MAAM;gBAEjB,QAAA,EAAA;oBAAA,OAAO;oBAEND,kOAAAA,AAAA,EAAC,qBAAqB,EAAA;wBACpB,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,aAAa;wBAC7C,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,aAAa;oBAAA,CACrC,CAAA;iBAAA;YAAA,CAAA,CAEA;SACF;IAAA,CAAA,CAAA,EACN;AACJ;AClDA,oDAAA,GACM,SAAU,cAAc,CAAC,KAA0B,EAAA;;IACjD,IAAA,EAAA,GASF,YAAY,EAAE,EARhB,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,MAAM,GAAA,EAAA,CAAA,MAAA,EACQ,kBAAkB,GAAA,EAAA,CAAA,UAAA,CAAA,kBAAA,EAChC,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,UAAU,GAAA,EAAA,CAAA,UAAA,EACA,kBAAkB,GAAA,EAAA,CAAA,MAAA,CAAA,kBACZ,CAAC;;IAGnB,IAAI,CAAC,QAAQ,EAAE,mOAAOA,MAAAA,EAAAA,uNAAAA,CAAAA,WAAAA,EAAAA,CAAAA,EAAK,CAAC;IAC5B,IAAI,CAAC,MAAM,EAAE,kOAAOA,OAAAA,EAAAA,uNAAAA,CAAAA,WAAAA,EAAAA,CAAAA,EAAK,CAAC;IAE1B,IAAM,cAAc,GAAW,EAAE,CAAC;IAElC,mJAAI,aAAA,AAAU,EAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;;QAEhC,IAAM,IAAI,IAAG,+JAAA,AAAY,EAAC,QAAQ,CAAC,CAAC;QACpC,IAAK,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAE;YACzE,cAAc,CAAC,IAAI,CAAC,wJAAQ,AAAR,EAAS,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;SAC5C;KACF,MAAM;;QAEL,IAAM,IAAI,oJAAG,eAAY,AAAZ,EAAa,IAAI,IAAI,EAAE,CAAC,CAAC,CAAA,kDAAA;QACtC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE,KAAK,EAAE,CAAE;YACxC,cAAc,CAAC,IAAI,8IAAC,WAAQ,AAAR,EAAS,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;SAC5C;KACF;IAED,IAAM,YAAY,GAA0C,SAAC,CAAC,EAAA;QAC5D,IAAM,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAM,QAAQ,GAAG,wJAAQ,AAAR,mJAAS,eAAY,AAAZ,EAAa,KAAK,CAAC,YAAY,CAAC,EAAE,aAAa,CAAC,CAAC;QAC3E,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAM,iBAAiB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAQ,CAAC;IAE3D,mOACEA,MAAAA,AAAC,EAAA,iBAAiB,EAAA;QAChB,IAAI,EAAC,QAAQ;QACD,YAAA,EAAA,kBAAkB,EAAE;QAChC,SAAS,EAAE,UAAU,CAAC,cAAc;QACpC,KAAK,EAAE,MAAM,CAAC,cAAc;QAC5B,QAAQ,EAAE,YAAY;QACtB,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE;QACpC,OAAO,EAAE,kBAAkB,CAAC,KAAK,CAAC,YAAY,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC;QAAA,UAE1D,cAAc,CAAC,GAAG,CAAC,SAAC,CAAC,EAAK;YAAA,QACzBA,iOAAAA,AAA2B,EAAA,QAAA,EAAA;gBAAA,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE;gBAAA,QAAA,EAC3C,kBAAkB,CAAC,CAAC,EAAE;oBAAE,MAAM,EAAA,MAAA;gBAAA,CAAE,CAAC;YAAA,CADvB,EAAA,CAAC,CAAC,QAAQ,EAAE,CAEhB,EACV;QAAA,CAAA,CAAC;IAAA,CAAA,CACgB,EACpB;AACJ;ACvDA;;;CAGG,GACG,SAAU,aAAa,CAAC,KAAyB,EAAA;;IAC7C,IAAA,YAAY,GAAK,KAAK,CAAA,YAAV,CAAW;IACzB,IAAA,EAAA,GASF,YAAY,EAAE,EARhB,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,UAAU,GAAA,EAAA,CAAA,UAAA,EACI,iBAAiB,GAAA,EAAA,CAAA,UAAA,CAAA,iBAAA,EACrB,iBAAiB,GAAA,EAAA,CAAA,MAAA,CAAA,iBACX,CAAC;IAEnB,IAAM,KAAK,GAAW,EAAE,CAAC;;IAGzB,IAAI,CAAC,QAAQ,EAAE,mOAAOA,MAAAA,EAAAA,uNAAAA,CAAAA,WAAAA,EAAAA,CAAAA,EAAK,CAAC;IAC5B,IAAI,CAAC,MAAM,EAAE,mOAAOA,MAAAA,EAAAA,uNAAAA,CAAAA,WAAAA,EAAAA,CAAAA,EAAK,CAAC;IAE1B,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IACxC,IAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;IACpC,IAAK,IAAI,IAAI,GAAG,QAAQ,EAAE,IAAI,IAAI,MAAM,EAAE,IAAI,EAAE,CAAE;QAChD,KAAK,CAAC,IAAI,CAAC,sJAAA,AAAO,kJAAC,cAAA,AAAW,EAAC,IAAI,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KACpD;IAED,IAAM,YAAY,GAA0C,SAAC,CAAC,EAAA;QAC5D,IAAM,QAAQ,+IAAG,UAAA,AAAO,mJACtB,eAAA,AAAY,EAAC,YAAY,CAAC,EAC1B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACvB,CAAC;QACF,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAM,iBAAiB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAQ,CAAC;IAE3D,mOACEA,MAAC,AAADA,EAAC,iBAAiB,EAAA;QAChB,IAAI,EAAC,OAAO;QACA,YAAA,EAAA,iBAAiB,EAAE;QAC/B,SAAS,EAAE,UAAU,CAAC,aAAa;QACnC,KAAK,EAAE,MAAM,CAAC,aAAa;QAC3B,QAAQ,EAAE,YAAY;QACtB,KAAK,EAAE,YAAY,CAAC,WAAW,EAAE;QACjC,OAAO,EAAE,iBAAiB,CAAC,YAAY,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC;QAEnD,QAAA,EAAA,KAAK,CAAC,GAAG,CAAC,SAAC,IAAI,EAAA;YAAK,mOACnBA,MAAAA,EAAAA,UAAAA;gBAAiC,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE;gBACvD,QAAA,EAAA,iBAAiB,CAAC,IAAI,EAAE;oBAAE,MAAM,EAAA,MAAA;gBAAA,CAAE,CAAC;YAAA,GADzB,IAAI,CAAC,WAAW,EAAE,CAEtB,EACV;QAAA,CAAA,CAAC;IAAA,CAAA,CACgB,EACpB;AACJ;ACtEA;;;;;;;;CAQG,GACa,SAAA,kBAAkB,CAChC,YAAe,EACf,eAA8B,EAAA;IAExB,IAAA,EAAA,OAAgC,iNAAA,AAAQ,EAAC,YAAY,CAAC,EAArD,iBAAiB,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,CAAA,CAA0B,CAAC;IAE7D,IAAM,KAAK,GACT,eAAe,KAAK,SAAS,GAAG,iBAAiB,GAAG,eAAe,CAAC;IAEtE,OAAO;QAAC,KAAK;QAAE,QAAQ;KAAgC,CAAC;AAC1D;ACnBA,6DAAA,GACM,SAAU,eAAe,CAAC,OAAuC,EAAA;IAC7D,IAAA,KAAK,GAA0B,OAAO,CAAA,KAAjC,EAAE,YAAY,GAAY,OAAO,CAAA,YAAnB,EAAE,KAAK,GAAK,OAAO,CAAA,KAAZ,CAAa;IAC/C,IAAI,YAAY,GAAG,KAAK,IAAI,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC;IAExD,IAAA,MAAM,GAAmC,OAAO,CAAA,MAA1C,EAAE,QAAQ,GAAyB,OAAO,CAAA,QAAhC,EAAE,EAAA,GAAuB,OAAO,CAAZ,cAAA,EAAlB,cAAc,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,CAAC,GAAA,EAAA,CAAa;;IAGzD,IAAI,MAAM,mKAAI,6BAAA,AAA0B,EAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE;QAClE,IAAM,MAAM,GAAG,CAAC,CAAC,GAAA,CAAI,cAAc,GAAG,CAAC,CAAC,CAAC;QACzC,YAAY,iJAAG,YAAA,AAAS,EAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC1C;;IAED,IAAI,QAAQ,mKAAI,6BAAA,AAA0B,EAAC,YAAY,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtE,YAAY,GAAG,QAAQ,CAAC;KACzB;IACD,OAAO,gKAAA,AAAY,EAAC,YAAY,CAAC,CAAC;AACpC;ACPA,mCAAA,YACgB,kBAAkB,GAAA;IAChC,IAAM,OAAO,GAAG,YAAY,EAAE,CAAC;IAC/B,IAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;IACxC,IAAA,EAAoB,GAAA,kBAAkB,CAAC,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,EAAlE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,EAAmD,CAAC;IAE1E,IAAM,SAAS,GAAG,SAAC,IAAU,EAAA;;QAC3B,IAAI,OAAO,CAAC,iBAAiB,EAAE,OAAO;QACtC,IAAM,KAAK,oJAAG,eAAA,AAAY,EAAC,IAAI,CAAC,CAAC;QACjC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,CAAA,EAAA,GAAA,OAAO,CAAC,aAAa,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,OAAA,EAAA,KAAK,CAAC,CAAC;IACjC,CAAC,CAAC;IAEF,OAAO;QAAC,KAAK;QAAE,SAAS;KAAC,CAAC;AAC5B;AC1BA;;;CAGG,GACa,SAAA,gBAAgB,CAC9B,KAAW,EACX,EAMC,EAAA;QALC,aAAa,GAAA,EAAA,CAAA,aAAA,EACb,cAAc,GAAA,EAAA,CAAA,cAAA,CAAA;IAMhB,IAAM,KAAK,oJAAG,eAAA,AAAY,EAAC,KAAK,CAAC,CAAC;IAClC,IAAM,GAAG,oJAAG,eAAA,AAAY,gJAAC,YAAA,AAAS,EAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;IAC3D,IAAM,UAAU,kKAAG,6BAAA,AAA0B,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC1D,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,CAAE;QACnC,IAAM,SAAS,GAAG,0JAAS,AAAT,EAAU,KAAK,EAAE,CAAC,CAAC,CAAC;QACtC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACxB;IAED,IAAI,aAAa,EAAE,MAAM,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAC7C,OAAO,MAAM,CAAC;AAChB;AC1BA;;;;;;;;;CASG,GACa,SAAA,YAAY,CAC1B,aAAmB,EACnB,OAOC,EAAA;IAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;QAC7B,OAAO,SAAS,CAAC;KAClB;IACO,IAAA,MAAM,GAA0C,OAAO,CAAA,MAAjD,EAAE,eAAe,GAAyB,OAAO,CAAA,eAAhC,EAAE,EAAA,GAAuB,OAAO,CAAZ,cAAA,EAAlB,cAAc,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,CAAC,GAAA,EAAA,CAAa;IAChE,IAAM,MAAM,GAAG,eAAe,GAAG,cAAc,GAAG,CAAC,CAAC;IACpD,IAAM,KAAK,oJAAG,eAAA,AAAY,EAAC,aAAa,CAAC,CAAC;IAE1C,IAAI,CAAC,MAAM,EAAE;QACX,qJAAO,YAAS,AAAT,EAAU,KAAK,EAAE,MAAM,CAAC,CAAC;KACjC;IAED,IAAM,UAAU,kKAAG,6BAAA,AAA0B,EAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAErE,IAAI,UAAU,GAAG,cAAc,EAAE;QAC/B,OAAO,SAAS,CAAC;KAClB;;IAGD,qJAAO,YAAA,AAAS,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAClC;ACxCA;;;;;;;;;;CAUG,GACa,SAAA,gBAAgB,CAC9B,aAAmB,EACnB,OAOC,EAAA;IAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;QAC7B,OAAO,SAAS,CAAC;KAClB;IACO,IAAA,QAAQ,GAA0C,OAAO,CAAA,QAAjD,EAAE,eAAe,GAAyB,OAAO,CAAA,eAAhC,EAAE,EAAA,GAAuB,OAAO,CAAZ,cAAA,EAAlB,cAAc,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,CAAC,GAAA,EAAA,CAAa;IAClE,IAAM,MAAM,GAAG,eAAe,GAAG,cAAc,GAAG,CAAC,CAAC;IACpD,IAAM,KAAK,oJAAG,eAAA,AAAY,EAAC,aAAa,CAAC,CAAC;IAC1C,IAAI,CAAC,QAAQ,EAAE;QACb,qJAAO,YAAA,AAAS,EAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;KAClC;IACD,IAAM,UAAU,OAAG,wLAAA,AAA0B,EAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAE/D,IAAI,UAAU,IAAI,CAAC,EAAE;QACnB,OAAO,SAAS,CAAC;KAClB;;IAGD,qJAAO,YAAS,AAAT,EAAU,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;AACnC;ACbA;;;CAGG,OACU,iBAAiB,6MAAG,gBAAA,AAAa,EAE5C,SAAS,EAAE;AAEb,2DAAA,GACM,SAAU,kBAAkB,CAAC,KAElC,EAAA;IACC,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IAC3B,IAAA,EAAA,GAA4B,kBAAkB,EAAE,EAA/C,YAAY,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,SAAS,GAAA,EAAA,CAAA,CAAA,CAAwB,CAAC;IAEvD,IAAM,aAAa,GAAG,gBAAgB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IAChE,IAAM,SAAS,GAAG,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACxD,IAAM,aAAa,GAAG,gBAAgB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IAEhE,IAAM,eAAe,GAAG,SAAC,IAAU,EAAA;QACjC,OAAO,aAAa,CAAC,IAAI,CAAC,SAAC,YAAY,EAAA;YACrC,uJAAA,cAAA,AAAW,EAAC,IAAI,EAAE,YAAY,CAAC,CAAA;QAA/B,CAA+B,CAChC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAM,QAAQ,GAAG,SAAC,IAAU,EAAE,OAAc,EAAA;QAC1C,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE;YACzB,OAAO;SACR;QAED,IAAI,OAAO,QAAI,oJAAA,AAAQ,EAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACtC,SAAS,+IAAC,YAAA,AAAS,EAAC,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/D,MAAM;YACL,SAAS,CAAC,IAAI,CAAC,CAAC;SACjB;IACH,CAAC,CAAC;IAEF,IAAM,KAAK,GAA2B;QACpC,YAAY,EAAA,YAAA;QACZ,aAAa,EAAA,aAAA;QACb,SAAS,EAAA,SAAA;QACT,QAAQ,EAAA,QAAA;QACR,aAAa,EAAA,aAAA;QACb,SAAS,EAAA,SAAA;QACT,eAAe,EAAA,eAAA;KAChB,CAAC;IAEF,QACEA,iOAAA,AAAAA,EAAC,iBAAiB,CAAC,QAAQ,EAAC;QAAA,KAAK,EAAE,KAAK;QAAA,UACrC,KAAK,CAAC,QAAQ;IAAA,CAAA,CACY,EAC7B;AACJ,CAAC;AAED;;;;;CAKG,YACa,aAAa,GAAA;IAC3B,IAAM,OAAO,6MAAG,aAAA,AAAU,EAAC,iBAAiB,CAAC,CAAC;IAC9C,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;KAC3E;IACD,OAAO,OAAO,CAAC;AACjB;ACpFA;;CAEG,GACG,SAAU,gBAAgB,CAAC,KAAmB,EAAA;;IAC5C,IAAA,EAAqC,GAAA,YAAY,EAAE,EAAjD,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,UAAU,GAAA,EAAA,CAAA,UAAmB,CAAC;IAClD,IAAA,SAAS,GAAK,aAAa,EAAE,CAAA,SAApB,CAAqB;IAEtC,IAAM,iBAAiB,GAA4B,SAAC,QAAQ,EAAA;QAC1D,SAAS,+IACP,YAAA,AAAS,EAAC,QAAQ,EAAE,KAAK,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAClE,CAAC;IACJ,CAAC,CAAC;IACF,IAAM,qBAAqB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,YAAY,CAAC;IACvE,IAAM,YAAY,+NAChBA,MAAAA,EAAC,qBAAqB,EAAA;QAAC,EAAE,EAAE,KAAK,CAAC,EAAE;QAAE,YAAY,EAAE,KAAK,CAAC,YAAY;IAAA,CAAA,CAAI,CAC1E,CAAC;IACF,mOACEC,OAAAA,AACE,EAAA,KAAA,EAAA;QAAA,SAAS,EAAE,UAAU,CAAC,iBAAiB;QACvC,KAAK,EAAE,MAAM,CAAC,iBAAiB;QAG/B,QAAA,EAAA;wOAAAD,MAAAA,AAAA,EAAA,KAAA,EAAA;gBAAK,SAAS,EAAE,UAAU,CAAC,OAAO;gBAAG,QAAA,EAAA,YAAY;YAAA,CAAO,CAAA;wOACxDA,MAAAA,AAAC,EAAA,cAAc,EAAA;gBACb,QAAQ,EAAE,iBAAiB;gBAC3B,YAAY,EAAE,KAAK,CAAC,YAAY;YAAA,CAChC,CAAA;wOACFA,MAAAA,AAAC,EAAA,aAAa,EAAA;gBACZ,QAAQ,EAAE,iBAAiB;gBAC3B,YAAY,EAAE,KAAK,CAAC,YAAY;YAAA,CAAA,CAChC;SACE;IAAA,CAAA,CAAA,EACN;AACJ;ACzCA;;CAEG,GACG,SAAU,QAAQ,CAAC,KAAsB,EAAA;IAC7C,mOACEA,MAAAA,AAAA,EAAA,KAAA,EAAA,QAAA,CAAA;QAAK,KAAK,EAAC,MAAM;QAAC,MAAM,EAAC,MAAM;QAAC,OAAO,EAAC,aAAa;IAAA,CAAK,EAAA,KAAK,EAC7D;QAAA,QAAA,8NAAAA,MAAAA,AAAA,EAAA,MAAA,EAAA;YACE,CAAC,EAAC,ihBAAihB;YACnhB,IAAI,EAAC,cAAc;YACnB,QAAQ,EAAC,SAAS;QAAA,CACZ,CAAA;IAAA,CAAA,CAAA,CACJ,EACN;AACJ;ACbA;;CAEG,GACG,SAAU,SAAS,CAAC,KAAsB,EAAA;IAC9C,mOACEA,MAAAA,AAAK,EAAA,KAAA,EAAA,QAAA,CAAA;QAAA,KAAK,EAAC,MAAM;QAAC,MAAM,EAAC,MAAM;QAAC,OAAO,EAAC,aAAa;IAAA,CAAK,EAAA,KAAK,EAC7D;QAAA,QAAA,8NAAAA,MAAA,AAAAA,EAAA,MAAA,EAAA;YACE,CAAC,EAAC,ohBAAohB;YACthB,IAAI,EAAC,cAAc;QAAA,CAAA,CACb;IAAA,CACJ,CAAA,CAAA,EACN;AACJ;ACPA,gEAAA,OACa,MAAM,6MAAG,aAAA,AAAU,EAC9B,SAAC,KAAK,EAAE,GAAG,EAAA;IACH,IAAA,EAAA,GAAyB,YAAY,EAAE,EAArC,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAmB,CAAC;IAE9C,IAAM,aAAa,GAAG;QAAC,UAAU,CAAC,YAAY;QAAE,UAAU,CAAC,MAAM;KAAC,CAAC;IACnE,IAAI,KAAK,CAAC,SAAS,EAAE;QACnB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KACrC;IACD,IAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAM,KAAK,GAAQ,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,MAAM,CAAC,YAAY,GAAK,MAAM,CAAC,MAAM,CAAE,CAAC;IAC3D,IAAI,KAAK,CAAC,KAAK,EAAE;QACf,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;KACnC;IAED,mOACEA,MAAAA,EAAAA,UAAAA,SAAAA,CAAAA,GACM,KAAK,EAAA;QACT,GAAG,EAAE,GAAG;QACR,IAAI,EAAC,QAAQ;QACb,SAAS,EAAE,SAAS;QACpB,KAAK,EAAE,KAAK;IAAA,CACZ,CAAA,CAAA,EACF;AACJ,CAAC;ACNH,oEAAA,GACM,SAAU,UAAU,CAAC,KAAsB,EAAA;;IACzC,IAAA,EAAA,GAOF,YAAY,EAAE,EANhB,GAAG,GAAA,EAAA,CAAA,GAAA,EACH,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,EAAoC,GAAA,EAAA,CAAA,MAAA,EAA1B,aAAa,GAAA,EAAA,CAAA,aAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAA,EAClC,UAAU,GAAA,EAAA,CAAA,UACM,CAAC;IAEnB,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;QAC5C,mOAAOA,MAAAA,EAAAA,uNAAAA,CAAAA,WAAAA,EAAAA,CAAAA,EAAK,CAAC;KACd;IAED,IAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,aAAa,EAAE;QAAE,MAAM,EAAA,MAAA;IAAA,CAAE,CAAC,CAAC;IACrE,IAAM,iBAAiB,GAAG;QACxB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,mBAAmB;KAC/B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,IAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE;QAAE,MAAM,EAAA,MAAA;IAAA,CAAE,CAAC,CAAC;IACzD,IAAM,aAAa,GAAG;QACpB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,eAAe;KAC3B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,IAAM,kBAAkB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,SAAS,CAAC;IAC9D,IAAM,iBAAiB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAQ,CAAC;IAC3D,OACEC,mOAAAA,AAAK,EAAA,KAAA,EAAA;QAAA,SAAS,EAAE,UAAU,CAAC,GAAG;QAAE,KAAK,EAAE,MAAM,CAAC,GAAG;QAC9C,QAAA,EAAA;YAAA,CAAC,KAAK,CAAC,YAAY,IAClBD,kOAAAA,AAAA,EAAC,MAAM,EAAA;gBACL,IAAI,EAAC,gBAAgB;gBAAA,cACT,aAAa;gBACzB,SAAS,EAAE,iBAAiB;gBAC5B,KAAK,EAAE,MAAM,CAAC,mBAAmB;gBACjC,QAAQ,EAAE,CAAC,KAAK,CAAC,aAAa;gBAC9B,OAAO,EAAE,KAAK,CAAC,eAAe;gBAAA,QAAA,EAE7B,GAAG,KAAK,KAAK,+NACZA,MAAAA,AAAC,EAAA,kBAAkB,EAAA;oBACjB,SAAS,EAAE,UAAU,CAAC,QAAQ;oBAC9B,KAAK,EAAE,MAAM,CAAC,QAAQ;gBAAA,EACtB,+NAEFA,MAAAA,EAAC,iBAAiB,EAAA;oBAChB,SAAS,EAAE,UAAU,CAAC,QAAQ;oBAC9B,KAAK,EAAE,MAAM,CAAC,QAAQ;gBAAA,EACtB,CACH;YAAA,CAAA,CACM,CACV;YACA,CAAC,KAAK,CAAC,QAAQ,gOACdA,MAAAA,AAAA,EAAC,MAAM,EACL;gBAAA,IAAI,EAAC,YAAY;gBAAA,YAAA,EACL,SAAS;gBACrB,SAAS,EAAE,aAAa;gBACxB,KAAK,EAAE,MAAM,CAAC,eAAe;gBAC7B,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS;gBAC1B,OAAO,EAAE,KAAK,CAAC,WAAW;gBAAA,UAEzB,GAAG,KAAK,KAAK,OACZA,8NAAAA,EAAC,iBAAiB,EAAA;oBAChB,SAAS,EAAE,UAAU,CAAC,QAAQ;oBAC9B,KAAK,EAAE,MAAM,CAAC,QAAQ;gBAAA,CAAA,CACtB,+NAEFA,MAAAA,AAAA,EAAC,kBAAkB,EACjB;oBAAA,SAAS,EAAE,UAAU,CAAC,QAAQ;oBAC9B,KAAK,EAAE,MAAM,CAAC,QAAQ;gBAAA,CACtB,CAAA,CACH;YAAA,EACM,CACV;SAAA;IAAA,CAAA,CACG,EACN;AACJ;AC9FA;;CAEG,GACG,SAAU,iBAAiB,CAAC,KAAmB,EAAA;IAC3C,IAAA,cAAc,GAAK,YAAY,EAAE,CAAA,cAAnB,CAAoB;IACpC,IAAA,EACJ,GAAA,aAAa,EAAE,EADT,aAAa,GAAA,EAAA,CAAA,aAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,aAAa,GAAA,GAAA,aACzC,CAAC;IAElB,IAAM,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,SAAC,KAAK,EAAA;QACjD,uJAAA,cAAW,AAAX,EAAY,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IAAtC,CAAsC,CACvC,CAAC;IAEF,IAAM,OAAO,GAAG,YAAY,KAAK,CAAC,CAAC;IACnC,IAAM,MAAM,GAAG,YAAY,KAAK,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IAEzD,IAAM,QAAQ,GAAG,cAAc,GAAG,CAAC,IAAA,CAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5D,IAAM,YAAY,GAAG,cAAc,GAAG,CAAC,IAAA,CAAK,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;IAEhE,IAAM,mBAAmB,GAAsB,YAAA;QAC7C,IAAI,CAAC,aAAa,EAAE,OAAO;QAC3B,SAAS,CAAC,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAM,eAAe,GAAsB,YAAA;QACzC,IAAI,CAAC,SAAS,EAAE,OAAO;QACvB,SAAS,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,mOACEA,MAAAA,AAAA,EAAC,UAAU,EAAA;QACT,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,QAAQ,EAAE,QAAQ;QAClB,YAAY,EAAE,YAAY;QAC1B,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,aAAa;QAC5B,eAAe,EAAE,mBAAmB;QACpC,WAAW,EAAE,eAAe;IAAA,CAAA,CAC5B,EACF;AACJ;ACxBA;;;CAGG,GACG,SAAU,OAAO,CAAC,KAAmB,EAAA;;IACnC,IAAA,KACJ,YAAY,EAAE,EADR,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,iBAAiB,GAAA,EAAA,CAAA,iBAAA,EAAE,MAAM,GAAA,GAAA,MAAA,EAAE,aAAa,GAAA,GAAA,aAAA,EAAE,UAAU,GAAA,GAAA,UACxD,CAAC;IAEjB,IAAM,qBAAqB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,YAAY,CAAC;IAEvE,IAAI,OAAoB,CAAC;IACzB,IAAI,iBAAiB,EAAE;QACrB,OAAO,+NACLA,MAAAA,AAAA,EAAC,qBAAqB,EAAC;YAAA,EAAE,EAAE,KAAK,CAAC,EAAE;YAAE,YAAY,EAAE,KAAK,CAAC,YAAY;QAAA,CAAA,CAAI,CAC1E,CAAC;KACH,MAAM,IAAI,aAAa,KAAK,UAAU,EAAE;QACvC,OAAO,+NACLA,MAAA,AAAAA,EAAC,gBAAgB,EAAC;YAAA,YAAY,EAAE,KAAK,CAAC,YAAY;YAAE,EAAE,EAAE,KAAK,CAAC,EAAE;QAAA,CAAA,CAAI,CACrE,CAAC;KACH,MAAM,IAAI,aAAa,KAAK,kBAAkB,EAAE;QAC/C,OAAO,+NACLC,OAAAA,AACE,0NAAAC,WAAA,EAAA;YAAA,QAAA,EAAA;4OAAAF,MAAAA,AAAA,EAAC,gBAAgB,EACf;oBAAA,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,EAAE,EAAE,KAAK,CAAC,EAAE;gBAAA,CAAA,CACZ;gBACFA,kOAAAA,AAAC,EAAA,iBAAiB,EAAA;oBAChB,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,EAAE,EAAE,KAAK,CAAC,EAAE;gBAAA,CAAA,CACZ;aACD;QAAA,CAAA,CAAA,CACJ,CAAC;KACH,MAAM;QACL,OAAO,+NACLC,OAAAA,AAAA,0NAAAC,WAAA,EAAA;YAAA,QAAA,EAAA;4OACEF,MAAAA,EAAC,qBAAqB,EAAA;oBACpB,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,YAAY,EAAE,KAAK,CAAC,YAAY;gBAAA,EAChC;gBACFA,kOAAAA,AAAA,EAAC,iBAAiB,EAAC;oBAAA,YAAY,EAAE,KAAK,CAAC,YAAY;oBAAE,EAAE,EAAE,KAAK,CAAC,EAAE;gBAAA,CAAI,CAAA;aAAA;QAAA,CAAA,CACpE,CACJ,CAAC;KACH;IAED,mOACEA,MAAAA,AAAK,EAAA,KAAA,EAAA;QAAA,SAAS,EAAE,UAAU,CAAC,OAAO;QAAE,KAAK,EAAE,MAAM,CAAC,OAAO;QAAA,UACtD,OAAO;IAAA,CAAA,CACJ,EACN;AACJ;ACtEA,mDAAA,GACA,6DAAA;AACM,SAAU,MAAM,CAAC,KAAkB,EAAA;IACjC,IAAA,EAIF,GAAA,YAAY,EAAE,EAHhB,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,MAAM,GAAA,EAAA,CAAA,MAAA,EACQ,KAAK,GAAA,EAAA,CAAA,UAAA,CAAA,KACH,CAAC;IACnB,IAAI,CAAC,MAAM,EAAE,mOAAOA,MAAAA,EAAAA,uNAAAA,CAAAA,WAAAA,EAAAA,CAAAA,EAAK,CAAC;IAC1B,mOACEA,MAAAA,EAAAA,SAAAA;QAAO,SAAS,EAAE,KAAK;QAAE,KAAK,EAAE,MAAM,CAAC,KAAK;QAAA,sOAC1CA,MAAAA,AACE,EAAA,IAAA,EAAA;YAAA,QAAA,MAAAA,8NAAAA,AAAA,EAAA,IAAA,EAAA;gBAAI,OAAO,EAAE,CAAC;gBAAA,QAAA,EAAG,MAAM;YAAA,CAAM,CAAA;QAAA,CAAA,CAC1B;IAAA,CACC,CAAA,EACR;AACJ;ACpBA;;;CAGG,GACG,SAAU,WAAW,CACzB,MAAe,EACf,yDAAA,GACA,YAAwC,EACxC,mCAAA,GACA,OAAiB,EAAA;IAEjB,IAAM,KAAK,GAAG,OAAO,sJACjB,iBAAA,AAAc,EAAC,IAAI,IAAI,EAAE,CAAC,mJAC1B,cAAA,AAAW,EAAC,IAAI,IAAI,EAAE,EAAE;QAAE,MAAM,EAAA,MAAA;QAAE,YAAY,EAAA,YAAA;IAAA,CAAE,CAAC,CAAC;IAEtD,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;QAC1B,IAAM,GAAG,+IAAG,UAAA,AAAO,EAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAChB;IACD,OAAO,IAAI,CAAC;AACd;ACnBA;;CAEG,YACa,OAAO,GAAA;IACf,IAAA,EAAA,GASF,YAAY,EAAE,EARhB,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,cAAc,GAAA,EAAA,CAAA,cAAA,EACd,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,OAAO,GAAA,EAAA,CAAA,OAAA,EACO,iBAAiB,GAAA,EAAA,CAAA,UAAA,CAAA,iBAAA,EACrB,YAAY,GAAA,EAAA,CAAA,MAAA,CAAA,YACN,CAAC;IAEnB,IAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IAE5D,OACEC,mOAAAA,AAAI,EAAA,IAAA,EAAA;QAAA,KAAK,EAAE,MAAM,CAAC,QAAQ;QAAE,SAAS,EAAE,UAAU,CAAC,QAAQ;QACvD,QAAA,EAAA;YAAA,cAAc,gOACbD,MAAAA,AAAA,EAAA,IAAA,EAAA;gBAAI,KAAK,EAAE,MAAM,CAAC,SAAS;gBAAE,SAAS,EAAE,UAAU,CAAC,SAAS;YAAA,EAAO,CACpE;YACA,QAAQ,CAAC,GAAG,CAAC,SAAC,OAAO,EAAE,CAAC,EAAA;gBAAK,mOAC5BA,MAAAA,AAAA,EAAA,IAAA,EAAA;oBAEE,KAAK,EAAC,KAAK;oBACX,SAAS,EAAE,UAAU,CAAC,SAAS;oBAC/B,KAAK,EAAE,MAAM,CAAC,SAAS;oBACX,YAAA,EAAA,YAAY,CAAC,OAAO,EAAE;wBAAE,MAAM,EAAA,MAAA;oBAAA,CAAE,CAAC;oBAAA,UAE5C,iBAAiB,CAAC,OAAO,EAAE;wBAAE,MAAM,EAAA,MAAA;oBAAA,CAAE,CAAC;gBAAA,CANlC,EAAA,CAAC,CAOH,EACN;YAAA,CAAA,CAAC;SAAA;IAAA,CAAA,CACC,EACL;AACJ;ACpCA,2BAAA,YACgB,IAAI,GAAA;;IACZ,IAAA,EAAqC,GAAA,YAAY,EAAE,EAAjD,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,UAAU,GAAA,EAAA,CAAA,UAAmB,CAAC;IAC1D,IAAM,gBAAgB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,OAAO,CAAC;IACxD,mOACEA,MAAAA,AAAO,EAAA,OAAA,EAAA;QAAA,KAAK,EAAE,MAAM,CAAC,IAAI;QAAE,SAAS,EAAE,UAAU,CAAC,IAAI;QACnD,QAAA,8NAAAA,MAAAA,AAAA,EAAC,gBAAgB,EAAG,CAAA,CAAA,CAAA;IAAA,CAAA,CACd,EACR;AACJ;ACCA,wCAAA,GACM,SAAU,UAAU,CAAC,KAAsB,EAAA;IACzC,IAAA,EAAA,GAGF,YAAY,EAAE,EAFhB,MAAM,GAAA,EAAA,CAAA,MAAA,EACQ,SAAS,GAAA,EAAA,CAAA,UAAA,CAAA,SACP,CAAC;IAEnB,mOAAOA,MAAAA,AAAG,0NAAAE,WAAA,EAAA;QAAA,QAAA,EAAA,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC;IAAA,EAAI,CAAC;AAClD;ACOA;;;;;CAKG,OACU,qBAAqB,6MAAG,gBAAA,AAAa,EAEhD,SAAS,EAAE;AAOb,+DAAA,GACM,SAAU,sBAAsB,CACpC,KAAkC,EAAA;IAElC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;QAC5C,IAAM,iBAAiB,GAA+B;YACpD,QAAQ,EAAE,SAAS;YACnB,SAAS,EAAE;gBACT,QAAQ,EAAE,EAAE;YACb,CAAA;SACF,CAAC;QACF,mOACEF,MAAAA,AAAA,EAAC,qBAAqB,CAAC,QAAQ,EAAC;YAAA,KAAK,EAAE,iBAAiB;YAAA,UACrD,KAAK,CAAC,QAAQ;QAAA,CAAA,CACgB,EACjC;KACH;IACD,mOACEA,MAAAA,AAAC,EAAA,8BAA8B,EAC7B;QAAA,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ;IAAA,CAAA,CACxB,EACF;AACJ,CAAC;AAQK,SAAU,8BAA8B,CAAC,EAGT,EAAA;QAFpC,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,QAAQ,GAAA,EAAA,CAAA,QAAA,CAAA;IAEA,IAAA,QAAQ,GAAe,YAAY,CAAA,QAA3B,EAAE,GAAG,GAAU,YAAY,CAAA,GAAtB,EAAE,GAAG,GAAK,YAAY,CAAA,GAAjB,CAAkB;IAE5C,IAAM,UAAU,GAAyB,SAAC,GAAG,EAAE,eAAe,EAAE,CAAC,EAAA;;QAC/D,CAAA,EAAA,GAAA,YAAY,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,YAAA,EAAA,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;QAEnD,IAAM,aAAa,GAAG,OAAO,CAC3B,eAAe,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,MAAM,MAAK,GAAG,CAC5D,CAAC;QACF,IAAI,aAAa,EAAE;YACjB,OAAO;SACR;QAED,IAAM,aAAa,GAAG,OAAO,CAC3B,CAAC,eAAe,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,MAAM,MAAK,GAAG,CAC7D,CAAC;QACF,IAAI,aAAa,EAAE;YACjB,OAAO;SACR;QAED,IAAM,YAAY,GAAG,QAAQ,GAAO,aAAA,CAAA,EAAA,EAAA,QAAQ,EAAE,IAAA,CAAA,GAAE,EAAE,CAAC;QAEnD,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,IAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC,SAAC,WAAW,EAAA;gBAC/C,qJAAA,YAAA,AAAS,EAAC,GAAG,EAAE,WAAW,CAAC,CAAA;YAA3B,CAA2B,CAC5B,CAAC;YACF,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/B,MAAM;YACL,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACxB;QACD,CAAA,EAAA,GAAA,YAAY,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,YAAA,EAAG,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;IAEF,IAAM,SAAS,GAA4B;QACzC,QAAQ,EAAE,EAAE;KACb,CAAC;IAEF,IAAI,QAAQ,EAAE;QACZ,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAC,GAAS,EAAA;YAChC,IAAM,aAAa,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;YACvD,IAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAC,WAAW,EAAA;gBAC3C,qJAAA,YAAS,AAAT,EAAU,WAAW,EAAE,GAAG,CAAC,CAAA;YAA3B,CAA2B,CAC5B,CAAC;YACF,OAAO,OAAO,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;KACJ;IAED,IAAM,YAAY,GAAG;QACnB,QAAQ,EAAA,QAAA;QACR,UAAU,EAAA,UAAA;QACV,SAAS,EAAA,SAAA;KACV,CAAC;IAEF,mOACEA,MAAA,AAAAA,EAAC,qBAAqB,CAAC,QAAQ,EAAA;QAAC,KAAK,EAAE,YAAY;QAAA,QAAA,EAChD,QAAQ;IAAA,CAAA,CACsB,EACjC;AACJ,CAAC;AAED;;;;CAIG,YACa,iBAAiB,GAAA;IAC/B,IAAM,OAAO,6MAAG,aAAU,AAAV,EAAW,qBAAqB,CAAC,CAAC;IAClD,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CACb,gEAAgE,CACjE,CAAC;KACH;IACD,OAAO,OAAO,CAAC;AACjB;AClJA;;;;;CAKG,GACa,SAAA,UAAU,CACxB,GAAS,EACT,KAAiB,EAAA;IAEX,IAAA,EAAA,GAAe,KAAK,IAAI,CAAA,CAAE,EAAxB,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,EAAE,GAAA,EAAA,CAAA,EAAgB,CAAC;IACjC,IAAI,IAAI,IAAI,EAAE,EAAE;QACd,kJAAI,YAAA,AAAS,EAAC,EAAE,EAAE,GAAG,CAAC,kJAAI,YAAA,AAAS,EAAC,IAAI,EAAE,GAAG,CAAC,EAAE;YAC9C,OAAO,SAAS,CAAC;SAClB;QACD,kJAAI,YAAA,AAAS,EAAC,EAAE,EAAE,GAAG,CAAC,EAAE;YACtB,OAAO;gBAAE,IAAI,EAAE,EAAE;gBAAE,EAAE,EAAE,SAAS;YAAA,CAAE,CAAC;SACpC;QACD,kJAAI,YAAA,AAAS,EAAC,IAAI,EAAE,GAAG,CAAC,EAAE;YACxB,OAAO,SAAS,CAAC;SAClB;QACD,gJAAI,UAAA,AAAO,EAAC,IAAI,EAAE,GAAG,CAAC,EAAE;YACtB,OAAO;gBAAE,IAAI,EAAE,GAAG;gBAAE,EAAE,EAAA,EAAA;YAAA,CAAE,CAAC;SAC1B;QACD,OAAO;YAAE,IAAI,EAAA,IAAA;YAAE,EAAE,EAAE,GAAG;QAAA,CAAE,CAAC;KAC1B;IACD,IAAI,EAAE,EAAE;QACN,KAAI,qJAAA,AAAO,EAAC,GAAG,EAAE,EAAE,CAAC,EAAE;YACpB,OAAO;gBAAE,IAAI,EAAE,EAAE;gBAAE,EAAE,EAAE,GAAG;YAAA,CAAE,CAAC;SAC9B;QACD,OAAO;YAAE,IAAI,EAAE,GAAG;YAAE,EAAE,EAAA,EAAA;QAAA,CAAE,CAAC;KAC1B;IACD,IAAI,IAAI,EAAE;QACR,iJAAI,WAAA,AAAQ,EAAC,GAAG,EAAE,IAAI,CAAC,EAAE;YACvB,OAAO;gBAAE,IAAI,EAAE,GAAG;gBAAE,EAAE,EAAE,IAAI;YAAA,CAAE,CAAC;SAChC;QACD,OAAO;YAAE,IAAI,EAAA,IAAA;YAAE,EAAE,EAAE,GAAG;QAAA,CAAE,CAAC;KAC1B;IACD,OAAO;QAAE,IAAI,EAAE,GAAG;QAAE,EAAE,EAAE,SAAS;IAAA,CAAE,CAAC;AACtC;ACPA;;;;;CAKG,OACU,kBAAkB,6MAAG,gBAAA,AAAa,EAE7C,SAAS,EAAE;AAOb,6DAAA,GACM,SAAU,mBAAmB,CACjC,KAA+B,EAAA;IAE/B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;QACzC,IAAM,iBAAiB,GAA4B;YACjD,QAAQ,EAAE,SAAS;YACnB,SAAS,EAAE;gBACT,WAAW,EAAE,EAAE;gBACf,SAAS,EAAE,EAAE;gBACb,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,EAAE;YACb,CAAA;SACF,CAAC;QACF,mOACEA,MAAA,AAAAA,EAAC,kBAAkB,CAAC,QAAQ,EAAC;YAAA,KAAK,EAAE,iBAAiB;YAAA,UAClD,KAAK,CAAC,QAAQ;QAAA,CAAA,CACa,EAC9B;KACH;IACD,OACEA,kOAAAA,AAAC,EAAA,2BAA2B,EAC1B;QAAA,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ;IAAA,CAAA,CACxB,EACF;AACJ,CAAC;AAQK,SAAU,2BAA2B,CAAC,EAGT,EAAA;QAFjC,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,QAAQ,GAAA,EAAA,CAAA,QAAA,CAAA;IAEA,IAAA,QAAQ,GAAK,YAAY,CAAA,QAAjB,CAAkB;IAC5B,IAAA,EAAA,GAAyC,QAAQ,IAAI,CAAA,CAAE,EAA/C,YAAY,GAAA,EAAA,CAAA,IAAA,EAAM,UAAU,GAAA,EAAA,CAAA,EAAmB,CAAC;IAC9D,IAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC;IAC7B,IAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC;IAE7B,IAAM,UAAU,GAAyB,SAAC,GAAG,EAAE,eAAe,EAAE,CAAC,EAAA;;QAC/D,CAAA,EAAA,GAAA,YAAY,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,YAAA,EAAA,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAC3C,CAAA,EAAA,GAAA,YAAY,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,YAAA,EAAG,QAAQ,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEF,IAAM,SAAS,GAAyB;QACtC,WAAW,EAAE,EAAE;QACf,SAAS,EAAE,EAAE;QACb,YAAY,EAAE,EAAE;QAChB,QAAQ,EAAE,EAAE;KACb,CAAC;IAEF,IAAI,YAAY,EAAE;QAChB,SAAS,CAAC,WAAW,GAAG;YAAC,YAAY;SAAC,CAAC;QACvC,IAAI,CAAC,UAAU,EAAE;YACf,SAAS,CAAC,SAAS,GAAG;gBAAC,YAAY;aAAC,CAAC;SACtC,MAAM;YACL,SAAS,CAAC,SAAS,GAAG;gBAAC,UAAU;aAAC,CAAC;YACnC,IAAI,KAAC,sJAAA,AAAS,EAAC,YAAY,EAAE,UAAU,CAAC,EAAE;gBACxC,SAAS,CAAC,YAAY,GAAG;oBACvB;wBACE,KAAK,EAAE,YAAY;wBACnB,MAAM,EAAE,UAAU;oBACnB,CAAA;iBACF,CAAC;aACH;SACF;KACF,MAAM,IAAI,UAAU,EAAE;QACrB,SAAS,CAAC,WAAW,GAAG;YAAC,UAAU;SAAC,CAAC;QACrC,SAAS,CAAC,SAAS,GAAG;YAAC,UAAU;SAAC,CAAC;KACpC;IAED,IAAI,GAAG,EAAE;QACP,IAAI,YAAY,IAAI,CAAC,UAAU,EAAE;YAC/B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,KAAK,8IAAE,UAAO,AAAP,EAAQ,YAAY,EAAE,GAAG,GAAG,CAAC,CAAC;gBACrC,MAAM,8IAAE,UAAA,AAAO,EAAC,YAAY,EAAE,GAAG,GAAG,CAAC,CAAC;YACvC,CAAA,CAAC,CAAC;SACJ;QACD,IAAI,YAAY,IAAI,UAAU,EAAE;YAC9B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,KAAK,EAAE,YAAY;gBACnB,MAAM,8IAAE,UAAA,AAAO,EAAC,YAAY,EAAE,GAAG,GAAG,CAAC,CAAC;YACvC,CAAA,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,YAAY,IAAI,UAAU,EAAE;YAC/B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,KAAK,8IAAE,UAAA,AAAO,EAAC,UAAU,EAAE,GAAG,GAAG,CAAC,CAAC;gBACnC,MAAM,GAAE,qJAAA,AAAO,EAAC,UAAU,EAAE,GAAG,GAAG,CAAC,CAAC;YACrC,CAAA,CAAC,CAAC;SACJ;KACF;IACD,IAAI,GAAG,EAAE;QACP,IAAI,YAAY,IAAI,CAAC,UAAU,EAAE;YAC/B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,MAAM,8IAAE,UAAA,AAAO,EAAC,YAAY,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;YACxC,CAAA,CAAC,CAAC;YACH,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,KAAK,MAAE,kJAAA,AAAO,EAAC,YAAY,EAAE,GAAG,GAAG,CAAC,CAAC;YACtC,CAAA,CAAC,CAAC;SACJ;QACD,IAAI,YAAY,IAAI,UAAU,EAAE;YAC9B,IAAM,aAAa,gKACjB,2BAAwB,AAAxB,EAAyB,UAAU,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;YACzD,IAAM,MAAM,GAAG,GAAG,GAAG,aAAa,CAAC;YACnC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,MAAM,8IAAE,UAAO,AAAP,EAAQ,YAAY,EAAE,MAAM,CAAC;YACtC,CAAA,CAAC,CAAC;YACH,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,KAAK,6IAAE,WAAA,AAAO,EAAC,UAAU,EAAE,MAAM,CAAC;YACnC,CAAA,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,YAAY,IAAI,UAAU,EAAE;YAC/B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,MAAM,8IAAE,UAAA,AAAO,EAAC,UAAU,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;YACtC,CAAA,CAAC,CAAC;YACH,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACtB,KAAK,8IAAE,UAAA,AAAO,EAAC,UAAU,EAAE,GAAG,GAAG,CAAC,CAAC;YACpC,CAAA,CAAC,CAAC;SACJ;KACF;IAED,mOACEA,MAAAA,EAAC,kBAAkB,CAAC,QAAQ,EAAC;QAAA,KAAK,EAAE;YAAE,QAAQ,EAAA,QAAA;YAAE,UAAU,EAAA,UAAA;YAAE,SAAS,EAAA,SAAA;QAAA,CAAE;QACpE,QAAA,EAAA,QAAQ;IAAA,CACmB,CAAA,EAC9B;AACJ,CAAC;AAED;;;;CAIG,YACa,cAAc,GAAA;IAC5B,IAAM,OAAO,4MAAG,cAAA,AAAU,EAAC,kBAAkB,CAAC,CAAC;IAC/C,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;KAC7E;IACD,OAAO,OAAO,CAAC;AACjB;ACjMA,wCAAA,GACM,SAAU,cAAc,CAC5B,OAAwC,EAAA;IAExC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAA,aAAA,CAAA,EAAA,EAAW,OAAO,EAAE,IAAA,CAAA,CAAA;KACrB,MAAM,IAAI,OAAO,KAAK,SAAS,EAAE;QAChC,OAAO;YAAC,OAAO;SAAC,CAAC;KAClB,MAAM;QACL,OAAO,EAAE,CAAC;KACX;AACH;ACTA,6CAAA,GACM,SAAU,kBAAkB,CAChC,YAA0B,EAAA;IAE1B,IAAM,eAAe,GAAoB,CAAA,CAAE,CAAC;IAC5C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,SAAC,EAAmB,EAAA;YAAlB,QAAQ,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,OAAO,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;QACtD,eAAe,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IACH,OAAO,eAAe,CAAC;AACzB;ACHA,qEAAA,OACY,iBAgBX;AAhBD,CAAA,SAAY,gBAAgB,EAAA;IAC1B,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;yFAEnB,gBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;yFAErB,gBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;qFAErB,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;sFAEjB,gBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;qGAEf,gBAAA,CAAA,YAAA,CAAA,GAAA,aAA0B,CAAA;mGAE1B,gBAAA,CAAA,UAAA,CAAA,GAAA,WAAsB,CAAA;8HAEtB,gBAAA,CAAA,aAAA,CAAA,GAAA,cAA4B,CAAA;AAC9B,CAAC,EAhBW,gBAAgB,IAAA,CAAhB,gBAAgB,GAgB3B,CAAA,CAAA,CAAA,CAAA;ACjBC,IAAA,QAAQ,GAQN,gBAAgB,CARV,QAAA,EACR,QAAQ,GAON,gBAAgB,CAPV,QAAA,EACR,MAAM,GAMJ,gBAAgB,CAAA,MANZ,EACN,KAAK,GAKH,gBAAgB,CAAA,KALb,EACL,QAAQ,GAIN,gBAAgB,CAAA,QAJV,EACR,WAAW,GAGT,gBAAgB,CAHP,WAAA,EACX,UAAU,GAER,gBAAgB,CAFR,UAAA,EACV,OAAO,GACL,gBAAgB,CAAA,OADX,CACY;AAErB,iFAAA,YACgB,oBAAoB,CAClC,SAAgC,EAChC,cAA0C,EAC1C,WAAoC,EAAA;;IAEpC,IAAM,iBAAiB,GAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EACrB,EAAA,CAAC,QAAQ,CAAG,GAAA,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,EAC9C,EAAA,CAAC,QAAQ,CAAG,GAAA,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,EAC9C,EAAA,CAAC,MAAM,CAAG,GAAA,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,EAC1C,EAAA,CAAC,KAAK,CAAG,GAAA;QAAC,SAAS,CAAC,KAAK;KAAC,EAC1B,EAAC,CAAA,QAAQ,CAAA,GAAG,EAAE,EACd,EAAC,CAAA,WAAW,CAAA,GAAG,EAAE,EACjB,EAAC,CAAA,UAAU,CAAA,GAAG,EAAE,EAChB,EAAC,CAAA,OAAO,CAAA,GAAG,EAAE,KACd,CAAC;IAEF,IAAI,SAAS,CAAC,QAAQ,EAAE;QACtB,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;YAAE,MAAM,EAAE,SAAS,CAAC,QAAQ;QAAA,CAAE,CAAC,CAAC;KAClE;IACD,IAAI,SAAS,CAAC,MAAM,EAAE;QACpB,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;YAAE,KAAK,EAAE,SAAS,CAAC,MAAM;QAAA,CAAE,CAAC,CAAC;KAC/D;IAED,IAAI,mBAAmB,CAAC,SAAS,CAAC,EAAE;QAClC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAC9D,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CACnC,CAAC;KACH,MAAM,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;QACtC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAC9D,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAChC,CAAC;QACF,iBAAiB,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAClE,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACpE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;KAC/D;IACD,OAAO,iBAAiB,CAAC;AAC3B;AC/CA,gIAAA,GACO,IAAM,gBAAgB,6MAAG,gBAAA,AAAa,EAAwB,SAAS,CAAC,CAAC;AAIhF,wDAAA,GACM,SAAU,iBAAiB,CAAC,KAA6B,EAAA;IAC7D,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACjC,IAAM,cAAc,GAAG,iBAAiB,EAAE,CAAC;IAC3C,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IAErC,IAAM,iBAAiB,GAAsB,oBAAoB,CAC/D,SAAS,EACT,cAAc,EACd,WAAW,CACZ,CAAC;IAEF,IAAM,eAAe,GAAoB,kBAAkB,CACzD,SAAS,CAAC,SAAS,CACpB,CAAC;IAEF,IAAM,SAAS,GACV,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,iBAAiB,CACjB,EAAA,eAAe,CACnB,CAAC;IAEF,kOACEA,OAAAA,AAAA,EAAC,gBAAgB,CAAC,QAAQ,EAAC;QAAA,KAAK,EAAE,SAAS;QAAA,UACxC,KAAK,CAAC,QAAQ;IAAA,CAAA,CACW,EAC5B;AACJ,CAAC;AAED;;;;;;CAMG,YACa,YAAY,GAAA;IAC1B,IAAM,OAAO,GAAG,uNAAA,AAAU,EAAC,gBAAgB,CAAC,CAAC;IAC7C,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACzE;IACD,OAAO,OAAO,CAAC;AACjB;ACqBA,+DAAA,GACM,SAAU,cAAc,CAAC,OAAgB,EAAA;IAC7C,OAAO,OAAO,CACZ,OAAO,IACL,OAAO,OAAO,KAAK,QAAQ,IAC3B,QAAQ,IAAI,OAAO,IACnB,OAAO,IAAI,OAAO,CACrB,CAAC;AACJ,CAAC;AAED,yDAAA,GACM,SAAU,WAAW,CAAC,KAAc,EAAA;IACxC,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;AACxE,CAAC;AAED,0DAAA,GACM,SAAU,eAAe,CAAC,KAAc,EAAA;IAC5C,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC;AACzE,CAAC;AAED,2DAAA,GACM,SAAU,gBAAgB,CAAC,KAAc,EAAA;IAC7C,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAI,KAAK,CAAC,CAAC;AAC1E,CAAC;AAED,yDAAA,GACM,SAAU,eAAe,CAAC,KAAc,EAAA;IAC5C,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,WAAW,IAAI,KAAK,CAAC,CAAC;AAC7E;ACrGA,oDAAA,GACgB,SAAA,aAAa,CAAC,IAAU,EAAE,KAAgB,EAAA;;IAClD,IAAA,IAAI,GAAS,KAAK,CAAA,IAAd,EAAE,EAAE,GAAK,KAAK,CAAA,EAAV,CAAW;IACzB,IAAI,IAAI,IAAI,EAAE,EAAE;QACd,IAAM,eAAe,gKAAG,2BAAA,AAAwB,EAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,IAAI,eAAe,EAAE;YACnB,EAAa,GAAA;gBAAC,EAAE;gBAAE,IAAI;aAAC,EAAtB,IAAI,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,EAAE,GAAA,EAAA,CAAA,CAAA,CAAA,CAAe;SACzB;QACD,IAAM,SAAS,gKACb,2BAAA,AAAwB,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,iKACzC,2BAAA,AAAwB,EAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,EAAE,EAAE;QACN,qJAAO,YAAS,AAAT,EAAU,EAAE,EAAE,IAAI,CAAC,CAAC;KAC5B;IACD,IAAI,IAAI,EAAE;QACR,qJAAO,YAAS,AAAT,EAAU,IAAI,EAAE,IAAI,CAAC,CAAC;KAC9B;IACD,OAAO,KAAK,CAAC;AACf;ACXA,4CAAA,GACA,SAAS,UAAU,CAAC,KAAc,EAAA;IAChC,kJAAO,SAAM,AAAN,EAAO,KAAK,CAAC,CAAC;AACvB,CAAC;AAED,wDAAA,GACA,SAAS,cAAc,CAAC,KAAc,EAAA;IACpC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,wIAAC,SAAM,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;;;;;;;;;;GAgBK,GACW,SAAA,OAAO,CAAC,GAAS,EAAE,QAAmB,EAAA;IACpD,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAC,OAAgB,EAAA;QACpC,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAChC,OAAO,OAAO,CAAC;SAChB;QACD,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;YACvB,qJAAO,YAAA,AAAS,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SAChC;QACD,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SAC9B;QACD,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;YACxB,OAAO,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SACpC;QACD,IAAI,eAAe,CAAC,OAAO,CAAC,EAAE;YAC5B,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;SACjD;QACD,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;YAC3B,IAAM,UAAU,gKAAG,2BAAA,AAAwB,EAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACjE,IAAM,SAAS,gKAAG,2BAAwB,AAAxB,EAAyB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC/D,IAAM,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC;YACnC,IAAM,UAAU,GAAG,SAAS,GAAG,CAAC,CAAC;YACjC,IAAM,gBAAgB,+IAAG,UAAA,AAAO,EAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAChE,IAAI,gBAAgB,EAAE;gBACpB,OAAO,UAAU,IAAI,WAAW,CAAC;aAClC,MAAM;gBACL,OAAO,WAAW,IAAI,UAAU,CAAC;aAClC;SACF;QACD,IAAI,eAAe,CAAC,OAAO,CAAC,EAAE;YAC5B,oKAAO,2BAAA,AAAwB,EAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACzD;QACD,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE;YAC7B,oKAAO,2BAAwB,AAAxB,EAAyB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;SAC1D;QACD,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;SACrB;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AACL;AC1EA,mDAAA,GACM,SAAU,kBAAkB,CAChC,GAAS,EACT,+CAAA,GACA,SAAoB,EACpB,2EAAA,GACA,YAAmB,EAAA;IAEnB,IAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CACpD,SAAC,MAAgB,EAAE,GAAW,EAAA;QAC5B,IAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;YAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClB;QACD,OAAO,MAAM,CAAC;KACf,EACD,EAAE,CACH,CAAC;IACF,IAAM,eAAe,GAAoB,CAAA,CAAE,CAAC;IAC5C,gBAAgB,CAAC,OAAO,CAAC,SAAC,QAAQ,EAAA;QAAK,OAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,IAAI,EAAjC;IAAA,CAAkC,CAAC,CAAC;IAE3E,IAAI,YAAY,IAAI,iJAAC,cAAA,AAAW,EAAC,GAAG,EAAE,YAAY,CAAC,EAAE;QACnD,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;KAChC;IAED,OAAO,eAAe,CAAC;AACzB;AC3BA;;;;;;;CAOG,GACa,SAAA,qBAAqB,CACnC,aAAqB,EACrB,SAAoB,EAAA;IAEpB,IAAM,eAAe,OAAG,4JAAA,AAAY,EAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,IAAM,cAAc,GAAG,4JAAA,AAAU,EAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;IAG3E,IAAI,iBAAiB,CAAC;IACtB,IAAI,KAAK,CAAC;IACV,IAAI,IAAI,GAAG,eAAe,CAAC;IAC3B,MAAO,IAAI,IAAI,cAAc,CAAE;QAC7B,IAAM,eAAe,GAAG,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC5D,IAAM,WAAW,GAAG,CAAC,eAAe,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,WAAW,EAAE;YAChB,IAAI,+IAAG,UAAA,AAAO,EAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACxB,SAAS;SACV;QACD,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,eAAe,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;YACnC,KAAK,GAAG,IAAI,CAAC;SACd;QACD,IAAI,CAAC,iBAAiB,EAAE;YACtB,iBAAiB,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,+IAAG,UAAA,AAAO,EAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KACzB;IACD,IAAI,KAAK,EAAE;QACT,OAAO,KAAK,CAAC;KACd,MAAM;QACL,OAAO,iBAAiB,CAAC;KAC1B;AACH;ACLA,IAAM,SAAS,GAAG,GAAG,CAAC;AAEtB,wCAAA,GACgB,SAAA,YAAY,CAAC,UAAgB,EAAE,OAAwB,EAAA;IAEnE,IAAA,MAAM,GAKJ,OAAO,CALH,MAAA,EACN,SAAS,GAIP,OAAO,CAJA,SAAA,EACT,OAAO,GAGL,OAAO,CAHF,OAAA,EACP,SAAS,GAEP,OAAO,CAFA,SAAA,EACT,KACE,OAAO,CAAA,KADoC,EAA7C,KAAK,GAAA,OAAA,KAAA,IAAG;QAAE,KAAK,EAAE,CAAC;QAAE,WAAW,EAAE,UAAU;IAAA,CAAE,GAAA,EAAA,CACnC;IACJ,IAAA,YAAY,GAA+B,OAAO,CAAA,YAAtC,EAAE,QAAQ,GAAqB,OAAO,CAAA,QAA5B,EAAE,MAAM,GAAa,OAAO,CAApB,MAAA,EAAE,MAAM,GAAK,OAAO,CAAA,MAAZ,CAAa;IAE3D,IAAM,OAAO,GAAG;QACd,GAAG,0IAAE,UAAO;QACZ,IAAI,2IAAE,WAAQ;QACd,KAAK,4IAAE,YAAS;QAChB,IAAI,2IAAE,WAAQ;QACd,WAAW,EAAE,SAAC,IAAU,EAAA;YACtB,OAAA,OAAO,CAAC,OAAO,IACX,mKAAA,AAAc,EAAC,IAAI,CAAC,mJACpB,cAAA,AAAW,EAAC,IAAI,EAAE;gBAAE,MAAM,EAAA,MAAA;gBAAE,YAAY,EAAA,YAAA;YAAA,CAAE,CAAC,CAAA;SAAA;QACjD,SAAS,EAAE,SAAC,IAAU,EAAA;YACpB,OAAA,OAAO,CAAC,OAAO,oJACX,eAAA,AAAY,EAAC,IAAI,CAAC,iJAClB,YAAA,AAAS,EAAC,IAAI,EAAE;gBAAE,MAAM,EAAA,MAAA;gBAAE,YAAY,EAAA,YAAA;YAAA,CAAE,CAAC,CAAA;SAAA;KAChD,CAAC;IAEF,IAAI,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CACjC,UAAU,EACV,SAAS,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAC/B,CAAC;IAEF,IAAI,SAAS,KAAK,QAAQ,IAAI,QAAQ,EAAE;QACtC,aAAa,2IAAG,MAAA,AAAG,EAAC;YAAC,QAAQ;YAAE,aAAa;SAAC,CAAC,CAAC;KAChD,MAAM,IAAI,SAAS,KAAK,OAAO,IAAI,MAAM,EAAE;QAC1C,aAAa,GAAG,8IAAA,AAAG,EAAC;YAAC,MAAM;YAAE,aAAa;SAAC,CAAC,CAAC;KAC9C;IACD,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,IAAI,SAAS,EAAE;QACb,IAAM,eAAe,GAAG,kBAAkB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACrE,WAAW,GAAG,CAAC,eAAe,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;KACpE;IACD,IAAI,WAAW,EAAE;QACf,OAAO,aAAa,CAAC;KACtB,MAAM;QACL,IAAI,KAAK,CAAC,KAAK,GAAG,SAAS,EAAE;YAC3B,OAAO,KAAK,CAAC,WAAW,CAAC;SAC1B;QACD,OAAO,YAAY,CAAC,aAAa,EAAE;YACjC,MAAM,EAAA,MAAA;YACN,SAAS,EAAA,SAAA;YACT,OAAO,EAAA,OAAA;YACP,SAAS,EAAA,SAAA;YACT,KAAK,EAAA,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EACA,KAAK,CAAA,EAAA;gBACR,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC;YAAA,CACvB,CAAA;QACF,CAAA,CAAC,CAAC;KACJ;AACH;ACxDA;;;;CAIG,OACU,YAAY,6MAAG,gBAAA,AAAa,EACvC,SAAS,EACT;AAIF,+CAAA,GACM,SAAU,aAAa,CAAC,KAAyB,EAAA;IACrD,IAAM,UAAU,GAAG,aAAa,EAAE,CAAC;IACnC,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IAE3B,IAAA,EAAA,6MAA8B,WAAA,AAAQ,EAAoB,GAAzD,UAAU,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,aAAa,GAAA,EAAA,CAAA,CAAA,CAAgC,CAAC;IAC3D,IAAA,EAAA,6MAAgC,WAAQ,AAAR,EAA4B,GAA3D,WAAW,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,cAAc,GAAA,EAAA,CAAA,CAAA,CAAgC,CAAC;IAEnE,IAAM,kBAAkB,GAAG,qBAAqB,CAC9C,UAAU,CAAC,aAAa,EACxB,SAAS,CACV,CAAC;;IAGF,IAAM,WAAW,GACf,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAA,KAAA,IAAV,UAAU,GAAK,WAAW,IAAI,UAAU,CAAC,eAAe,CAAC,WAAW,CAAC,AAAC,IAClE,WAAW,GACX,kBAAkB,CAAC;IAEzB,IAAM,IAAI,GAAG,YAAA;QACX,cAAc,CAAC,UAAU,CAAC,CAAC;QAC3B,aAAa,CAAC,SAAS,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,IAAM,KAAK,GAAG,SAAC,IAAU,EAAA;QACvB,aAAa,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,IAAM,OAAO,GAAG,YAAY,EAAE,CAAC;IAE/B,IAAM,SAAS,GAAG,SAAC,MAAmB,EAAE,SAA6B,EAAA;QACnE,IAAI,CAAC,UAAU,EAAE,OAAO;QACxB,IAAM,WAAW,GAAG,YAAY,CAAC,UAAU,EAAE;YAC3C,MAAM,EAAA,MAAA;YACN,SAAS,EAAA,SAAA;YACT,OAAO,EAAA,OAAA;YACP,SAAS,EAAA,SAAA;QACV,CAAA,CAAC,CAAC;QACH,iJAAI,aAAA,AAAS,EAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,SAAS,CAAC;QACzD,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAC7C,KAAK,CAAC,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,IAAM,KAAK,GAAsB;QAC/B,UAAU,EAAA,UAAA;QACV,WAAW,EAAA,WAAA;QACX,IAAI,EAAA,IAAA;QACJ,KAAK,EAAA,KAAA;QACL,aAAa,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;QAAA,CAAA;QAC9C,cAAc,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAAA,CAAA;QAChD,cAAc,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAAA,CAAA;QAChD,eAAe,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QAAA,CAAA;QAClD,gBAAgB,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAAA,CAAA;QACpD,eAAe,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAAA,CAAA;QAClD,eAAe,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QAAA,CAAA;QAClD,cAAc,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAAA,CAAA;QAChD,gBAAgB,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;QAAA,CAAA;QAC1D,cAAc,EAAE,YAAM;YAAA,OAAA,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QAAA,CAAA;KACtD,CAAC;IAEF,mOACEA,MAAAA,AAAA,EAAC,YAAY,CAAC,QAAQ,EAAC;QAAA,KAAK,EAAE,KAAK;QAAA,UAChC,KAAK,CAAC,QAAQ;IAAA,CAAA,CACO,EACxB;AACJ,CAAC;AAED;;;;;CAKG,YACa,eAAe,GAAA;IAC7B,IAAM,OAAO,4MAAG,cAAA,AAAU,EAAC,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;KACxE;IACD,OAAO,OAAO,CAAC;AACjB;ACrIA;;;;;;;CAOG,GACG,SAAU,kBAAkB,CAChC,GAAS,EACT;;;CAGG,GACH,YAAmB,EAAA;IAEnB,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACjC,IAAM,eAAe,GAAG,kBAAkB,CAAC,GAAG,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IACzE,OAAO,eAAe,CAAC;AACzB;ACRA;;;;;CAKG,OACU,mBAAmB,OAAG,sNAAA,AAAa,EAE9C,SAAS,EAAE;AAOb,8DAAA,GACM,SAAU,oBAAoB,CAClC,KAAgC,EAAA;IAEhC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;QAC1C,IAAM,iBAAiB,GAA6B;YAClD,QAAQ,EAAE,SAAS;SACpB,CAAC;QACF,mOACEA,MAAAA,AAAA,EAAC,mBAAmB,CAAC,QAAQ,EAAC;YAAA,KAAK,EAAE,iBAAiB;YAAA,UACnD,KAAK,CAAC,QAAQ;QAAA,CAAA,CACc,EAC/B;KACH;IACD,mOACEA,MAAAA,AAAC,EAAA,4BAA4B,EAC3B;QAAA,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ;IAAA,CAAA,CACxB,EACF;AACJ,CAAC;AAQK,SAAU,4BAA4B,CAAC,EAGd,EAAA;QAF7B,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,QAAQ,GAAA,EAAA,CAAA,QAAA,CAAA;IAER,IAAM,UAAU,GAAyB,SAAC,GAAG,EAAE,eAAe,EAAE,CAAC,EAAA;;QAC/D,CAAA,EAAA,GAAA,YAAY,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,YAAA,EAAA,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;QAEnD,IAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;YACtD,CAAA,EAAA,GAAA,YAAY,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,YAAA,EAAG,SAAS,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,CAAA,EAAA,GAAA,YAAY,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,YAAA,EAAG,GAAG,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC,CAAC;IAEF,IAAM,YAAY,GAA6B;QAC7C,QAAQ,EAAE,YAAY,CAAC,QAAQ;QAC/B,UAAU,EAAA,UAAA;KACX,CAAC;IACF,QACEA,iOAAAA,AAAA,EAAC,mBAAmB,CAAC,QAAQ,EAAA;QAAC,KAAK,EAAE,YAAY;QAAA,QAAA,EAC9C,QAAQ;IAAA,CAAA,CACoB,EAC/B;AACJ,CAAC;AAED;;;;CAIG,YACa,eAAe,GAAA;IAC7B,IAAM,OAAO,6MAAG,aAAA,AAAU,EAAC,mBAAmB,CAAC,CAAC;IAChD,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CACb,4DAA4D,CAC7D,CAAC;KACH;IACD,OAAO,OAAO,CAAC;AACjB;AC5CA;;;;;;;;;;;;;;;;;;;CAmBG,GACa,SAAA,mBAAmB,CACjC,IAAU,EACV,eAAgC,EAAA;IAEhC,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACjC,IAAM,MAAM,GAAG,eAAe,EAAE,CAAC;IACjC,IAAM,QAAQ,GAAG,iBAAiB,EAAE,CAAC;IACrC,IAAM,KAAK,GAAG,cAAc,EAAE,CAAC;IACzB,IAAA,KAaF,eAAe,EAAE,EAZnB,aAAa,GAAA,GAAA,aAAA,EACb,cAAc,GAAA,EAAA,CAAA,cAAA,EACd,cAAc,GAAA,EAAA,CAAA,cAAA,EACd,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,IAAI,GAAA,EAAA,CAAA,IAAA,EACJ,KAAK,GAAA,EAAA,CAAA,KAAA,EACL,gBAAgB,GAAA,EAAA,CAAA,gBAAA,EAChB,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,cAAc,GAAA,GAAA,cAAA,EACd,gBAAgB,GAAA,GAAA,gBAAA,EAChB,cAAc,GAAA,GAAA,cACK,CAAC;IAEtB,IAAM,OAAO,GAAsB,SAAC,CAAC,EAAA;;QACnC,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE;YAChC,CAAA,EAAA,GAAA,MAAM,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;SAC/C,MAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,EAAE;YACzC,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;SACjD,MAAM,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;YACtC,CAAA,EAAA,GAAA,KAAK,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;SAC9C,MAAM;YACL,CAAA,EAAA,GAAA,SAAS,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;SAClD;IACH,CAAC,CAAC;IAEF,IAAM,OAAO,GAAsB,SAAC,CAAC,EAAA;;QACnC,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,CAAA,EAAA,GAAA,SAAS,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;IAEF,IAAM,MAAM,GAAsB,SAAC,CAAC,EAAA;;QAClC,IAAI,EAAE,CAAC;QACP,CAAA,EAAA,GAAA,SAAS,CAAC,SAAS,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC;IAEF,IAAM,YAAY,GAAsB,SAAC,CAAC,EAAA;;QACxC,CAAA,EAAA,GAAA,SAAS,CAAC,eAAe,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC,CAAC;IACF,IAAM,YAAY,GAAsB,SAAC,CAAC,EAAA;;QACxC,CAAA,EAAA,GAAA,SAAS,CAAC,eAAe,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC,CAAC;IACF,IAAM,cAAc,GAAwB,SAAC,CAAC,EAAA;;QAC5C,CAAA,EAAA,GAAA,SAAS,CAAC,iBAAiB,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC;IACF,IAAM,cAAc,GAAwB,SAAC,CAAC,EAAA;;QAC5C,CAAA,EAAA,GAAA,SAAS,CAAC,iBAAiB,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC;IACF,IAAM,aAAa,GAAsB,SAAC,CAAC,EAAA;;QACzC,CAAA,EAAA,GAAA,SAAS,CAAC,gBAAgB,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;IACF,IAAM,UAAU,GAAsB,SAAC,CAAC,EAAA;;QACtC,CAAA,EAAA,GAAA,SAAS,CAAC,aAAa,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,IAAM,WAAW,GAAsB,SAAC,CAAC,EAAA;;QACvC,CAAA,EAAA,GAAA,SAAS,CAAC,cAAc,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC;IACF,IAAM,YAAY,GAAsB,SAAC,CAAC,EAAA;;QACxC,CAAA,EAAA,GAAA,SAAS,CAAC,eAAe,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC,CAAC;IAEF,IAAM,OAAO,GAAyB,SAAC,CAAC,EAAA;;QACtC,CAAA,EAAA,GAAA,SAAS,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;IAEF,IAAM,SAAS,GAAyB,SAAC,CAAC,EAAA;;QACxC,OAAQ,CAAC,CAAC,GAAG;YACX,KAAK,WAAW;gBACd,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,aAAa,EAAE,GAAG,cAAc,EAAE,CAAC;gBAC7D,MAAM;YACR,KAAK,YAAY;gBACf,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,cAAc,EAAE,GAAG,aAAa,EAAE,CAAC;gBAC7D,MAAM;YACR,KAAK,WAAW;gBACd,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,cAAc,EAAE,CAAC;gBACjB,MAAM;YACR,KAAK,SAAS;gBACZ,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,eAAe,EAAE,CAAC;gBAClB,MAAM;YACR,KAAK,QAAQ;gBACX,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,CAAC,CAAC,QAAQ,GAAG,eAAe,EAAE,GAAG,gBAAgB,EAAE,CAAC;gBACpD,MAAM;YACR,KAAK,UAAU;gBACb,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,CAAC,CAAC,QAAQ,GAAG,cAAc,EAAE,GAAG,eAAe,EAAE,CAAC;gBAClD,MAAM;YACR,KAAK,MAAM;gBACT,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,gBAAgB,EAAE,CAAC;gBACnB,MAAM;YACR,KAAK,KAAK;gBACR,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;gBACpB,cAAc,EAAE,CAAC;gBACjB,MAAM;SACT;QACD,CAAA,EAAA,GAAA,SAAS,CAAC,YAAY,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC;IAEF,IAAM,aAAa,GAAqB;QACtC,OAAO,EAAA,OAAA;QACP,OAAO,EAAA,OAAA;QACP,MAAM,EAAA,MAAA;QACN,SAAS,EAAA,SAAA;QACT,OAAO,EAAA,OAAA;QACP,YAAY,EAAA,YAAA;QACZ,YAAY,EAAA,YAAA;QACZ,cAAc,EAAA,cAAA;QACd,cAAc,EAAA,cAAA;QACd,aAAa,EAAA,aAAA;QACb,UAAU,EAAA,UAAA;QACV,WAAW,EAAA,WAAA;QACX,YAAY,EAAA,YAAA;KACb,CAAC;IAEF,OAAO,aAAa,CAAC;AACvB;ACrMA;;;;;;CAMG,YACa,eAAe,GAAA;IAC7B,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACjC,IAAM,MAAM,GAAG,eAAe,EAAE,CAAC;IACjC,IAAM,QAAQ,GAAG,iBAAiB,EAAE,CAAC;IACrC,IAAM,KAAK,GAAG,cAAc,EAAE,CAAC;IAE/B,IAAM,YAAY,GAAG,iBAAiB,CAAC,SAAS,CAAC,GAC7C,MAAM,CAAC,QAAQ,GACf,mBAAmB,CAAC,SAAS,CAAC,GAC5B,QAAQ,CAAC,QAAQ,GACjB,gBAAgB,CAAC,SAAS,CAAC,GACzB,KAAK,CAAC,QAAQ,GACd,SAAS,CAAC;IAElB,OAAO,YAAY,CAAC;AACtB;AC9BA,SAAS,kBAAkB,CAAC,QAAgB,EAAA;IAC1C,OAAO,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,QAA4B,CAAC,CAAC;AAChF,CAAC;AAED;;;;;;CAMG,GACa,SAAA,gBAAgB,CAC9B,SAA4E,EAC5E,eAAgC,EAAA;IAEhC,IAAM,UAAU,GAAa;QAAC,SAAS,CAAC,UAAU,CAAC,GAAG;KAAC,CAAC;IACxD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAC,QAAQ,EAAA;QAC5C,IAAM,eAAe,GAAG,SAAS,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,eAAe,EAAE;YACnB,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAClC,MAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACvC,IAAM,iBAAiB,GAAG,SAAS,CAAC,UAAU,CAAC,MAAO,CAAA,MAAA,CAAA,QAAQ,CAAE,CAAC,CAAC;YAClE,IAAI,iBAAiB,EAAE;gBACrB,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACpC;SACF;IACH,CAAC,CAAC,CAAC;IACH,OAAO,UAAU,CAAC;AACpB;AC1BA,mFAAA,GACgB,SAAA,WAAW,CACzB,SAAoE,EACpE,eAAgC,EAAA;IAEhC,IAAI,KAAK,GAAA,SAAA,CAAA,GACJ,SAAS,CAAC,MAAM,CAAC,GAAG,CACxB,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAC,QAAQ,EAAA;;QAC5C,KAAK,GAAA,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EACA,KAAK,CAAA,EACL,CAAA,EAAA,GAAA,SAAS,CAAC,eAAe,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,CACzC,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,OAAO,KAAK,CAAC;AACf;ACgBA;;;;;CAKG,YACa,YAAY,CAC1B,wBAAA,GACA,GAAS,EACT,wGAAA,GACA,YAAkB,EAClB,0FAAA,GACA,SAAuC,EAAA;;;IAEvC,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACjC,IAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,IAAM,eAAe,GAAG,kBAAkB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;IAC9D,IAAM,aAAa,GAAG,mBAAmB,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;IAChE,IAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,IAAM,QAAQ,GAAG,OAAO,CACtB,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CACrD,CAAC;;QAGF,kNAAA,AAAS,EAAC,YAAA;;QACR,IAAI,eAAe,CAAC,OAAO,EAAE,OAAO;QACpC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO;QACrC,IAAI,CAAC,QAAQ,EAAE,OAAO;QACtB,kJAAI,YAAA,AAAS,EAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;YAC3C,CAAA,EAAA,GAAA,SAAS,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,EAAE,CAAC;SAC5B;IACH,CAAC,EAAE;QACD,YAAY,CAAC,UAAU;QACvB,GAAG;QACH,SAAS;QACT,QAAQ;QACR,eAAe,CAAC,OAAO;KACxB,CAAC,CAAC;IAEH,IAAM,SAAS,GAAG,gBAAgB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzE,IAAM,KAAK,GAAG,WAAW,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;IACtD,IAAM,QAAQ,GAAG,OAAO,CACtB,AAAC,eAAe,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,IACpD,eAAe,CAAC,MAAM,CACzB,CAAC;IAEF,IAAM,mBAAmB,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,SAAS,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,UAAU,CAAC;IAC3E,IAAM,QAAQ,+NACZA,MAAAA,EAAC,mBAAmB,EAAA;QAClB,IAAI,EAAE,GAAG;QACT,YAAY,EAAE,YAAY;QAC1B,eAAe,EAAE,eAAe;IAAA,CAAA,CAChC,CACH,CAAC;IAEF,IAAM,QAAQ,GAAG;QACf,KAAK,EAAA,KAAA;QACL,SAAS,EAAA,SAAA;QACT,QAAQ,EAAA,QAAA;QACR,IAAI,EAAE,UAAU;KACjB,CAAC;IAEF,IAAM,aAAa,GACjB,YAAY,CAAC,WAAW,kJACxB,YAAA,AAAS,EAAC,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,IACxC,CAAC,eAAe,CAAC,OAAO,CAAC;IAE3B,IAAM,SAAS,GACb,YAAY,CAAC,UAAU,kJAAI,YAAA,AAAS,EAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAErE,IAAM,WAAW,GACZ,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,QAAQ,CACX,EAAA,CAAA,EAAA,GAAA;QAAA,QAAQ,EAAE,eAAe,CAAC,QAAQ;QAClC,IAAI,EAAE,UAAU;IAAA,CACf,EAAA,EAAA,CAAA,eAAe,CAAA,GAAG,eAAe,CAAC,QAAQ,EAC3C,GAAA,QAAQ,GAAE,SAAS,IAAI,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,EAC1C,EAAA,EAAA,EAAA,aAAa,CACjB,CAAC;IAEF,IAAM,SAAS,GAAc;QAC3B,QAAQ,EAAA,QAAA;QACR,QAAQ,EAAA,QAAA;QACR,eAAe,EAAE,eAAe;QAChC,YAAY,EAAA,YAAA;QACZ,WAAW,EAAA,WAAA;QACX,QAAQ,EAAA,QAAA;KACT,CAAC;IAEF,OAAO,SAAS,CAAC;AACnB;AC/GA;;;CAGG,GACG,SAAU,GAAG,CAAC,KAAe,EAAA;IACjC,IAAM,SAAS,6MAAG,SAAA,AAAM,EAAoB,IAAI,CAAC,CAAC;IAClD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IAE1E,IAAI,SAAS,CAAC,QAAQ,EAAE;QACtB,kOAAOA,OAAAA,AAAK,EAAA,KAAA,EAAA;YAAA,IAAI,EAAC,UAAU;QAAA,EAAO,CAAC;KACpC;IACD,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;QACvB,mOAAOA,MAAAA,AAAS,EAAA,KAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,SAAS,CAAC,QAAQ,EAAI,CAAC;KACxC;IACD,kOAAOA,OAAAA,AAAC,EAAA,MAAM,EAAC,QAAA,CAAA;QAAA,IAAI,EAAC,KAAK;QAAC,GAAG,EAAE,SAAS;IAAA,CAAM,EAAA,SAAS,CAAC,WAAW,EAAI,CAAC;AAC1E;ACbA;;;CAGG,GACG,SAAU,UAAU,CAAC,KAAsB,EAAA;IACvC,IAAQ,UAAU,GAAY,KAAK,CAAA,MAAjB,EAAE,KAAK,GAAK,KAAK,CAAA,KAAV,CAAW;IACtC,IAAA,EAAA,GAOF,YAAY,EAAE,EANhB,iBAAiB,GAAA,EAAA,CAAA,iBAAA,EACjB,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,MAAM,GAAA,EAAA,CAAA,MAAA,EACI,eAAe,GAAA,EAAA,CAAA,MAAA,CAAA,eAAA,EACX,gBAAgB,GAAA,EAAA,CAAA,UAAA,CAAA,gBACd,CAAC;IAEnB,IAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QAAE,MAAM,EAAA,MAAA;IAAA,CAAE,CAAC,CAAC;IAEjE,IAAI,CAAC,iBAAiB,EAAE;QACtB,mOACEA,MAAAA,AAAM,EAAA,MAAA,EAAA;YAAA,SAAS,EAAE,UAAU,CAAC,UAAU;YAAE,KAAK,EAAE,MAAM,CAAC,UAAU;YAAA,UAC7D,OAAO;QAAA,CAAA,CACH,EACP;KACH;IAED,IAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QAAE,MAAM,EAAA,MAAA;IAAA,CAAE,CAAC,CAAC;IAE9D,IAAM,WAAW,GAAsB,SAAU,CAAC,EAAA;QAChD,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,OACEA,kOAAAA,AAAA,EAAC,MAAM,EAAA;QACL,IAAI,EAAC,aAAa;QAAA,YAAA,EACN,KAAK;QACjB,SAAS,EAAE,UAAU,CAAC,UAAU;QAChC,KAAK,EAAE,MAAM,CAAC,UAAU;QACxB,OAAO,EAAE,WAAW;QAAA,QAAA,EAEnB,OAAO;IAAA,CAAA,CACD,EACT;AACJ;ACxCA,qEAAA,GACM,SAAU,GAAG,CAAC,KAAe,EAAA;;IAC3B,IAAA,EAAqD,GAAA,YAAY,EAAE,EAAjE,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,cAAc,GAAA,EAAA,CAAA,cAAA,EAAE,UAAU,GAAA,GAAA,UAAmB,CAAC;IAE1E,IAAM,YAAY,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,GAAG,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,GAAG,CAAC;IAC5C,IAAM,mBAAmB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,UAAU,CAAC;IAEjE,IAAI,cAAc,CAAC;IACnB,IAAI,cAAc,EAAE;QAClB,cAAc,+NACZA,MAAAA,AAAI,EAAA,IAAA,EAAA;YAAA,SAAS,EAAE,UAAU,CAAC,IAAI;YAAE,KAAK,EAAE,MAAM,CAAC,IAAI;YAChD,QAAA,EAAAA,kOAAAA,AAAA,EAAC,mBAAmB,EAAA;gBAAC,MAAM,EAAE,KAAK,CAAC,UAAU;gBAAE,KAAK,EAAE,KAAK,CAAC,KAAK;YAAA,CAAI,CAAA;QAAA,CAAA,CAClE,CACN,CAAC;KACH;IAED,mOACEC,OAAAA,AAAA,EAAA,IAAA,EAAA;QAAI,SAAS,EAAE,UAAU,CAAC,GAAG;QAAE,KAAK,EAAE,MAAM,CAAC,GAAG;QAAA,UAAA;YAC7C,cAAc;YACd,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,SAAC,IAAI,EAAA;gBAAK,WACzBD,8NAAAA,AACE,EAAA,IAAA,EAAA;oBAAA,SAAS,EAAE,UAAU,CAAC,IAAI;oBAC1B,KAAK,EAAE,MAAM,CAAC,IAAI;oBAElB,IAAI,EAAC,cAAc;oBAEnB,QAAA,8NAAAA,MAAAA,AAAA,EAAC,YAAY,EAAA;wBAAC,YAAY,EAAE,KAAK,CAAC,YAAY;wBAAE,IAAI,EAAE,IAAI;oBAAA,CAAI,CAAA;gBAAA,CAAA,kJAHzD,cAAA,AAAW,EAAC,IAAI,CAAC,CAInB,EARoB;YAAA,CAS1B,CAAC;SAAA;IAAA,CAAA,CACC,EACL;AACJ;ACnCA,yCAAA,YACgB,gBAAgB,CAC9B,QAAc,EACd,MAAY,EACZ,OAKC,EAAA;IAED,IAAM,MAAM,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,OAAO,IAC3B,gKAAA,AAAY,EAAC,MAAM,CAAC,iJACpB,YAAA,AAAS,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/B,IAAM,QAAQ,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,OAAO,uJAC7B,iBAAA,AAAc,EAAC,QAAQ,CAAC,mJACxB,cAAA,AAAW,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAEnC,IAAM,OAAO,gKAAG,2BAAA,AAAwB,EAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC3D,IAAM,IAAI,GAAW,EAAE,CAAC;IAExB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,EAAE,CAAE;QACjC,IAAI,CAAC,IAAI,6IAAC,UAAA,AAAO,EAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;KACjC;IAED,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,SAAC,MAAmB,EAAE,IAAI,EAAA;QACzD,IAAM,UAAU,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,OAAO,IAC/B,4JAAA,AAAU,EAAC,IAAI,CAAC,+IAChB,UAAO,AAAP,EAAQ,IAAI,EAAE,OAAO,CAAC,CAAC;QAE3B,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAC9B,SAAC,KAAK,EAAA;YAAK,OAAA,KAAK,CAAC,UAAU,KAAK,UAAU,CAA/B;QAAA,CAA+B,CAC3C,CAAC;QACF,IAAI,YAAY,EAAE;YAChB,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,OAAO,MAAM,CAAC;SACf;QACD,MAAM,CAAC,IAAI,CAAC;YACV,UAAU,EAAA,UAAA;YACV,KAAK,EAAE;gBAAC,IAAI;aAAC;QACd,CAAA,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;KACf,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,YAAY,CAAC;AACtB;ACzCA;;;CAGG,GACa,SAAA,aAAa,CAC3B,KAAW,EACX,OAMC,EAAA;IAED,IAAM,YAAY,GAAgB,gBAAgB,kJAChD,eAAA,AAAY,EAAC,KAAK,CAAC,iJACnB,aAAA,AAAU,EAAC,KAAK,CAAC,EACjB,OAAO,CACR,CAAC;IAEF,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,aAAa,EAAE;;QAE1B,IAAM,cAAc,uJAAG,kBAAA,AAAe,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,cAAc,GAAG,CAAC,EAAE;YACtB,IAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvD,IAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3D,IAAM,MAAM,OAAG,oJAAA,AAAQ,EAAC,QAAQ,EAAE,CAAC,GAAG,cAAc,CAAC,CAAC;YACtD,IAAM,UAAU,GAAG,gBAAgB,8IACjC,WAAA,AAAQ,EAAC,QAAQ,EAAE,CAAC,CAAC,EACrB,MAAM,EACN,OAAO,CACR,CAAC;YACF,YAAY,CAAC,IAAI,CAAA,KAAA,CAAjB,YAAY,EAAS,UAAU,CAAE,CAAA;SAClC;KACF;IACD,OAAO,YAAY,CAAC;AACtB;ACrCA,wCAAA,GACM,SAAU,KAAK,CAAC,KAAiB,EAAA;;IAC/B,IAAA,EAUF,GAAA,YAAY,EAAE,EAThB,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,qBAAqB,GAAA,EAAA,CAAA,qBAAA,EACrB,OAAO,GAAA,GAAA,OACS,CAAC;IAEnB,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,YAAY,EAAE;QAC9C,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC;QAClC,OAAO,EAAA,OAAA;QACP,MAAM,EAAA,MAAA;QACN,YAAY,EAAA,YAAA;QACZ,qBAAqB,EAAA,qBAAA;IACtB,CAAA,CAAC,CAAC;IAEH,IAAM,aAAa,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,IAAI,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC;IAC/C,IAAM,YAAY,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,GAAG,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,GAAG,CAAC;IAC5C,IAAM,eAAe,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,MAAM,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,MAAM,CAAC;IACrD,mOACEC,OAAAA,EAAAA,SAAAA;QACE,EAAE,EAAE,KAAK,CAAC,EAAE;QACZ,SAAS,EAAE,UAAU,CAAC,KAAK;QAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,IAAI,EAAC,MAAM;QAAA,iBAAA,EACM,KAAK,CAAC,iBAAiB,CAAC;QAExC,QAAA,EAAA;YAAA,CAAC,QAAQ,gOAAID,MAAAA,AAAA,EAAC,aAAa,EAAA,CAAA,CAAA,CAAG;wOAC/BA,MAAAA,AAAA,EAAA,OAAA,EAAA;gBAAO,SAAS,EAAE,UAAU,CAAC,KAAK;gBAAE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACpD,QAAA,EAAA,KAAK,CAAC,GAAG,CAAC,SAAC,IAAI,EAAA;oBAAK,mOACnBA,MAAA,AAAAA,EAAC,YAAY,EAAA;wBACX,YAAY,EAAE,KAAK,CAAC,YAAY;wBAEhC,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAAA,CAAA,EAFtB,IAAI,CAAC,UAAU,CAGpB,EACH;gBAAA,CAAA,CAAC;YAAA,CACI,CAAA;YACRA,kOAAAA,EAAC,eAAe,EAAA;gBAAC,YAAY,EAAE,KAAK,CAAC,YAAY;YAAA,CAAA,CAAI;SAC/C;IAAA,CAAA,CAAA,EACR;AACJ;AChEA;;;;;;;;;;AAUE,GAEF,+BAAA,GACA,oDAAA,GACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqDG,GAIH,SAAS,SAAS,GAAA;IAChB,OAAO,CAAC,CAAA,CACN,OAAO,MAAM,KAAK,WAAW,IAC7B,MAAM,CAAC,QAAQ,IACf,MAAM,CAAC,QAAQ,CAAC,aAAa,CAC9B,CAAC;AACJ,CAAC;AACD;;;;;;;;;;;;;;;;;;;;;;;CAuBG,GACH,IAAM,yBAAyB,GAAG,SAAS,EAAE,yMAAG,kBAAe,yMAAG,YAAS,CAAC;AAE5E,IAAI,qBAAqB,GAAG,KAAK,CAAC;AAClC,IAAI,EAAE,GAAG,CAAC,CAAC;AACX,SAAS,KAAK,GAAA;IACZ,OAAO,mBAAoB,CAAA,MAAA,CAAA,EAAE,EAAE,CAAE,CAAC;AACpC,CAAC;AAyBD,SAAS,KAAK,CAAC,UAA+C,EAAA;;;;;;IAM5D,IAAI,SAAS,GAAG,UAAU,KAAA,IAAA,IAAV,UAAU,KAAV,KAAA,CAAA,GAAA,UAAU,GAAK,qBAAqB,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IACnE,IAAA,EAAA,6MAAc,WAAA,AAAQ,EAAC,SAAS,CAAC,EAAhC,EAAE,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAuB,CAAC;IAEtC,yBAAyB,CAAC,YAAA;QACxB,IAAI,EAAE,KAAK,IAAI,EAAE;;;;;YAKf,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;SAChB;;KAEF,EAAE,EAAE,CAAC,CAAC;8MAEP,YAAS,AAAT,EAAU,YAAA;QACR,IAAI,qBAAqB,KAAK,KAAK,EAAE;;;;YAInC,qBAAqB,GAAG,IAAI,CAAC;SAC9B;KACF,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CAAA,EAAA,GAAA,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAV,UAAU,GAAI,EAAE,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,SAAS,CAAC;AACvC;ACvJA,oBAAA,GACM,SAAU,KAAK,CAAC,KAAiB,EAAA;;;IACrC,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACzB,IAAA,GAAG,GAAqC,SAAS,CAAA,GAA9C,EAAE,UAAU,GAAyB,SAAS,CAAA,UAAlC,EAAE,MAAM,GAAiB,SAAS,CAA1B,MAAA,EAAE,UAAU,GAAK,SAAS,CAAA,UAAd,CAAe;IAClD,IAAA,aAAa,GAAK,aAAa,EAAE,CAAA,aAApB,CAAqB;IAE1C,IAAM,SAAS,GAAG,KAAK,CACrB,SAAS,CAAC,EAAE,GAAG,EAAG,CAAA,MAAA,CAAA,SAAS,CAAC,EAAE,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,KAAK,CAAC,YAAY,CAAE,GAAG,SAAS,CACnE,CAAC;IAEF,IAAM,OAAO,GAAG,SAAS,CAAC,EAAE,GACxB,GAAA,MAAA,CAAG,SAAS,CAAC,EAAE,EAAS,QAAA,CAAA,CAAA,MAAA,CAAA,KAAK,CAAC,YAAY,CAAE,GAC5C,SAAS,CAAC;IAEd,IAAM,SAAS,GAAG;QAAC,UAAU,CAAC,KAAK;KAAC,CAAC;IACrC,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAEzB,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,KAAK,CAAC,CAAC;IACvC,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,KAAK,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5D,IAAM,QAAQ,GAAG,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC;IACpC,IAAI,GAAG,KAAK,KAAK,EAAE;QACjB,EAAmB,GAAA;YAAC,OAAO;YAAE,KAAK;SAAC,EAAlC,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,OAAO,GAAA,EAAA,CAAA,CAAA,CAAA,CAAqB;KACrC;IAED,IAAI,OAAO,EAAE;QACX,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QACzC,KAAK,GAAA,SAAA,SAAA,CAAA,GAAQ,KAAK,CAAA,EAAK,MAAM,CAAC,aAAa,CAAE,CAAC;KAC/C;IACD,IAAI,KAAK,EAAE;QACT,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QACvC,KAAK,GAAA,SAAA,SAAA,CAAA,GAAQ,KAAK,CAAA,EAAK,MAAM,CAAC,WAAW,CAAE,CAAC;KAC7C;IACD,IAAI,QAAQ,EAAE;QACZ,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QAC3C,KAAK,GAAA,SAAA,SAAA,CAAA,GAAQ,KAAK,CAAA,EAAK,MAAM,CAAC,eAAe,CAAE,CAAC;KACjD;IAED,IAAM,gBAAgB,GAAG,CAAA,EAAA,GAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,OAAO,CAAC;IAExD,OACEC,mOAAAA,AAA8B,EAAA,KAAA,EAAA;QAAA,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;QAAE,KAAK,EAAE,KAAK;QACxE,QAAA,EAAA;wOAAAD,MAAAA,AAAA,EAAC,gBAAgB,EAAA;gBACf,EAAE,EAAE,SAAS;gBACb,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,YAAY,EAAE,KAAK,CAAC,YAAY;YAAA,CAChC,CAAA;wOACFA,MAAAA,AAAC,EAAA,KAAK,EACJ;gBAAA,EAAE,EAAE,OAAO;gBACM,iBAAA,EAAA,SAAS;gBAC1B,YAAY,EAAE,KAAK,CAAC,YAAY;YAAA,CAAA,CAChC;SAVM;IAAA,CAAA,EAAA,KAAK,CAAC,YAAY,CAWtB,EACN;AACJ;AC1DA;;CAEG,GACG,SAAU,MAAM,CAAC,KAAkB,EAAA;IACjC,IAAA,EAAA,GAAyB,YAAY,EAAE,EAArC,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAAmB,CAAC;IAE9C,mOACEA,MAAAA,AAAK,EAAA,KAAA,EAAA;QAAA,SAAS,EAAE,UAAU,CAAC,MAAM;QAAE,KAAK,EAAE,MAAM,CAAC,MAAM;QACpD,QAAA,EAAA,KAAK,CAAC,QAAQ;IAAA,CAAA,CACX,EACN;AACJ;ACEA,uFAAA,GACM,SAAU,IAAI,CAAC,EAA2B,EAAA;;IAAzB,IAAA,YAAY,GAAA,EAAA,CAAA,YAAA,CAAA;IACjC,IAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACjC,IAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,IAAM,UAAU,GAAG,aAAa,EAAE,CAAC;IAE7B,IAAA,EAAA,6MAAwC,WAAQ,AAAR,EAAS,KAAK,CAAC,EAAtD,eAAe,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,kBAAkB,GAAA,EAAA,CAAA,CAAA,CAAmB,CAAC;;8MAG9D,YAAA,AAAS,EAAC,YAAA;QACR,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO;QACpC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO;QACtC,IAAI,eAAe,EAAE,OAAO;QAE5B,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAC7C,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,EAAE;QACD,SAAS,CAAC,YAAY;QACtB,eAAe;QACf,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,WAAW;QACxB,YAAY;KACb,CAAC,CAAC;;IAGH,IAAM,UAAU,GAAG;QAAC,SAAS,CAAC,UAAU,CAAC,IAAI;QAAE,SAAS,CAAC,SAAS;KAAC,CAAC;IACpE,IAAI,SAAS,CAAC,cAAc,GAAG,CAAC,EAAE;QAChC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;KACvD;IACD,IAAI,SAAS,CAAC,cAAc,EAAE;QAC5B,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;KACvD;IAED,IAAM,KAAK,GAAA,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EACN,SAAS,CAAC,MAAM,CAAC,IAAI,CAAA,EACrB,SAAS,CAAC,KAAK,CACnB,CAAC;IAEF,IAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAC7C,MAAM,CAAC,SAAC,GAAG,EAAA;QAAK,OAAA,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;IAAA,CAAA,CAAC,CACxC,MAAM,CAAC,SAAC,KAAK,EAAE,GAAG,EAAA;;QAEjB,OACK,QAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,KAAK,GAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CACP,GAAG,CAAA,GAAG,YAAY,CAAC,GAAG,CAAC,EACxB,EAAA,EAAA,CAAA;KACH,EAAE,CAAA,CAAE,CAAC,CAAC;IAET,IAAM,eAAe,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,YAAY,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,MAAM,CAAC;IAElE,mOACEA,MAAAA,AACE,EAAA,KAAA,EAAA,QAAA,CAAA;QAAA,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;QAC/B,KAAK,EAAE,KAAK;QACZ,GAAG,EAAE,SAAS,CAAC,GAAG;QAClB,EAAE,EAAE,SAAS,CAAC,EAAE;QAChB,KAAK,EAAE,YAAY,CAAC,KAAK;QACzB,KAAK,EAAE,YAAY,CAAC,KAAK;QACzB,IAAI,EAAE,YAAY,CAAC,IAAI;IAAA,CACnB,EAAA,cAAc,EAAA;QAAA,sOAElBA,MAAAA,AAAC,EAAA,eAAe,EAAA;YAAA,UACb,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,SAAC,KAAK,EAAE,CAAC;gBAAK,mOAC1CA,MAAAA,AAAC,EAAA,KAAK,EAAA;oBAAS,YAAY,EAAE,CAAC;oBAAE,YAAY,EAAE,KAAK;gBAAA,CAAA,EAAvC,CAAC,CAA0C,EADb;YAAA,CAE3C,CAAC;QAAA,CACc,CAAA;IAAA,CAAA,CAAA,CACd,EACN;AACJ;AC9DA,qDAAA,GACM,SAAU,YAAY,CAAC,KAAkB,EAAA;IACrC,IAAA,QAAQ,GAAsB,KAAK,CAA3B,QAAA,EAAK,YAAY,GAAA,MAAA,CAAK,KAAK,EAArC;QAA6B,UAAA;KAAA,CAAF,CAAW;IAE5C,QACEA,iOAAAA,EAAC,iBAAiB,EAAA;QAAC,YAAY,EAAE,YAAY;QAAA,sOAC3CA,MAAAA,AAAC,EAAA,kBAAkB,EAAA;YAAA,cACjBA,8NAAC,AAADA,EAAC,oBAAoB,EAAA;gBAAC,YAAY,EAAE,YAAY;gBAC9C,QAAA,8NAAAA,MAAAA,AAAA,EAAC,sBAAsB,EAAA;oBAAC,YAAY,EAAE,YAAY;oBAChD,QAAA,8NAAAA,MAAAA,AAAA,EAAC,mBAAmB,EAAC;wBAAA,YAAY,EAAE,YAAY;wBAAA,QAAA,8NAC7CA,MAAAA,AAAC,EAAA,iBAAiB,EAChB;4BAAA,QAAA,8NAAAA,MAAAA,AAAA,EAAC,aAAa,EAAE;gCAAA,QAAA,EAAA,QAAQ;4BAAA,EAAiB;wBAAA,CACvB,CAAA;oBAAA,CAAA,CACA;gBAAA,EACC;YAAA,CACJ,CAAA;QAAA,CAAA,CACJ;IAAA,CACH,CAAA,EACpB;AACJ;AC/BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsFG,GACG,SAAU,SAAS,CACvB,KAIuB,EAAA;IAEvB,mOACEA,MAAAA,AAAA,EAAC,YAAY,EAAA,QAAA,CAAA,CAAA,CAAA,EAAK,KAAK,EACrB;QAAA,QAAA,8NAAAA,MAAAA,AAAA,EAAC,IAAI,EAAA;YAAC,YAAY,EAAE,KAAK;QAAA,CAAI,CAAA;IAAA,CAAA,CAAA,CAChB,EACf;AACJ;ACjHA,aAAA,GACM,SAAU,WAAW,CAAC,GAAS,EAAA;IACnC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AAC/B;ACyEA,sEAAA,GACM,SAAU,QAAQ,CAAC,OAA6B,EAAA;IAA7B,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA;QAAA,OAA6B,GAAA,CAAA,CAAA,CAAA;IAAA,CAAA;IAElD,IAAA,KAKE,OAAO,CAAA,MALI,EAAb,MAAM,GAAA,OAAA,KAAA,uJAAG,OAAI,GAAA,EAAA,EACb,QAAQ,GAIN,OAAO,CAAA,QAJD,EACR,EAGE,GAAA,OAAO,CAAA,MAHI,EAAbG,QAAM,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,IAAI,GAAA,EAAA,EACb,eAAe,GAEb,OAAO,CAAA,eAFM,EACf,EAAA,GACE,OAAO,CAAA,KADS,EAAlB,KAAK,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,IAAI,IAAI,EAAE,GAAA,EAAA,CACR;IACN,IAAA,EAAA,GAAuB,gBAAgB,CAAC,OAAO,CAAC,EAA9C,QAAQ,GAAA,EAAA,CAAA,QAAA,EAAE,MAAM,GAAA,EAAA,CAAA,MAA8B,CAAC;;IAGvD,IAAM,UAAU,GAAG,SAAC,KAAa;QAAK,QAAA,iKAAA,AAAK,EAAC,KAAK,EAAEA,QAAM,EAAE,KAAK,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC,CAAvC;IAAA,CAAuC,CAAC;;IAGxE,IAAA,MAAoB,oNAAA,AAAQ,EAAC,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,KAAA,CAAA,GAAA,eAAe,GAAI,KAAK,CAAC,EAArD,KAAK,GAAA,EAAA,CAAA,EAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,EAAsC,CAAC;IACvD,IAAA,EAAA,IAAgC,oNAAQ,AAAR,EAAS,eAAe,CAAC,EAAxD,WAAW,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,cAAc,GAAA,EAAA,CAAA,CAAA,CAA6B,CAAC;IAChE,IAAM,iBAAiB,GAAG,eAAe,8JACrCC,SAAAA,AAAO,EAAC,eAAe,EAAED,QAAM,EAAE;QAAE,MAAM,EAAA,MAAA;IAAA,CAAE,CAAC,GAC5C,EAAE,CAAC;IACD,IAAA,EAAA,6MAA8B,WAAA,AAAQ,EAAC,iBAAiB,CAAC,EAAxD,UAAU,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,aAAa,GAAA,EAAA,CAAA,CAAA,CAA+B,CAAC;IAEhE,IAAM,KAAK,GAAG,YAAA;QACZ,cAAc,CAAC,eAAe,CAAC,CAAC;QAChC,QAAQ,CAAC,eAAe,KAAf,IAAA,IAAA,eAAe,KAAA,KAAA,IAAf,eAAe,GAAI,KAAK,CAAC,CAAC;QACnC,aAAa,CAAC,iBAAiB,KAAjB,IAAA,IAAA,iBAAiB,KAAA,KAAA,IAAjB,iBAAiB,GAAI,EAAE,CAAC,CAAC;IACzC,CAAC,CAAC;IAEF,IAAM,WAAW,GAAG,SAAC,IAAsB,EAAA;QACzC,cAAc,CAAC,IAAI,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAA,KAAA,IAAJ,IAAI,GAAI,KAAK,CAAC,CAAC;QACxB,aAAa,CAAC,IAAI,8JAAGC,SAAO,AAAPA,EAAQ,IAAI,EAAED,QAAM,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAC/D,CAAC,CAAC;IAEF,IAAM,cAAc,GAAyB,SAAC,GAAG,EAAE,EAAY,EAAA;QAAV,IAAA,QAAQ,GAAA,EAAA,CAAA,QAAA,CAAA;QAC3D,IAAI,CAAC,QAAQ,IAAI,QAAQ,EAAE;YACzB,cAAc,CAAC,SAAS,CAAC,CAAC;YAC1B,aAAa,CAAC,EAAE,CAAC,CAAC;YAClB,OAAO;SACR;QACD,cAAc,CAAC,GAAG,CAAC,CAAC;QACpB,aAAa,CAAC,GAAG,OAAGC,gKAAO,AAAPA,EAAQ,GAAG,EAAED,QAAM,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEF,IAAM,iBAAiB,GAA4B,SAAC,KAAK,EAAA;QACvD,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClB,CAAC,CAAC;;;;IAKF,IAAM,YAAY,GAAyC,SAAC,CAAC,EAAA;QAC3D,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvC,IAAM,QAAQ,GAAG,QAAQ,iKAAI,2BAAA,AAAwB,EAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QACzE,IAAM,OAAO,GAAG,MAAM,IAAI,wLAAA,AAAwB,EAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QACpE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,QAAQ,IAAI,OAAO,EAAE;YAC5C,cAAc,CAAC,SAAS,CAAC,CAAC;YAC1B,OAAO;SACR;QACD,cAAc,CAAC,GAAG,CAAC,CAAC;QACpB,QAAQ,CAAC,GAAG,CAAC,CAAC;IAChB,CAAC,CAAC;;;IAIF,IAAM,UAAU,GAAwC,SAAC,CAAC,EAAA;QACxD,IAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YACrB,KAAK,EAAE,CAAC;SACT;IACH,CAAC,CAAC;;;IAIF,IAAM,WAAW,GAAwC,SAAC,CAAC,EAAA;QACzD,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;YACnB,KAAK,EAAE,CAAC;YACR,OAAO;SACR;QACD,IAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;YACpB,QAAQ,CAAC,GAAG,CAAC,CAAC;SACf;IACH,CAAC,CAAC;IAEF,IAAM,cAAc,GAAwB;QAC1C,KAAK,EAAE,KAAK;QACZ,UAAU,EAAE,cAAc;QAC1B,aAAa,EAAE,iBAAiB;QAChC,QAAQ,EAAE,WAAW;QACrB,MAAM,EAAA,MAAA;QACN,QAAQ,EAAA,QAAA;QACR,MAAM,EAAA,MAAA;QACN,KAAK,EAAA,KAAA;KACN,CAAC;IAEF,IAAM,UAAU,GAAe;QAC7B,MAAM,EAAE,UAAU;QAClB,QAAQ,EAAE,YAAY;QACtB,OAAO,EAAE,WAAW;QACpB,KAAK,EAAE,UAAU;QACjB,WAAW,6JAAEC,SAAAA,AAAO,EAAC,IAAI,IAAI,EAAE,EAAED,QAAM,EAAE;YAAE,MAAM,EAAA,MAAA;QAAA,CAAE,CAAC;KACrD,CAAC;IAEF,OAAO;QAAE,cAAc,EAAA,cAAA;QAAE,UAAU,EAAA,UAAA;QAAE,KAAK,EAAA,KAAA;QAAE,WAAW,EAAA,WAAA;IAAA,CAAE,CAAC;AAC5D;AC7KA,2EAAA,GACM,SAAU,kBAAkB,CAChC,KAAqB,EAAA;IAErB,OAAO,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;AAC9D", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81]}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}